import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/job_seeker/profile_steps/common_widgets.dart';
import 'job_form_data.dart';

class StepJobDetails extends StatefulWidget {
  final JobFormData formData;

  const StepJobDetails({
    Key? key,
    required this.formData,
  }) : super(key: key);

  @override
  _StepJobDetailsState createState() => _StepJobDetailsState();
}

class _StepJobDetailsState extends State<StepJobDetails> {
  // Theme helpers
  bool get _isDarkMode => Theme.of(context).brightness == Brightness.dark;
  Color get _primaryColor => Theme.of(context).colorScheme.primary;
  Color get _cardColor => _isDarkMode ? Colors.grey[850]! : Colors.white;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _isDarkMode ? Colors.black.withOpacity(0.3) : Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.description, color: _primaryColor, size: 18),
              const SizedBox(width: 8),
              Text(
                'تفاصيل الوظيفة',
                style: GoogleFonts.tajawal(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          buildInputField(
            controller: widget.formData.descController,
            label: 'وصف الوظيفة',
            icon: Icons.description,
            isRequired: true,
            maxLines: 4,
            validator: (v) => v == null || v.isEmpty ? 'يرجى إدخال وصف الوظيفة' : null,
          ),
          const SizedBox(height: 12),
          buildDropdownField(
            label: 'المستوى التعليمي',
            value: widget.formData.selectedEducation,
            items: widget.formData.educationLevels,
            onChanged: (val) => setState(() => widget.formData.selectedEducation = val),
            icon: Icons.school,
            isRequired: true,
            validator: (v) => v == null ? 'يرجى اختيار المستوى التعليمي' : null,
          ),
          const SizedBox(height: 12),
          buildDropdownField(
            label: 'مستوى الخبرة',
            value: widget.formData.selectedExperience,
            items: widget.formData.experienceLevels,
            onChanged: (val) => setState(() => widget.formData.selectedExperience = val),
            icon: Icons.work_history,
            isRequired: true,
            validator: (v) => v == null ? 'يرجى اختيار مستوى الخبرة' : null,
          ),
          const SizedBox(height: 12),
          buildDropdownField(
            label: 'الجنس المطلوب',
            value: widget.formData.selectedGender,
            items: widget.formData.genders,
            onChanged: (val) => setState(() => widget.formData.selectedGender = val),
            icon: Icons.person,
            isRequired: true,
            validator: (v) => v == null ? 'يرجى اختيار الجنس المطلوب' : null,
          ),
          const SizedBox(height: 16),
          // قسم معلومات الاتصال الاختيارية
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.contact_page, color: _primaryColor, size: 16),
                    const SizedBox(width: 6),
                    Text(
                      'معلومات الاتصال (اختيارية)',
                      style: GoogleFonts.tajawal(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'يمكنك إضافة بريد إلكتروني أو رقم هاتف للتواصل المباشر',
                  style: GoogleFonts.tajawal(
                    fontSize: 10,
                    color: _primaryColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          buildInputField(
            controller: widget.formData.companyEmailController,
            label: 'البريد الإلكتروني للتواصل (اختياري)',
            icon: Icons.email,
            isRequired: false,
            keyboardType: TextInputType.emailAddress,
            validator: (v) {
              if (v != null && v.isNotEmpty && !v.contains('@')) {
                return 'يرجى إدخال بريد إلكتروني صحيح';
              }
              return null;
            },
          ),
          const SizedBox(height: 12),
          buildInputField(
            controller: widget.formData.companyPhoneController,
            label: 'رقم الهاتف للتواصل (اختياري)',
            icon: Icons.phone,
            isRequired: false,
            keyboardType: TextInputType.phone,
            validator: (v) {
              if (v != null && v.isNotEmpty && v.length < 7) {
                return 'يرجى إدخال رقم هاتف صحيح';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
} 