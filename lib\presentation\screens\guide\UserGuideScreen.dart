import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/app_state_provider.dart';

class UserGuideScreen extends StatefulWidget {
  const UserGuideScreen({Key? key}) : super(key: key);

  @override
  State<UserGuideScreen> createState() => _UserGuideScreenState();
}

class _UserGuideScreenState extends State<UserGuideScreen>
    with TickerProviderStateMixin {
  
  // Animation Controllers
  late AnimationController _headerAnimationController;
  late AnimationController _listAnimationController;
  late AnimationController _tabAnimationController;
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _headerSlideAnimation;
  late Animation<double> _listAnimation;
  late Animation<double> _tabAnimation;

  // Tab Controller
  late TabController _tabController;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);
  Color get successColor => const Color(0xff4caf50);
  Color get warningColor => const Color(0xffff9800);
  Color get errorColor => const Color(0xfff44336);

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(length: 4, vsync: this);
    _startAnimations();
  }

  void _setupAnimations() {
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _listAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _tabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _headerFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeInOut,
    ));

    _headerSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.elasticOut,
    ));

    _listAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _listAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _tabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _tabAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    _headerAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _tabAnimationController.forward();
      }
    });
    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) {
        _listAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _listAnimationController.dispose();
    _tabAnimationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = Theme.of(context).scaffoldBackgroundColor;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: Stack(
          children: [
            // خلفية متدرجة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    primaryColor.withOpacity(0.1),
                    backgroundColor,
                    backgroundColor,
                  ],
                  stops: const [0.0, 0.3, 1.0],
                ),
              ),
            ),

            Column(
              children: [
                // Header
                _buildAnimatedHeader(isDarkMode),

                // TabBar
                _buildTabBar(isDarkMode),

                // محتوى التابات
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildOverviewTab(isDarkMode),
                      _buildFeaturesTab(isDarkMode),
                      _buildTipsTab(isDarkMode),
                      _buildHelpTab(isDarkMode),
                    ],
                  ),
                ),
              ],
            ),

            // زر الرجوع العائم
            _buildFloatingBackButton(isDarkMode),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedHeader(bool isDarkMode) {
    return FadeTransition(
      opacity: _headerFadeAnimation,
      child: SlideTransition(
        position: _headerSlideAnimation,
        child: Container(
          padding: EdgeInsets.fromLTRB(16, MediaQuery.of(context).padding.top + 12, 16, 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [primaryColor, accentColor],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.help_center,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  "دليل استخدام التطبيق",
                  style: GoogleFonts.tajawal(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              _buildQuickStat(
                icon: Icons.help_outline,
                number: "شامل",
                label: "دليل",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStat({
    required IconData icon,
    required String number,
    required String label,
    required bool isDarkMode,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 16),
          const SizedBox(height: 2),
          Text(
            number,
            style: GoogleFonts.tajawal(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.tajawal(
              fontSize: 8,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(bool isDarkMode) {
    return FadeTransition(
      opacity: _tabAnimation,
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey[850] : Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          indicatorColor: primaryColor,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.tab,
          labelColor: primaryColor,
          unselectedLabelColor: Colors.grey[600],
          labelStyle: GoogleFonts.tajawal(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          unselectedLabelStyle: GoogleFonts.tajawal(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
          tabs: const [
            Tab(
              icon: Icon(Icons.home, size: 20),
              text: "نظرة عامة",
            ),
            Tab(
              icon: Icon(Icons.featured_play_list, size: 20),
              text: "المميزات",
            ),
            Tab(
              icon: Icon(Icons.lightbulb, size: 20),
              text: "نصائح",
            ),
            Tab(
              icon: Icon(Icons.help, size: 20),
              text: "مساعدة",
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab(bool isDarkMode) {
    return FadeTransition(
      opacity: _listAnimation,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionCard(
            title: "كيفية استخدام التطبيق",
            icon: Icons.play_circle_fill,
            color: primaryColor,
            isDarkMode: isDarkMode,
            children: [
              _buildStepItem(
                step: "1",
                title: "ابدأ بالبحث عن الوظائف",
                description: "استخدم شريط البحث في الصفحة الرئيسية للعثور على الوظائف المناسبة",
                icon: Icons.search,
                isDarkMode: isDarkMode,
              ),
              _buildStepItem(
                step: "2",
                title: "تصفح وفلتر النتائج",
                description: "استخدم الفلاتر المتقدمة لتضييق نطاق البحث حسب التخصص والموقع",
                icon: Icons.filter_list,
                isDarkMode: isDarkMode,
              ),
              _buildStepItem(
                step: "3",
                title: "احفظ الوظائف المهمة",
                description: "اضغط على أيقونة القلب لحفظ الوظائف في قائمة المفضلة",
                icon: Icons.favorite,
                isDarkMode: isDarkMode,
              ),
              _buildStepItem(
                step: "4",
                title: "أنشئ سيرتك الذاتية",
                description: "استخدم أداة إنشاء السيرة الذاتية المجانية بقوالب احترافية",
                icon: Icons.description,
                isDarkMode: isDarkMode,
              ),
              _buildStepItem(
                step: "5",
                title: "قدم على الوظائف",
                description: "اضغط على زر التقديم وأرسل طلبك مباشرة للشركة",
                icon: Icons.send,
                isDarkMode: isDarkMode,
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: "أقسام التطبيق الرئيسية",
            icon: Icons.apps,
            color: accentColor,
            isDarkMode: isDarkMode,
            children: [
              _buildFeatureItem(
                icon: Icons.work,
                title: "البحث عن الوظائف",
                description: "تصفح آلاف الوظائف من مختلف المجالات والشركات",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.description,
                title: "إنشاء السيرة الذاتية",
                description: "أنشئ سيرة ذاتية احترافية بقوالب عربية وإنجليزية",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.newspaper,
                title: "أخبار الوظائف",
                description: "تابع آخر الأخبار والمقالات المتعلقة بسوق العمل",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.person,
                title: "الملف الشخصي",
                description: "إدارة بياناتك الشخصية وتتبع طلبات التوظيف",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesTab(bool isDarkMode) {
    return FadeTransition(
      opacity: _listAnimation,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionCard(
            title: "مميزات البحث والتصفح",
            icon: Icons.search,
            color: primaryColor,
            isDarkMode: isDarkMode,
            children: [
              _buildFeatureItem(
                icon: Icons.search,
                title: "البحث الذكي",
                description: "ابحث بالكلمات المفتاحية، اسم الشركة، أو التخصص",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.filter_alt,
                title: "فلترة متقدمة",
                description: "رتب النتائج حسب التاريخ، الراتب، أو نوع الوظيفة",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.location_on,
                title: "البحث بالموقع",
                description: "ابحث عن الوظائف في مدينة أو دولة معينة",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.bookmark,
                title: "حفظ البحثات",
                description: "احفظ عمليات البحث المفضلة للرجوع إليها لاحقاً",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: "مميزات السيرة الذاتية",
            icon: Icons.description,
            color: successColor,
            isDarkMode: isDarkMode,
            children: [
              _buildFeatureItem(
                icon: Icons.palette,
                title: "قوالب متنوعة",
                description: "اختر من بين قوالب احترافية باللغتين العربية والإنجليزية",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.edit,
                title: "تخصيص كامل",
                description: "عدل الألوان، الخطوط، والتخطيط حسب تفضيلك",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.download,
                title: "تصدير PDF",
                description: "احفظ سيرتك الذاتية بصيغة PDF عالية الجودة",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.share,
                title: "مشاركة سهلة",
                description: "شارك سيرتك الذاتية مباشرة مع أصحاب العمل",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: "مميزات إضافية",
            icon: Icons.star,
            color: warningColor,
            isDarkMode: isDarkMode,
            children: [
              _buildFeatureItem(
                icon: Icons.notifications,
                title: "إشعارات ذكية",
                description: "استقبل إشعارات بالوظائف الجديدة التي تناسبك",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.offline_bolt,
                title: "عمل بدون إنترنت",
                description: "تصفح الوظائف المحفوظة حتى بدون اتصال",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.dark_mode,
                title: "الوضع المظلم",
                description: "تبديل بين الوضع الفاتح والمظلم لراحة العينين",
                isDarkMode: isDarkMode,
              ),
              _buildFeatureItem(
                icon: Icons.language,
                title: "دعم متعدد اللغات",
                description: "واجهة باللغة العربية مع دعم المحتوى الإنجليزي",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTipsTab(bool isDarkMode) {
    return FadeTransition(
      opacity: _listAnimation,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionCard(
            title: "نصائح للبحث الناجح",
            icon: Icons.lightbulb,
            color: warningColor,
            isDarkMode: isDarkMode,
            children: [
              _buildTipItem(
                icon: Icons.track_changes,
                title: "حدد هدفك بوضوح",
                description: "كن محدداً في البحث عن نوع الوظيفة والمجال المطلوب",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.schedule,
                title: "ابحث بانتظام",
                description: "تحقق من الوظائف الجديدة يومياً واستخدم الإشعارات",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.update,
                title: "حدث ملفك باستمرار",
                description: "اجعل سيرتك الذاتية محدثة بآخر خبراتك ومهاراتك",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.network_check,
                title: "وسع شبكة علاقاتك",
                description: "تواصل مع زملاء المهنة واحضر الفعاليات المهنية",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: "نصائح للسيرة الذاتية",
            icon: Icons.assignment,
            color: successColor,
            isDarkMode: isDarkMode,
            children: [
              _buildTipItem(
                icon: Icons.photo,
                title: "صورة شخصية احترافية",
                description: "استخدم صورة واضحة وأنيقة تناسب طبيعة العمل",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.format_list_bulleted,
                title: "نظم المعلومات بوضوح",
                description: "رتب خبراتك زمنياً من الأحدث للأقدم",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.spellcheck,
                title: "راجع الأخطاء الإملائية",
                description: "تأكد من خلو السيرة من الأخطاء اللغوية",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.compress,
                title: "اختصر واكتب بوضوح",
                description: "استخدم نقاط مختصرة وواضحة لوصف خبراتك",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: "نصائح للتقديم",
            icon: Icons.send,
            color: accentColor,
            isDarkMode: isDarkMode,
            children: [
              _buildTipItem(
                icon: Icons.business,
                title: "ابحث عن الشركة",
                description: "تعرف على الشركة وثقافتها قبل التقديم",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.message,
                title: "اكتب رسالة تغطية",
                description: "اكتب رسالة شخصية تظهر اهتمامك بالوظيفة",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.schedule_send,
                title: "قدم في الوقت المناسب",
                description: "قدم على الوظائف الجديدة بأسرع وقت ممكن",
                isDarkMode: isDarkMode,
              ),
              _buildTipItem(
                icon: Icons.follow_the_signs,
                title: "تابع طلبك",
                description: "تواصل مع الشركة بعد أسبوع من التقديم",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHelpTab(bool isDarkMode) {
    return FadeTransition(
      opacity: _listAnimation,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSectionCard(
            title: "الأسئلة الشائعة",
            icon: Icons.help_outline,
            color: primaryColor,
            isDarkMode: isDarkMode,
            children: [
              _buildFaqItem(
                question: "كيف أنشئ حساب جديد؟",
                answer: "يمكنك استخدام التطبيق دون تسجيل، أو إنشاء حساب من خلال زر 'تسجيل' في الصفحة الرئيسية",
                isDarkMode: isDarkMode,
              ),
              _buildFaqItem(
                question: "هل إنشاء السيرة الذاتية مجاني؟",
                answer: "نعم، جميع قوالب السيرة الذاتية وخدمة الإنشاء مجانية تماماً",
                isDarkMode: isDarkMode,
              ),
              _buildFaqItem(
                question: "كيف أحفظ الوظائف المفضلة؟",
                answer: "اضغط على أيقونة القلب في بطاقة الوظيفة لإضافتها للمفضلة",
                isDarkMode: isDarkMode,
              ),
              _buildFaqItem(
                question: "هل يمكنني استخدام التطبيق بدون إنترنت؟",
                answer: "يمكنك تصفح الوظائف المحفوظة وإنشاء السيرة الذاتية بدون إنترنت",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildSectionCard(
            title: "تواصل معنا",
            icon: Icons.contact_support,
            color: successColor,
            isDarkMode: isDarkMode,
            children: [
              _buildContactItem(
                icon: Icons.email,
                title: "البريد الإلكتروني",
                subtitle: "<EMAIL>",
                isDarkMode: isDarkMode,
              ),
              _buildContactItem(
                icon: Icons.chat,
                title: "الدردشة المباشرة",
                subtitle: "متاح 24/7 داخل التطبيق",
                isDarkMode: isDarkMode,
              ),
              _buildContactItem(
                icon: Icons.bug_report,
                title: "الإبلاغ عن مشكلة",
                subtitle: "استخدم نموذج الإبلاغ في الإعدادات",
                isDarkMode: isDarkMode,
              ),
            ],
          ),
          const SizedBox(height: 20),
     
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Color color,
    required bool isDarkMode,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: GoogleFonts.tajawal(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
              ],
            ),
          ),
          
          // محتوى القسم
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepItem({
    required String step,
    required String title,
    required String description,
    required IconData icon,
    required bool isDarkMode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رقم الخطوة
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [primaryColor, accentColor],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                step,
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          
          // أيقونة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: primaryColor, size: 20),
          ),
          const SizedBox(width: 16),
          
          // النص
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.grey[600],
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required bool isDarkMode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: primaryColor, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                Text(
                  description,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTipItem({
    required IconData icon,
    required String title,
    required String description,
    required bool isDarkMode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: warningColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: warningColor, size: 22),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.grey[600],
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFaqItem({
    required String question,
    required String answer,
    required bool isDarkMode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: ExpansionTile(
        title: Text(
          question,
          style: GoogleFonts.tajawal(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.grey[800],
          ),
        ),
        iconColor: primaryColor,
        collapsedIconColor: Colors.grey[600],
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              answer,
              style: GoogleFonts.tajawal(
                fontSize: 13,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isDarkMode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: successColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: successColor, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required String title,
    required String value,
    required bool isDarkMode,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: GoogleFonts.tajawal(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: GoogleFonts.tajawal(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingBackButton(bool isDarkMode) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 16,
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode 
              ? Colors.black.withOpacity(0.6) 
              : Colors.white.withOpacity(0.9),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          shape: const CircleBorder(),
          child: InkWell(
            borderRadius: BorderRadius.circular(24),
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Icon(
                Icons.arrow_forward,
                color: isDarkMode ? Colors.white : primaryColor,
                size: 20,
              ),
            ),
          ),
        ),
      ),
    );
  }
} 