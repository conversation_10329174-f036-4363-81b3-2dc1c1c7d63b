import 'dart:convert';
import 'package:http/http.dart' as Http;
import 'package:wzzff/models/NewsModel.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/main.dart';
import 'package:wzzff/core/utils/app_messages.dart';

class NewsApi {
  static String baseUrl = "https://wzzff.com/apiwzzff/";

  // جلب جميع المقالات
  Future<List<NewsModel>> getNews() async {
    try {
      String url = "${baseUrl}blogapi";
      Uri urlConvert = Uri.parse(url);
      Http.Response response = await Http.post(urlConvert);
      List apiDataList = jsonDecode(response.body);
      List<NewsModel> newsModelList = [];
      for (int i = 0; i < apiDataList.length; i++) {
        if (apiDataList[i] != null) {
          newsModelList.add(NewsModel.fromJson(apiDataList[i]));
        }
      }
      return newsModelList;
    } catch (e) {
      return [];
    }
  }

  // جلب مقال محدد بواسطة المعرف
  Future<NewsModel?> getArticleById(int articleId) async {
    try {
      // أولاً نحاول جلب جميع المقالات
      List<NewsModel> allArticles = await getNews();
      
      // ثم نبحث عن المقال المطلوب بواسطة المعرف
      for (var article in allArticles) {
        if (article.id == articleId) {
          return article;
        }
      }
      
      // إذا لم نجد المقال، نحاول جلبه مباشرة من API
      String url = "${baseUrl}blog_details";
      Uri urlConvert = Uri.parse(url);
      Http.Response response = await Http.post(urlConvert, body: {
        "id": articleId.toString(),
      });
      
      Map<String, dynamic> apiData = jsonDecode(response.body);
      
      // التحقق من وجود بيانات المقال
      if (apiData.containsKey("blog") && apiData["blog"] != null) {
        return NewsModel.fromJson(apiData["blog"]);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }
}
