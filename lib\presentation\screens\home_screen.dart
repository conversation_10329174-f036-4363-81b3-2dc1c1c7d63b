import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
//import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:list_load_more/list_load_more/list_load_more.dart';
import 'package:wzzff/presentation/screens/CountriesScreen.dart';
import 'package:wzzff/presentation/screens/auth/LoginOrRegisterScreen.dart';
import 'package:wzzff/presentation/screens/jobs_news/jobs_news.dart';
import 'package:wzzff/presentation/utils/Constants.dart' as Constants;
import 'package:wzzff/presentation/screens/SearchScreen.dart';
import 'package:wzzff/presentation/components/ListShimmer.dart';

import 'package:wzzff/presentation/components/countryImageName.dart';
import 'package:wzzff/presentation/widgets/custom_theme.dart';
import 'package:wzzff/presentation/widgets/custom_text_field.dart';
import 'package:wzzff/presentation/widgets/job_card.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/presentation/screens/jobs/LoadScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/Notifications.dart';
import 'package:wzzff/presentation/components/MyCustomCruve.dart';
import 'package:wzzff/presentation/widgets/job_card_applied.dart';
import 'package:wzzff/presentation/screens/jobs/LatestJobs.dart';
import 'package:wzzff/presentation/screens/articles/NewsPage.dart';
import 'package:wzzff/presentation/screens/CountriesScreen.dart';
import 'package:wzzff/presentation/screens/auth/LoginOrRegisterScreen.dart';
import 'package:wzzff/main.dart' show GoToAccountTabNotification;


/// شاشة التطبيق الرئيسية مع نظام التتبع الذكي الشامل
/// 
/// المميزات المضافة:
/// 🎯 تتبع التنقل بين جميع التابات (6 أقسام)
/// ⏱️ تتبع الوقت المقضي في كل قسم
/// 📊 تحليل أنماط الاستخدام وتفضيلات المستخدم
/// 🔄 تتبع تكرار زيارة كل قسم
/// 💡 فهم عميق لاهتمامات المستخدم
/// 
/// هذا النظام هو **العمود الفقري** لفهم سلوك المستخدم:
/// - يحدد الأقسام الأكثر استخداماً (وظائف، بحث، أخبار...)
/// - يكشف أنماط التصفح اليومية
/// - يساعد في تحسين ترتيب التابات حسب الاستخدام
/// - يوفر بيانات أساسية لنظام الاقتراحات الذكية

class HomeScreen extends StatefulWidget {
  final TabController tabController;
  const HomeScreen({super.key, required this.tabController});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 6,
      child: Scaffold(
        body: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(
                        Theme.of(context).brightness == Brightness.dark ? 51 : 26),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Directionality(
                    textDirection: TextDirection.rtl,
                    child: TabBar(
                      tabAlignment: TabAlignment.start,
                      controller: widget.tabController,
                      indicatorWeight: 3,
                      indicatorColor: Colors.white,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelColor: Colors.white,
                      unselectedLabelColor: Colors.white.withAlpha(179),
                      labelStyle: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      unselectedLabelStyle: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      isScrollable: true,
                      padding: const EdgeInsets.symmetric(horizontal: 0),
                      labelPadding: const EdgeInsets.symmetric(horizontal: 8),
                      indicator: const UnderlineTabIndicator(
                        borderSide: BorderSide(
                          width: 3,
                          color: Colors.white,
                        ),
                        insets: EdgeInsets.symmetric(horizontal: 8),
                      ),
                      tabs: [
                        _buildTab("الأحدث", Icons.access_time),
                        _buildTab("بحث متقدم", Icons.search),
                        _buildTab("أخبار الوظائف", Icons.newspaper),
                        _buildTab("المقالات", Icons.article),
                        _buildTab("حسب الدولة", Icons.location_on),
                        _buildTab("حسابي", Icons.person),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: widget.tabController,
                children: [
                  LatestJobs(),
                  SearchScreen(),
                  JobsNewsScreen(),
                  NewsPage(),
                  CountriesScreen(),
                  LoginOrRegisterScreen(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(String text, IconData icon) {
    return Tab(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 4),
          Text(text),
        ],
      ),
    );
  }
}


/*

      return Stack(children: [
      Container(
        height: MediaQuery.of(context).size.height,
        child: Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: TabBarView(controller: _tabController, children: [
              SearchScreen(),
              LoadJobScreen(
                paddingTop: 40,
                where: "home",
                showCity: false,
              ),
              NewsPage(),
            ])),
      ),
      Positioned(
        top: 0,
        right: 0,
        left: 0,
        child: Container(
          child: Column(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                color: Color.fromARGB(255, 6, 193, 244),
                child: TabBar(
                  //indicatorWeight: 3,
                  indicatorColor: white,
                  isScrollable: true,
                  unselectedLabelColor: Colors.white.withOpacity(0.8),
                  labelColor: Colors.white,
                  tabs: [
                    Tab(
                      //   icon: Icon(Icons.list),
                      child: Container(
                        width: MediaQuery.of(context).size.width / 4,
                        child: Center(
                            child: Text(
                          "بحث متقدم",
                          style: GoogleFonts.tajawal(),
                        )),
                      ),
                    ),
                    Tab(
                      //   icon: Icon(Icons.list),
                      child: Container(
                        width: MediaQuery.of(context).size.width / 4,
                        child: Center(
                            child: Text(
                          "الأحدث",
                          style: GoogleFonts.tajawal(),
                        )),
                      ),
                    ),
                    Tab(
                      //   icon: Icon(Icons.list),
                      child: Container(
                        width: MediaQuery.of(context).size.width / 4,
                        child: Center(
                            child: Text(
                          "المقالات",
                          style: GoogleFonts.tajawal(),
                        )),
                      ),
                    ),
                    Tab(
                      child: Container(
                        width: MediaQuery.of(context).size.width / 4,
                        child: Center(
                            child: Text(
                          "حسب الدولة",
                          style: GoogleFonts.tajawal(),
                        )),
                      ),
                    ),
                    Tab(
                      child: Container(
                        width: MediaQuery.of(context).size.width / 4,
                        child: Center(
                            child: Text(
                          "حسابي",
                          style: GoogleFonts.tajawal(),
                        )),
                      ),
                    ),
                  ],
                  controller: _tabController,
                  // indicatorSize: TabBarIndicatorSize.,
                ),
              ),
            ],
          ),
        ),
      ),
    ]);



*/
