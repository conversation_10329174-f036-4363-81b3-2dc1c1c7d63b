import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/services/unified_stats_ai_service.dart';

/// شاشة عرض الوظائف المحفوظة عالية التطابق
/// 
/// 🎯 **النظام الذكي الموحد:**
/// - يستخدم UserBehaviorTracker كمصدر موحد للبيانات
/// - يتعلم من سلوكك أثناء البحث والتصفح في التطبيق
/// - يحلل الوظائف التي تشاهدها وتتقدم لها
/// - يحفظ تلقائياً الوظائف بنسبة تطابق 80%+ مع اهتماماتك
/// - يرسل إشعارات ذكية بالوظائف الأنسب لك
/// 
/// 📊 **مصادر البيانات:**
/// - كلمات البحث المستخدمة
/// - الوظائف المشاهدة والمحفوظة
/// - الوظائف المتقدم لها
/// - الوقت المقضي في عرض كل وظيفة
/// - التفاعل مع الشركات والمجالات المختلفة

class SmartMatchedJobsScreen extends StatefulWidget {
  const SmartMatchedJobsScreen({super.key});

  @override
  State<SmartMatchedJobsScreen> createState() => _SmartMatchedJobsScreenState();
}

class _SmartMatchedJobsScreenState extends State<SmartMatchedJobsScreen> {
  @override
  Widget build(BuildContext context) {
    // قائمة محلية مؤقتة للوظائف
    final List<dynamic> matchedJobs = [];
    return Scaffold(
      appBar: AppBar(
        title: const Text('الوظائف المتطابقة ذكيًا'),
      ),
      body: Column(
        children: [
          // الهيدر أو أي عناصر تصميمية أخرى
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                FutureBuilder<int>(
                  future: UnifiedStatsAIService().getViewedJobsCount(),
                  builder: (context, snapshot) {
                    final count = snapshot.data ?? 0;
                    return _buildStatItem('المشاهدات', count.toString());
                  },
                ),
                FutureBuilder<int>(
                  future: UnifiedStatsAIService().getAppliedJobsCount(),
                  builder: (context, snapshot) {
                    final count = snapshot.data ?? 0;
                    return _buildStatItem('التقديمات', count.toString());
                  },
                ),
                FutureBuilder<int>(
                  future: UnifiedStatsAIService().getSearchesCount(),
                  builder: (context, snapshot) {
                    final count = snapshot.data ?? 0;
                    return _buildStatItem('البحث', count.toString());
                  },
                ),
                FutureBuilder<int>(
                  future: UnifiedStatsAIService().getSavedJobsCount(),
                  builder: (context, snapshot) {
                    final count = snapshot.data ?? 0;
                    return _buildStatItem('المحفوظة', count.toString());
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: matchedJobs.isEmpty
                ? Center(child: Text('لا توجد وظائف متطابقة حالياً'))
                : ListView.builder(
                    itemCount: matchedJobs.length,
                    itemBuilder: (context, index) {
                      // تصميم عنصر الوظيفة (placeholder)
                      return Card(
                        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: ListTile(
                          title: Text('وظيفة رقم ${index + 1}'),
                          subtitle: Text('تفاصيل الوظيفة ستظهر هنا'),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(value, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }
} 