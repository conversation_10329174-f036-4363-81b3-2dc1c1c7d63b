import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// secure_storage ����
// fluttertoast ����
import 'package:google_fonts/google_fonts.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:provider/provider.dart';
import '../privacy_policy/privacy_policy.dart';
import '../privacy_policy/terms_OfUsing.dart';
import '../../../Apis/LoginAndCheckAndRegi.dart';
import '../../../core/providers/app_state_provider.dart';
import '../../../core/utils/app_messages.dart';
import '../../widgets/auth_disabled_message.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class Registeration_Page extends StatefulWidget {
  final String? messsase;

  const Registeration_Page({super.key, this.messsase});

  @override
  State<Registeration_Page> createState() => _Registeration_PageState();
}

class _Registeration_PageState extends State<Registeration_Page> {
  final _formKey = GlobalKey<FormState>();

  final _nameController = TextEditingController();
  final _midNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _obscureText = true;
  bool showLoading = false;
  bool conditionUse = false;
  bool policy = false;

  String? selectedValueGender;
  String? selectedValueCountry;

  final List<String> countries = [
    'السعودية', 'مصر', 'الامارات', 'الأردن', 'البحرين',
    'الكويت', 'قطر', 'عمان', 'العراق', 'الجزائر',
    'المغرب', 'تونس', 'لبنان', 'سوريا', 'السودان',
    'ليبيا', 'فلسطين', 'اليمن',
  ];

  final List<String> genders = ["انثي", "ذكر"];

  @override
  void dispose() {
    _nameController.dispose();
    _midNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  InputDecoration _getInputDecoration({
    required String label,
    required IconData icon,
    String? hint,
    Widget? suffixIcon,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return InputDecoration(
      labelText: label,
      hintText: hint,
      labelStyle: GoogleFonts.tajawal(
        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
      ),
      prefixIcon: Icon(icon, color: primaryColor),
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: isDarkMode
          ? Theme.of(context).inputDecorationTheme.fillColor
          : Colors.grey[50],
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: primaryColor),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(
          color: isDarkMode ? Colors.red[300]! : Colors.red[400]!,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    // التحقق من حالة التسجيل
    final appStateProvider = Provider.of<AppStateProvider>(context);
    if (appStateProvider.appState != null && !appStateProvider.appState!.authControl.registrationEnabled) {
      return AuthDisabledMessage(
        title: 'التسجيل معطل',
        message: 'عذراً، التسجيل معطل حالياً. يرجى المحاولة لاحقاً.',
        onBackPressed: () => Navigator.pop(context),
      );
    }

    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.white,
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Icon(
              Icons.arrow_forward,
              color: primaryColor,
            ),
            onPressed: () => Navigator.pop(context),
            tooltip: 'رجوع',
          ),
        ],
        title: Text(
          "تسجيل باحث عن عمل",
          style: GoogleFonts.tajawal(
            color: isDarkMode
                ? Theme.of(context).textTheme.titleLarge?.color
                : const Color(0xFF1A1A1A),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isDarkMode
            ? Theme.of(context).appBarTheme.backgroundColor
            : Colors.white,
        systemOverlayStyle: isDarkMode
            ? SystemUiOverlayStyle.light
            : SystemUiOverlayStyle.dark,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(24),
            children: [
              Text(
                'معلومات شخصية',
                style: GoogleFonts.tajawal(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode
                      ? Theme.of(context).textTheme.titleLarge?.color
                      : const Color(0xFF1A1A1A),
                ),
              ),
              const SizedBox(height: 24),

              // Name Fields
              TextFormField(
                controller: _nameController,
                decoration: _getInputDecoration(
                  label: 'الاسم الأول',
                  icon: Icons.person_outline,
                  hint: 'مثال: محمد',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الرجاء إدخال الاسم الأول';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _midNameController,
                decoration: _getInputDecoration(
                  label: 'الاسم الأوسط',
                  icon: Icons.person_outline,
                  hint: 'مثال: أحمد',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الرجاء إدخال الاسم الأوسط';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _lastNameController,
                decoration: _getInputDecoration(
                  label: 'اللقب',
                  icon: Icons.person_outline,
                  hint: 'مثال: الأحمد',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الرجاء إدخال اللقب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              Text(
                'معلومات الحساب',
                style: GoogleFonts.tajawal(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode
                      ? Theme.of(context).textTheme.titleLarge?.color
                      : const Color(0xFF1A1A1A),
                ),
              ),
              const SizedBox(height: 24),

              // Email and Password Fields
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: _getInputDecoration(
                  label: 'البريد الإلكتروني',
                  icon: Icons.email_outlined,
                  hint: '<EMAIL>',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الرجاء إدخال البريد الإلكتروني';
                  }
                  if (!value.contains('@')) {
                    return 'الرجاء إدخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _passwordController,
                obscureText: _obscureText,
                decoration: _getInputDecoration(
                  label: 'كلمة المرور',
                  icon: Icons.lock_outline,
                  hint: '••••••••',
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureText ? Icons.visibility_off : Icons.visibility,
                      color: Colors.grey[600],
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureText = !_obscureText;
                      });
                    },
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الرجاء إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Dropdowns
              Container(
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Theme.of(context).inputDecorationTheme.fillColor
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton2(
                    isExpanded: true,
                    dropdownStyleData: DropdownStyleData(
                      offset: const Offset(-20, 0),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Theme.of(context).cardTheme.color
                            : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    menuItemStyleData: MenuItemStyleData(
                      padding: const EdgeInsets.only(right: 16),
                    ),
                    hint: Row(
                      children: [
                        Icon(Icons.location_on_outlined, color: primaryColor),
                        const SizedBox(width: 8),
                        Text(
                          'الدولة',
                          style: GoogleFonts.tajawal(
                            fontSize: 16,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    items: countries.map((item) {
                      return DropdownMenuItem(
                        value: item,
                        child: Container(
                          width: double.infinity,
                          alignment: Alignment.centerRight,
                          child: Text(
                            item,
                            style: GoogleFonts.tajawal(
                              fontSize: 16,
                              color: isDarkMode
                                  ? Theme.of(context).textTheme.bodyLarge?.color
                                  : const Color(0xFF1A1A1A),
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      );
                    }).toList(),
                    value: selectedValueCountry,
                    onChanged: (value) {
                      setState(() {
                        selectedValueCountry = value as String;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),

              Container(
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Theme.of(context).inputDecorationTheme.fillColor
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton2(
                    isExpanded: true,
                    dropdownStyleData: DropdownStyleData(
                      offset: const Offset(-20, 0),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Theme.of(context).cardTheme.color
                            : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    menuItemStyleData: MenuItemStyleData(
                      padding: const EdgeInsets.only(right: 16),
                    ),
                    hint: Row(
                      children: [
                        Icon(Icons.person_outline, color: primaryColor),
                        const SizedBox(width: 8),
                        Text(
                          'الجنس',
                          style: GoogleFonts.tajawal(
                            fontSize: 16,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    items: genders.map((item) {
                      return DropdownMenuItem(
                        value: item,
                        child: Container(
                          width: double.infinity,
                          alignment: Alignment.centerRight,
                          child: Text(
                            item,
                            style: GoogleFonts.tajawal(
                              fontSize: 16,
                              color: isDarkMode
                                  ? Theme.of(context).textTheme.bodyLarge?.color
                                  : const Color(0xFF1A1A1A),
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      );
                    }).toList(),
                    value: selectedValueGender,
                    onChanged: (value) {
                      setState(() {
                        selectedValueGender = value as String;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Terms and Privacy Policy
              Container(
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Theme.of(context).inputDecorationTheme.fillColor
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
                  ),
                ),
                child: CheckboxListTile(
                  value: policy,
                  onChanged: (value) {
                    setState(() {
                      policy = value!;
                    });
                  },
                  title: GestureDetector(
                    onTap: () {
                      Navigator.push(context,
                        MaterialPageRoute(builder: (context) => const privacy_policy())
                      );
                    },
                    child: Text(
                      'الموافقة على سياسة الخصوصية',
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        color: primaryColor,
                      ),
                    ),
                  ),
                  activeColor: primaryColor,
                  checkColor: Colors.white,
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ),
              const SizedBox(height: 12),

              Container(
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Theme.of(context).inputDecorationTheme.fillColor
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
                  ),
                ),
                child: CheckboxListTile(
                  value: conditionUse,
                  onChanged: (value) {
                    setState(() {
                      conditionUse = value!;
                    });
                  },
                  title: GestureDetector(
                    onTap: () {
                      Navigator.push(context,
                        MaterialPageRoute(builder: (context) => const Terms_ofUsing())
                      );
                    },
                    child: Text(
                      'الموافقة على شروط الاستخدام',
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        color: primaryColor,
                      ),
                    ),
                  ),
                  activeColor: primaryColor,
                  checkColor: Colors.white,
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ),
              const SizedBox(height: 32),

              // Register Button
              ElevatedButton(
                onPressed: showLoading ? null : _handleRegistration,
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: isDarkMode ? 2 : 0,
                ),
                child: showLoading
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'تسجيل',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  void _handleRegistration() async {
    if (!_formKey.currentState!.validate()) {
      AppMessages.showError("الرجاء إكمال جميع الحقول المطلوبة");
      return;
    }

    if (selectedValueCountry == null) {
      AppMessages.showError("الرجاء اختيار الدولة");
      return;
    }

    if (selectedValueGender == null) {
      AppMessages.showError("الرجاء اختيار الجنس");
      return;
    }

    if (!policy) {
      AppMessages.showError("الرجاء الموافقة على سياسة الخصوصية");
      return;
    }

    if (!conditionUse) {
      AppMessages.showError("الرجاء الموافقة على شروط الاستخدام");
      return;
    }

    setState(() {
      showLoading = true;
    });

    try {
      String? fcmToken = await FirebaseMessaging.instance.getToken();
   
                       await LoginAndCheckAndRegi.register(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          password: _passwordController.text.trim(),
          phone: _lastNameController.text.trim(), // أو الهاتف المناسب
          userType: 'seeker',
          fcmToken: fcmToken,
        );
      setState(() {
        showLoading = false;
      });
    } catch (error) {
      setState(() {
        showLoading = false;
      });
      AppMessages.showError("حدث خطأ أثناء التسجيل: "+error.toString());
    }
  }
}
