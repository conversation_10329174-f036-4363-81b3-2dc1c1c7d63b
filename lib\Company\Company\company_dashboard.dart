import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/Apis/CompanyApi.dart';
import 'package:wzzff/Company/Company/edit_company_data_page.dart';
import 'package:wzzff/Company/Company/job_applications_page.dart';
import 'package:wzzff/Company/Company/create_job_page.dart';
import 'package:wzzff/presentation/screens/auth/LoginOrRegisterScreen.dart';
import 'package:wzzff/core/utils/app_messages.dart';
import 'package:wzzff/main.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import '../../core/widgets/user_auth_guard.dart';
import '../../core/services/user_service.dart';

class CompanyDashboard extends StatefulWidget {
  const CompanyDashboard({Key? key}) : super(key: key);

  @override
  State<CompanyDashboard> createState() => _CompanyDashboardState();
}

class _CompanyDashboardState extends State<CompanyDashboard> {
  @override
  Widget build(BuildContext context) {
    return CompanyAuthGuard(
      child: Scaffold(
        body: Directionality(
          textDirection: TextDirection.rtl,
          child: Container(
            child: Column(
              children: [
                _buildWaveHeader(),
                Expanded(
                  child: _buildDashboardContent(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWaveHeader() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // الخلفية المتدرجة
        Container(
          width: double.infinity,
          height: 120,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                const Color(0xff2daae2),
                const Color(0xff2daae2).withOpacity(0.8),
              ],
            ),
          ),
        ),

        // الموجة البيضاء
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: ClipPath(
            clipper: WaveClipper(),
            child: Container(
              height: 40,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).scaffoldBackgroundColor
                  : Colors.white,
            ),
          ),
        ),

        // محتوى الهيدر
        Positioned(
          top: 40,
          left: 0,
          right: 0,
          child: Column(
            children: [
              Text(
                "لوحة إدارة الشركة",
                style: GoogleFonts.tajawal(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDashboardContent() {
    return Column(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).scaffoldBackgroundColor
                  : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: ListView(
              padding: const EdgeInsets.only(top: 20, bottom: 20),
              children: [
                _buildDashboardSection("إدارة الشركة", [
                  _buildDashboardMenuItem(
                    "بيانات الشركة",
                    Icons.business,
                    const Color(0xFF2daae2),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return EditCompanyDataPage();
                      }));
                    },
                  ),
                  _buildDashboardMenuItem(
                    "إنشاء وظيفة جديدة",
                    Icons.add_business,
                    const Color(0xFF4CAF50),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return CreateJobPage();
                      }));
                    },
                  ),
                ]),

                const SizedBox(height: 20),

                _buildDashboardSection("إدارة الوظائف", [
                  _buildDashboardMenuItem(
                    "الوظائف المنشورة",
                    Icons.work_outline,
                    const Color(0xFFE91E63),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return JobApplicationsPage();
                      }));
                    },
                  ),
                  _buildDashboardMenuItem(
                    "طلبات التوظيف",
                    Icons.people_outline,
                    const Color(0xFF9C27B0),
                    () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return JobApplicationsPage();
                      }));
                    },
                  ),
                ]),

                const SizedBox(height: 20),

                _buildDashboardSection("الإحصائيات والتقارير", [
                  _buildDashboardMenuItem(
                    "إحصائيات الوظائف",
                    Icons.analytics_outlined,
                    const Color(0xFFFF9800),
                    () {
                      // TODO: إضافة صفحة الإحصائيات
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('صفحة الإحصائيات قيد التطوير', style: GoogleFonts.tajawal()),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                  ),
                  _buildDashboardMenuItem(
                    "تقارير المتقدمين",
                    Icons.assignment_outlined,
                    const Color(0xFF00BCD4),
                    () {
                      // TODO: إضافة صفحة التقارير
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('صفحة التقارير قيد التطوير', style: GoogleFonts.tajawal()),
                          backgroundColor: Colors.cyan,
                        ),
                      );
                    },
                  ),
                ]),

                const SizedBox(height: 20),

                _buildDashboardSection("الإعدادات", [
                
                  
                  _buildDashboardMenuItem(
                    "تسجيل خروج",
                    Icons.logout_outlined,
                    const Color(0xFFf44336),
                    () {
                      AppMessages.showConfirmation(
                        context: context,
                        message: "هل أنت متأكد من تسجيل الخروج؟",
                        confirmText: "تسجيل خروج",
                        onConfirm: _performLogout,
                      );
                    },
                  ),
                ]),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _performLogout() async {
    try {
      // استخدام UserService للخروج الموحد (يدعم الشركات والمستخدمين)
      await UserService.logoutCurrentUser();
      
      AppMessages.showSuccess('تم تسجيل الخروج بنجاح');
      
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
          builder: (context) => MysideHaveHome(title: "Wzzff", initialTabIndex: 0),
        ),
        (route) => false,
      );
    } catch (e) {
      debugPrint('خطأ أثناء تسجيل الخروج: $e');
      AppMessages.showError('حدث خطأ أثناء تسجيل الخروج');
    }
  }

  Widget _buildDashboardSection(String title, List<Widget> items) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            title,
            style: GoogleFonts.tajawal(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Theme.of(context).colorScheme.primary : const Color(0xFF043955),
            ),
          ),
        ),
        const SizedBox(height: 10),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          decoration: BoxDecoration(
            color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildDashboardMenuItem(
      String text, IconData icon, Color iconColor, VoidCallback onTap) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.withOpacity(isDarkMode ? 0.25 : 0.15),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(isDarkMode ? 0.2 : 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 22,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Text(
                text,
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : const Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Directionality.of(context) == TextDirection.rtl
                  ? Icons.arrow_forward_ios
                  : Icons.arrow_back_ios_new,
              color: isDarkMode ? Colors.grey[400] : Colors.grey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);

    var firstControlPoint = Offset(size.width * 0.75, size.height - 30);
    var firstEndPoint = Offset(size.width * 0.5, size.height - 20);
    path.quadraticBezierTo(
      firstControlPoint.dx, firstControlPoint.dy,
      firstEndPoint.dx, firstEndPoint.dy
    );

    var secondControlPoint = Offset(size.width * 0.25, size.height - 10);
    var secondEndPoint = Offset(0, size.height - 30);
    path.quadraticBezierTo(
      secondControlPoint.dx, secondControlPoint.dy,
      secondEndPoint.dx, secondEndPoint.dy
    );

    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
} 