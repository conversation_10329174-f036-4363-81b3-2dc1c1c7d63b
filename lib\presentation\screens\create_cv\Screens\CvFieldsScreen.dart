﻿import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart' as image_picker;
import 'package:shared_preferences/shared_preferences.dart';
// طھظ… ط­ط°ظپ ط§ط³طھظٹط±ط§ط¯ ظ…ظƒطھط¨ط© ط§ظ„ط¥ط¹ظ„ط§ظ†ط§طھ
import 'package:wzzff/presentation/screens/create_cv/Api/ApiCvs.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/steps/basic_info_step.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/steps/additional_info_step.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/steps/experience_step.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/steps/education_step.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/steps/courses_step.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/steps/languages_step.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/steps/skills_step.dart';
// طھظ… ط­ط°ظپ ط§ط³طھظٹط±ط§ط¯ ط®ط¯ظ…ط© ط§ظ„ط¥ط¹ظ„ط§ظ†ط§طھ
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';

class CvFieldsScreen extends StatefulWidget {
  final int id;
  const CvFieldsScreen({super.key, required this.id});

  @override
  State<CvFieldsScreen> createState() => _CvFieldsScreenState();
}

class _CvFieldsScreenState extends State<CvFieldsScreen> 
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _sendData = false;

  // Animation Controllers
  late AnimationController _progressAnimationController;
  late AnimationController _stepAnimationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // ط§ظ„ط­ظ‚ظˆظ„ ط§ظ„ط®ط§طµط© ط¨ط§ظ„ظ†ظ…ظˆط°ط¬
  image_picker.XFile? _image;
  Map<String, dynamic> formData = {};

  List<Map<String, dynamic>> listExpAddIfClicked = [];
  List<Map<String, dynamic>> tempListExp = [];
  List<Map<String, dynamic>> listEduAddIfClicked = [];
  List<Map<String, dynamic>> listCourseAddIfClicked = [];
  List<Map<String, dynamic>> listLangIfClicked = [];
  List<Map<String, dynamic>> listSkillIfClicked = [];

  List<Map<String, dynamic>> tempListEdu = [];
  List<Map<String, dynamic>> tempListCourse = [];
  List<Map<String, dynamic>> tempListLang = [];
  List<Map<String, dynamic>> tempListSkill = [];

  // تم حذف متغيرات الإعلان لزيادة مساحة الخطوات

  late CvModel _cvData;
  bool _isDataLoaded = false;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);
  Color get successColor => const Color(0xff4caf50);

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadDataFromStorage();
    _loadCvData();
  }

  void _setupAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressAnimationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stepAnimationController,
      curve: Curves.easeIn,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _stepAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _stepAnimationController.forward();
  }

  Future<void> _loadCvData() async {
    try {
      final cvData = await ApiCvs().getFields(widget.id);
      setState(() {
        _cvData = cvData;
        _isDataLoaded = true;
      });
      _updateProgressAnimation();
    } catch (e) {
    }
  }

  void _updateProgressAnimation() {
    final totalSteps = _cvData.course_from == 1 ? 7 : 6;
    final progress = (_currentPage + 1) / totalSteps;
    _progressAnimationController.animateTo(progress);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _progressAnimationController.dispose();
    _stepAnimationController.dispose();
    // طھظ… ط­ط°ظپ ط§ظ„طھط®ظ„طµ ظ…ظ† ط§ظ„ط¥ط¹ظ„ط§ظ†
    super.dispose();
  }

  Future<void> _saveDataToStorage() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('first_name', formData['first_name'] ?? '');
    prefs.setString('last_name', formData['last_name'] ?? '');
    prefs.setString('location', formData['location'] ?? '');
    prefs.setString('job_name', formData['job_name'] ?? '');
    prefs.setString('nationality', formData['nationality'] ?? '');
    prefs.setString('phone', formData['phone'] ?? '');
    prefs.setString('email', formData['email'] ?? '');
    prefs.setString('face', formData['face'] ?? '');
    prefs.setString('twt', formData['twt'] ?? '');
    prefs.setString('linked_in', formData['linked_in'] ?? '');
    prefs.setString('behance', formData['behance'] ?? '');
    prefs.setString('summary', formData['summary'] ?? '');
    prefs.setString('edu', formData['edu'] ?? '');
  }

  Future<void> _loadDataFromStorage() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      formData = {
        'first_name': prefs.getString('first_name'),
        'last_name': prefs.getString('last_name'),
        'location': prefs.getString('location'),
        'job_name': prefs.getString('job_name'),
        'nationality': prefs.getString('nationality'),
        'phone': prefs.getString('phone'),
        'email': prefs.getString('email'),
        'face': prefs.getString('face'),
        'twt': prefs.getString('twt'),
        'linked_in': prefs.getString('linked_in'),
        'behance': prefs.getString('behance'),
        'summary': prefs.getString('summary'),
        'edu': prefs.getString('edu'),
        'age': null,
        'social_status': null,
        'exp_from': null,
        'exp_end': null,
        'exp_title': null,
        'exp_des': null,
        'exp_company_name': null,
        'edu_from': null,
        'edu_end': null,
        'edu_title': null,
        'edu_des': null,
        'edu_place_name': null,
        'edu_level': null,
        'course_from': null,
        'course_end': null,
        'course_title': null,
        'course_des': null,
        'course_place_name': null,
        'lang': null,
        'lang_level': null,
        'skill': null,
        'skill_process': null,
      };
    });
  }

  void _updateFormData(Map<String, dynamic> newData) {
    setState(() {
      formData.addAll(newData);
    });
    _saveDataToStorage();
  }

  // ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط£ظ† ط§ظ„ظ‚ظٹظ…ط© ط؛ظٹط± ظپط§ط±ط؛ط©
  bool _isEmptyValue(dynamic value) {
    if (value == null) return true;
    if (value is String && value.trim().isEmpty) return true;
    return false;
  }

  bool _validateCurrentPage() {
    switch (_currentPage) {
      case 0: // ط§ظ„ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط£ط³ط§ط³ظٹط©
        // ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ظˆط¬ظˆط¯ ط§ظ„طµظˆط±ط© (ظ…ط·ظ„ظˆط¨ط© ط¯ط§ط¦ظ…ظ‹ط§)
        if (_image == null) {
          _showErrorToast(Lang().getWord("profile_picture_required", _cvData.ltf));
          _showImageRequiredDialog();
          return false;
        }

        if (_isEmptyValue(formData['first_name']) && _cvData.first_last_name == 1) {
          _showErrorToast("${Lang().getWord("first_name", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['last_name']) && _cvData.first_last_name == 1) {
          _showErrorToast("${Lang().getWord("last_name", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['location']) && _cvData.location == 1) {
          _showErrorToast("${Lang().getWord("location", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['job_name']) && _cvData.job_name == 1) {
          _showErrorToast("${Lang().getWord("job_name", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['nationality']) && _cvData.nationality == 1) {
          _showErrorToast("${Lang().getWord("nationality", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['phone']) && _cvData.phone == 1) {
          _showErrorToast("${Lang().getWord("phone", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['email']) && _cvData.email == 1) {
          _showErrorToast("${Lang().getWord("email", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        return true;

      case 1: // ط§ظ„ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط¥ط¶ط§ظپظٹط©
        if (_isEmptyValue(formData['age']) && _cvData.age == 1) {
          _showErrorToast("${Lang().getWord("age", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['social_status']) && _cvData.social_status == 1) {
          _showErrorToast("${Lang().getWord("social_status", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['face']) && _cvData.face == 1) {
          _showErrorToast("${Lang().getWord("face", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['twt']) && _cvData.twt == 1) {
          _showErrorToast("${Lang().getWord("twt", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['linked_in']) && _cvData.linked_in == 1) {
          _showErrorToast("${Lang().getWord("linked_in", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['behance']) && _cvData.behance == 1) {
          _showErrorToast("${Lang().getWord("behance", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        if (_isEmptyValue(formData['summary']) && _cvData.summary == 1) {
          _showErrorToast("${Lang().getWord("summary", _cvData.ltf)} ${Lang().getWord("fill_all_fields", _cvData.ltf)}");
          return false;
        }

        return true;

      case 2: // ط§ظ„ط®ط¨ط±ط§طھ
        // ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ظˆط¬ظˆط¯ ط®ط¨ط±ط© ظˆط§ط­ط¯ط© ط¹ظ„ظ‰ ط§ظ„ط£ظ‚ظ„ ط¥ط°ط§ ظƒط§ظ†طھ ظ…ط·ظ„ظˆط¨ط© ظپظ‚ط· ط¹ظ†ط¯ ط§ظ„ط§ظ†طھظ‡ط§ط، ظ…ظ† ط§ظ„ط®ط·ظˆط©
        // ظ‡ظ†ط§ ظ†طھط­ظ‚ظ‚ ظپظ‚ط· ظ…ظ† ط§ظ„ط­ظ‚ظˆظ„ ط§ظ„ظ…ظ…ظ„ظˆط،ط© ظپظٹ ط§ظ„ظ†ظ…ظˆط°ط¬ ط§ظ„ط­ط§ظ„ظٹ
        return true;

      case 3: // ط§ظ„طھط¹ظ„ظٹظ…
        // ظ†ظپط³ ط§ظ„ظ…ظ†ط·ظ‚ - ظ†طھط­ظ‚ظ‚ ظپظ‚ط· ظ…ظ† ط§ظ„ط­ظ‚ظˆظ„ ط§ظ„ط­ط§ظ„ظٹط©
        return true;

      case 4: // ط§ظ„ط¯ظˆط±ط§طھ (ط¥ط°ط§ ظƒط§ظ†طھ ظ…ط·ظ„ظˆط¨ط©)
        return true;

      case 5: // ط§ظ„ظ„ط؛ط§طھ
        return true;

      case 6: // ط§ظ„ظ…ظ‡ط§ط±ط§طھ
        return true;

      default:
        return true;
    }
  }

  void _showErrorToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // ط¹ط±ط¶ ط­ظˆط§ط± طھظ†ط¨ظٹظ‡ ط§ظ„طµظˆط±ط© ط§ظ„ظ…ط·ظ„ظˆط¨ط©
  void _showImageRequiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(14),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.photo_camera,
                  color: Colors.orange.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  Lang().getWord("profile_picture_required", _cvData.ltf),
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            Lang().getWord("please_add_profile_picture", _cvData.ltf),
            style: GoogleFonts.tajawal(fontSize: 12),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                Lang().getWord("ok", _cvData.ltf),
                style: GoogleFonts.tajawal(
                  color: primaryColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // ط¯ط§ظ„ط© ط§ظ†طھظ‚ط§ظ„ ظ…ط­ط³ظ†ط© ظ…ط¹ animation
  void _nextPage() async {
    if (!_validateCurrentPage()) return;
    
    final totalSteps = _cvData.course_from == 1 ? 7 : 6;
    if (_currentPage < totalSteps - 1) {
      // Reset and restart animation for next step
      _stepAnimationController.reset();
      
      setState(() {
        _currentPage++;
      });
      
      _updateProgressAnimation();
      _stepAnimationController.forward();
      
      _pageController.nextPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() async {
    if (_currentPage > 0) {
      _stepAnimationController.reset();
      
      setState(() {
        _currentPage--;
      });
      
      _updateProgressAnimation();
      _stepAnimationController.forward();
      
      _pageController.previousPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    }
  }

  void _submitData() {
    // ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ظˆط¬ظˆط¯ ط§ظ„ط®ط¨ط±ط§طھ ظˆط§ظ„ظ…ظ‡ط§ط±ط§طھ ظˆط§ظ„ظ„ط؛ط§طھ ظˆط§ظ„ط´ظ‡ط§ط¯ط§طھ
    if (listExpAddIfClicked.isEmpty && _cvData.exp_from == 1) {
      _showErrorToast(Lang().getWord("add_at_least_one_exp", _cvData.ltf));
      _pageController.animateToPage(
        2, // طµظپط­ط© ط§ظ„ط®ط¨ط±ط§طھ
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    if (listEduAddIfClicked.isEmpty && _cvData.edu_from == 1) {
      _showErrorToast(Lang().getWord("add_at_least_one_edu", _cvData.ltf));
      _pageController.animateToPage(
        3, // طµظپط­ط© ط§ظ„طھط¹ظ„ظٹظ…
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    if (listCourseAddIfClicked.isEmpty && _cvData.course_from == 1) {
      _showErrorToast(Lang().getWord("add_at_least_one_course", _cvData.ltf));
      _pageController.animateToPage(
        4, // طµظپط­ط© ط§ظ„ط¯ظˆط±ط§طھ
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    if (listLangIfClicked.isEmpty && _cvData.lang == 1) {
      _showErrorToast(Lang().getWord("add_at_least_one_lang", _cvData.ltf));
      _pageController.animateToPage(
        _cvData.course_from == 1 ? 5 : 4, // طµظپط­ط© ط§ظ„ظ„ط؛ط§طھ
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    if (listSkillIfClicked.isEmpty && _cvData.skill == 1) {
      _showErrorToast(Lang().getWord("add_at_least_one_skill", _cvData.ltf));
      _pageController.animateToPage(
        _cvData.course_from == 1 ? 6 : 5, // طµظپط­ط© ط§ظ„ظ…ظ‡ط§ط±ط§طھ
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    // ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط¬ظ…ظٹط¹ ط§ظ„ط®ط·ظˆط§طھ ط§ظ„ط³ط§ط¨ظ‚ط©
    for (int i = 0; i < (_cvData.course_from == 1 ? 7 : 6); i++) {
      _currentPage = i;
      if (!_validateCurrentPage()) {
        // ط¥ط°ط§ ظƒط§ظ† ظ‡ظ†ط§ظƒ ط®ط·ط£ ظپظٹ ط£ظٹ ط®ط·ظˆط©طŒ ظ†ط¹ظˆط¯ ط¥ظ„ظ‰ طھظ„ظƒ ط§ظ„ط®ط·ظˆط©
        _pageController.animateToPage(
          i,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        return;
      }
    }

    // ط¥ط¹ط§ط¯ط© ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ظˆط¬ظˆط¯ ط§ظ„طµظˆط±ط© (ظ…ط·ظ„ظˆط¨ط© ط¯ط§ط¦ظ…ظ‹ط§)
    if (_image == null) {
      _showErrorToast(Lang().getWord("profile_picture_required", _cvData.ltf));
      _showImageRequiredDialog();

      // ط§ظ„ط¹ظˆط¯ط© ط¥ظ„ظ‰ ط§ظ„ط®ط·ظˆط© ط§ظ„ط£ظˆظ„ظ‰
      _pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }

    setState(() {
      _sendData = true; // ط¥ط¸ظ‡ط§ط± ظ…ط¤ط´ط± ط§ظ„طھط­ظ…ظٹظ„
    });

    // ط¥ط¹ط¯ط§ط¯ ط¨ظٹط§ظ†ط§طھ ط§ظ„ط®ط¨ط±ط©
    String? expFrom;
    String? expEnd;
    String? expTitle;
    String? expDes;
    String? expCompanyName;

    // ط§ط³طھط®ط±ط§ط¬ ط¨ظٹط§ظ†ط§طھ ط§ظ„ط®ط¨ط±ط© ظ…ظ† ط£ظˆظ„ ط¹ظ†طµط± ظپظٹ ط§ظ„ظ‚ط§ط¦ظ…ط© ط¥ط°ط§ ظƒط§ظ†طھ ظ…ظˆط¬ظˆط¯ط©
    if (listExpAddIfClicked.isNotEmpty) {
      expFrom = listExpAddIfClicked[0]["exp_from"];
      expEnd = listExpAddIfClicked[0]["exp_end"];
      expTitle = listExpAddIfClicked[0]["exp_title"];
      expDes = listExpAddIfClicked[0]["exp_des"];
      expCompanyName = listExpAddIfClicked[0]["exp_company_name"];
    }

    ApiCvs().createCv({
      "id": widget.id.toString(),
      "first_name": formData['first_name'],
      "last_name": formData['last_name'],
      "location": formData['location'],
      "job_name": formData['job_name'],
      "nationality": formData['nationality'],
      "phone": formData['phone'],
      "email": formData['email'],
      "age": formData['age'],
      "social_status": formData['social_status'],
      "summary": formData['summary'],
      "face": formData['face'],
      "twt": formData['twt'],
      "linked_in": formData['linked_in'],
      "edu": formData['edu'],
      "behance": formData['behance'],
      // ط¥ط±ط³ط§ظ„ ط¨ظٹط§ظ†ط§طھ ط§ظ„ط®ط¨ط±ط© ط¨ط´ظƒظ„ ظ…ظ†ظپطµظ„
      "exp_from": expFrom,
      "exp_end": expEnd,
      "exp_title": expTitle,
      "exp_des": expDes,
      "exp_company_name": expCompanyName,
      // ط¥ط±ط³ط§ظ„ ظ‚ظˆط§ط¦ظ… ط§ظ„ط¨ظٹط§ظ†ط§طھ
      "listexp": json.encode(listExpAddIfClicked),
      "listedu": json.encode(listEduAddIfClicked),
      "listlang": json.encode(listLangIfClicked),
      "listskill": json.encode(listSkillIfClicked),
      "listCourses": json.encode(listCourseAddIfClicked),
      'image': _image != null
          ? 'data:image/png;base64,${base64Encode(File(_image!.path).readAsBytesSync())}'
          : '',
    }, context).then((value) {
      setState(() {
        _sendData = false; // ط¥ط®ظپط§ط، ظ…ط¤ط´ط± ط§ظ„طھط­ظ…ظٹظ„ ط¨ط¹ط¯ ط§ظ„ط§ظ†طھظ‡ط§ط،
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = Theme.of(context).scaffoldBackgroundColor;

    if (!_isDataLoaded) {
      return Scaffold(
        backgroundColor: backgroundColor,
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                primaryColor.withOpacity(0.1),
                backgroundColor,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: primaryColor.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                    strokeWidth: 3,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'ط¬ط§ط±ظٹ طھط­ظ…ظٹظ„ ط¨ظٹط§ظ†ط§طھ ط§ظ„ط³ظٹط±ط© ط§ظ„ط°ط§طھظٹط©...',
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    final totalSteps = _cvData.course_from == 1 ? 7 : 6;
    final progress = (_currentPage + 1) / totalSteps;
    final isLastStep = _currentPage == totalSteps - 1;

    return Directionality(
      textDirection: _cvData.ltf == "0" ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: Stack(
          children: [
            // ط®ظ„ظپظٹط© ظ…طھط¯ط±ط¬ط©
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    primaryColor.withOpacity(0.05),
                    backgroundColor,
                    backgroundColor,
                  ],
                  stops: const [0.0, 0.3, 1.0],
                ),
              ),
            ),
            
            Column(
              children: [
                // Header ظ…ط¹ ظ…ط¤ط´ط± ط§ظ„طھظ‚ط¯ظ… ط§ظ„ظ…ط­ط³ظ†
                _buildModernHeader(progress, totalSteps),
                
                // ظ…ط­طھظˆظ‰ ط§ظ„ط®ط·ظˆط§طھ
                Expanded(
                  child: AnimatedBuilder(
                    animation: _slideAnimation,
                    builder: (context, child) {
                      return SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: PageView(
                            controller: _pageController,
                            physics: const NeverScrollableScrollPhysics(),
                            onPageChanged: (index) {
                              setState(() {
                                _currentPage = index;
                              });
                              _updateProgressAnimation();
                            },
                            children: [
                              // ط§ظ„طµظپط­ط© 1: ط§ظ„ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط£ط³ط§ط³ظٹط©
                              BasicInfoStep(
                                cvData: _cvData,
                                onDataChanged: _updateFormData,
                                currentData: formData,
                                currentImage: _image,
                                onImageChanged: (image) {
                                  setState(() {
                                    _image = image;
                                  });
                                },
                              ),
            
                              // ط§ظ„طµظپط­ط© 2: ط§ظ„ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط¥ط¶ط§ظپظٹط©
                              AdditionalInfoStep(
                                cvData: _cvData,
                                onDataChanged: _updateFormData,
                                currentData: formData,
                              ),
            
                              // ط§ظ„طµظپط­ط© 3: ط§ظ„ط®ط¨ط±ط§طھ
                              ExperienceStep(
                                cvData: _cvData,
                                onDataChanged: _updateFormData,
                                currentData: formData,
                                experienceList: listExpAddIfClicked,
                                onExperienceListChanged: (list) {
                                  setState(() {
                                    listExpAddIfClicked = list;
                                  });
                                },
                              ),
            
                              // ط§ظ„طµظپط­ط© 4: ط§ظ„طھط¹ظ„ظٹظ…
                              EducationStep(
                                cvData: _cvData,
                                onDataChanged: _updateFormData,
                                currentData: formData,
                                educationList: listEduAddIfClicked,
                                onEducationListChanged: (list) {
                                  setState(() {
                                    listEduAddIfClicked = list;
                                  });
                                },
                              ),
            
                              // ط§ظ„طµظپط­ط© 5: ط§ظ„ط¯ظˆط±ط§طھ (ط¥ط°ط§ ظƒط§ظ†طھ ظ…ظپط¹ظ„ط©)
                              if (_cvData.course_from == 1)
                                CoursesStep(
                                  cvData: _cvData,
                                  onDataChanged: _updateFormData,
                                  currentData: formData,
                                  coursesList: listCourseAddIfClicked,
                                  onCoursesListChanged: (list) {
                                    setState(() {
                                      listCourseAddIfClicked = list;
                                    });
                                  },
                                ),
            
                              // ط§ظ„طµظپط­ط© 6: ط§ظ„ظ„ط؛ط§طھ
                              LanguagesStep(
                                cvData: _cvData,
                                onDataChanged: _updateFormData,
                                currentData: formData,
                                languagesList: listLangIfClicked,
                                onLanguagesListChanged: (list) {
                                  setState(() {
                                    listLangIfClicked = list;
                                  });
                                },
                              ),
            
                              // ط§ظ„طµظپط­ط© 7: ط§ظ„ظ…ظ‡ط§ط±ط§طھ
                              SkillsStep(
                                cvData: _cvData,
                                onDataChanged: _updateFormData,
                                currentData: formData,
                                skillsList: listSkillIfClicked,
                                onSkillsListChanged: (list) {
                                  setState(() {
                                    listSkillIfClicked = list;
                                  });
                                },
                                onSubmit: _submitData,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // ط£ط²ط±ط§ط± ط§ظ„طھظ†ظ‚ظ„ ط§ظ„ظ…ط­ط³ظ†ط©
                _buildModernNavigationButtons(isLastStep),
              ],
            ),

            // ظ…ط¤ط´ط± ط§ظ„طھط­ظ…ظٹظ„ ط§ظ„ظ…ط­ط³ظ†
            if (_sendData) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildModernHeader(double progress, int totalSteps) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 4,
        left: 12,
        right: 12,
        bottom: 12,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          // ط§ظ„ط¹ظ†ظˆط§ظ† ظˆط§ظ„ط£ظٹظ‚ظˆظ†ط©
          Row(
            children: [
              // ط£ظٹظ‚ظˆظ†ط© ط§ظ„ط®ط·ظˆط©
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [primaryColor, accentColor],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withOpacity(0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Icon(
                  _getStepIconData(),
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _cvData.ltf == "0" ? 'ط¥ظ†ط´ط§ط، ط§ظ„ط³ظٹط±ط© ط§ظ„ط°ط§طھظٹط©' : 'Create CV',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 1),
                    Text(
                      '${_cvData.ltf == "0" ? "ط§ظ„ط®ط·ظˆط©" : "Step"} ${_currentPage + 1} ${_cvData.ltf == "0" ? "ظ…ظ†" : "of"} $totalSteps',
                      style: GoogleFonts.tajawal(
                        fontSize: 11,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(
                  Icons.arrow_forward,
                  color: primaryColor,
                  size: 16,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: primaryColor.withOpacity(0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.all(6),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // ط¹ظ†ظˆط§ظ† ط§ظ„ط®ط·ظˆط© ط§ظ„ط­ط§ظ„ظٹط©
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  primaryColor.withOpacity(0.1),
                  primaryColor.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: primaryColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Text(
              _getStepTitle(_currentPage),
              textAlign: TextAlign.center,
              style: GoogleFonts.tajawal(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: primaryColor,
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // ظ…ط¤ط´ط± ط§ظ„طھظ‚ط¯ظ… ط§ظ„ظ…ط­ط³ظ†
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _cvData.ltf == "0" ? 'ط§ظ„طھظ‚ط¯ظ…' : 'Progress',
                    style: GoogleFonts.tajawal(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '${(progress * 100).round()}%',
                    style: GoogleFonts.tajawal(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return Container(
                    height: 5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2.5),
                      color: Colors.grey[300],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(2.5),
                      child: LinearProgressIndicator(
                        value: _progressAnimation.value,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          progress == 1.0 ? successColor : primaryColor,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernNavigationButtons(bool isLastStep) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 10,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          // ط²ط± ط§ظ„ط±ط¬ظˆط¹
          if (_currentPage > 0)
            Expanded(
              child: Container(
                height: 38,
                margin: const EdgeInsets.only(right: 8),
                child: OutlinedButton.icon(
                  onPressed: _previousPage,
                  icon: Icon(
                    Icons.arrow_forward,
                    size: 14,
                  ),
                  label: Text(
                    _cvData.ltf == "0" ? 'ط§ظ„ط³ط§ط¨ظ‚' : 'Previous',
                    style: GoogleFonts.tajawal(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: primaryColor,
                    side: BorderSide(color: primaryColor, width: 1.5),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),
            ),
          
          // ط²ط± ط§ظ„طھط§ظ„ظٹ ط£ظˆ ط§ظ„ط¥ظ†ظ‡ط§ط،
          Expanded(
            flex: _currentPage > 0 ? 2 : 1,
            child: Container(
              height: 38,
              child: ElevatedButton.icon(
                onPressed: isLastStep ? _submitData : _nextPage,
                icon: Icon(
                  isLastStep ? Icons.check_circle : Icons.arrow_forward,
                  size: 14,
                ),
                label: Text(
                  isLastStep 
                    ? (_cvData.ltf == "0" ? 'ط¥ظ†ط´ط§ط، ط§ظ„ط³ظٹط±ط© ط§ظ„ط°ط§طھظٹط©' : 'Create CV')
                    : (_cvData.ltf == "0" ? 'ط§ظ„طھط§ظ„ظٹ' : 'Next'),
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isLastStep ? successColor : primaryColor,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shadowColor: (isLastStep ? successColor : primaryColor).withOpacity(0.3),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 16,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                strokeWidth: 2.5,
              ),
              const SizedBox(height: 16),
              Text(
                _cvData.ltf == "0" ? 'ط¬ط§ط±ظٹ ط¥ظ†ط´ط§ط، ط§ظ„ط³ظٹط±ط© ط§ظ„ط°ط§طھظٹط©...' : 'Creating CV...',
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // ط§ظ„ط­طµظˆظ„ ط¹ظ„ظ‰ ط£ظٹظ‚ظˆظ†ط© ط§ظ„ط®ط·ظˆط© ط§ظ„ط­ط§ظ„ظٹط©
  IconData _getStepIconData() {
    switch (_currentPage) {
      case 0:
        return Icons.person;
      case 1:
        return Icons.info;
      case 2:
        return Icons.work;
      case 3:
        return Icons.school;
      case 4:
        return _cvData.course_from == 1 ? Icons.book : Icons.language;
      case 5:
        return _cvData.course_from == 1 ? Icons.language : Icons.psychology;
      case 6:
        return Icons.psychology;
      default:
        return Icons.circle;
    }
  }

  // ط§ظ„ط­طµظˆظ„ ط¹ظ„ظ‰ ط¹ظ†ظˆط§ظ† ط§ظ„ط®ط·ظˆط© ط§ظ„ط­ط§ظ„ظٹط©
  String _getStepTitle(int step) {
    switch (step) {
      case 0:
        return _cvData.ltf == "0" ? "ط§ظ„ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط£ط³ط§ط³ظٹط©" : "Basic Information";
      case 1:
        return _cvData.ltf == "0" ? "ط§ظ„ظ…ط¹ظ„ظˆظ…ط§طھ ط§ظ„ط¥ط¶ط§ظپظٹط©" : "Additional Information";
      case 2:
        return _cvData.ltf == "0" ? "ط§ظ„ط®ط¨ط±ط§طھ" : "Experience";
      case 3:
        return _cvData.ltf == "0" ? "ط§ظ„طھط¹ظ„ظٹظ…" : "Education";
      case 4:
        if (_cvData.course_from == 1) {
          return _cvData.ltf == "0" ? "ط§ظ„ط¯ظˆط±ط§طھ" : "Courses";
        } else {
          return _cvData.ltf == "0" ? "ط§ظ„ظ„ط؛ط§طھ" : "Languages";
        }
      case 5:
        if (_cvData.course_from == 1) {
          return _cvData.ltf == "0" ? "ط§ظ„ظ„ط؛ط§طھ" : "Languages";
        } else {
          return _cvData.ltf == "0" ? "ط§ظ„ظ…ظ‡ط§ط±ط§طھ" : "Skills";
        }
      case 6:
        return _cvData.ltf == "0" ? "ط§ظ„ظ…ظ‡ط§ط±ط§طھ" : "Skills";
      default:
        return "";
    }
  }
}

