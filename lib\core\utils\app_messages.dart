import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';

// تأكد من وجود navigatorKey في main.dart
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class AppMessages {
  // ألوان موحدة للرسائل
  static final Color successColor = const Color(0xFF43A047);
  static final Color errorColor = const Color(0xFFE53935);
  static final Color infoColor = const Color(0xFF2daae2);
  static final Color warningColor = const Color(0xFFFFA000);

  // رسالة نجاح
  static void showSuccess(String message) {
    _showToast(
      message: message,
      backgroundColor: successColor,
      icon: Icons.check_circle_outline,
    );
  }

  // رسالة خطأ
  static void showError(String message) {
    _showToast(
      message: message,
      backgroundColor: errorColor,
      icon: Icons.error_outline,
    );
  }

  // رسالة معلومات
  static void showInfo(String message) {
    _showToast(
      message: message,
      backgroundColor: infoColor,
      icon: Icons.info_outline,
    );
  }

  // رسالة تحذير
  static void showWarning(String message) {
    _showToast(
      message: message,
      backgroundColor: warningColor,
      icon: Icons.warning_amber_outlined,
    );
  }

  // دالة مساعدة لعرض الرسائل
  static void _showToast({
    required String message,
    required Color backgroundColor,
    required IconData icon,
  }) {
    // استخدام Fluttertoast مباشرة بدون الحاجة إلى سياق
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: 3,
      backgroundColor: backgroundColor,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  // رسالة تأكيد
  static void showConfirmation({
    required BuildContext context,
    required String message,
    required String confirmText,
    required VoidCallback onConfirm,
    String cancelText = "إلغاء",
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final Color dialogBg = isDark ? Colors.grey[900]! : Colors.white;
    final Color titleColor = isDark ? infoColor.withOpacity(0.9) : infoColor;
    final Color iconBg = isDark ? Colors.blueGrey[800]! : infoColor.withOpacity(0.1);
    final Color confirmBtn = infoColor;
    final Color cancelBtn = isDark ? Colors.grey[800]! : infoColor.withOpacity(0.1);
    final Color cancelTextColor = isDark ? Colors.white70 : infoColor;
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.25),
      builder: (context) {
        return AlertDialog(
          backgroundColor: dialogBg,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(18),
          ),
          titlePadding: const EdgeInsets.only(top: 24, left: 24, right: 24, bottom: 0),
          contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          actionsPadding: const EdgeInsets.only(bottom: 16, left: 16, right: 16, top: 0),
          title: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: iconBg,
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(18),
                child: Icon(
                  Icons.warning_amber_rounded,
                  color: titleColor,
                  size: 40,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'تأكيد',
                textAlign: TextAlign.center,
                style: GoogleFonts.tajawal(
                  fontWeight: FontWeight.bold,
                  color: titleColor,
                  fontSize: 22,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            textAlign: TextAlign.center,
            style: GoogleFonts.tajawal(
              fontSize: 16,
              color: isDark ? Colors.white70 : Colors.grey[800],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              textDirection: TextDirection.rtl,
              children: [
                Expanded(
                  child: TextButton.icon(
                    icon: const Icon(
                      Icons.check_circle_outline,
                      color: Colors.white,
                      size: 20,
                    ),
                    label: Text(
                      confirmText,
                      style: const TextStyle(color: Colors.white),
                    ),
                    style: TextButton.styleFrom(
                      backgroundColor: confirmBtn,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                      onConfirm();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextButton.icon(
                    icon: Icon(
                      Icons.cancel_outlined,
                      color: cancelTextColor,
                      size: 20,
                    ),
                    label: Text(
                      cancelText,
                      style: TextStyle(color: cancelTextColor),
                    ),
                    style: TextButton.styleFrom(
                      backgroundColor: cancelBtn,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}

// تأكد من وجود navigatorKey في main.dart





