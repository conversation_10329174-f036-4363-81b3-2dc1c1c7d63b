import 'dart:ui';

enum ApplicationStatus {
  pending,
  viewed,
  accepted,
  rejected
}

extension ApplicationStatusExtension on ApplicationStatus {
  String get name {
    switch (this) {
      case ApplicationStatus.pending:
        return 'قيد الانتظار';
      case ApplicationStatus.viewed:
        return 'تمت المشاهدة';
      case ApplicationStatus.accepted:
        return 'تم القبول';
      case ApplicationStatus.rejected:
        return 'تم الرفض';
    }
  }
  
  Color get color {
    switch (this) {
      case ApplicationStatus.pending:
        return const Color(0xFFFFC757);
      case ApplicationStatus.viewed:
        return const Color(0xFF2daae2);
      case ApplicationStatus.accepted:
        return const Color(0xFF4CD963);
      case ApplicationStatus.rejected:
        return const Color(0xFFFF6E47);
    }
  }
}

class JobApplication {
  final int id;
  final String slug;
  final String title;
  final String company_name;
  final String city_name;
  final String country_name;
  final String applied_date;
  final ApplicationStatus status;
  final String? interview_date;
  final String? application_title;
  final String? application_email;
  final String? application_phone;
  final dynamic application_salary;
  final String? application_description;

  JobApplication({
    required this.id,
    required this.slug,
    required this.title,
    required this.company_name,
    required this.city_name,
    required this.country_name,
    required this.applied_date,
    required this.status,
    this.interview_date,
    this.application_title,
    this.application_email,
    this.application_phone,
    this.application_salary,
    this.application_description,
  });
}