import 'dart:io';
import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/constants/Constants.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/core/providers/notification_provider.dart';
import 'package:wzzff/core/providers/theme_provider.dart';
import 'package:wzzff/presentation/screens/maintenance/maintenance_screen.dart';
import 'package:wzzff/presentation/screens/splash_screen.dart';
import 'package:wzzff/presentation/screens/maintenance/update_required_screen.dart';
import 'package:wzzff/presentation/widgets/CustomDrawer.dart';
import 'package:wzzff/presentation/widgets/notification_badge.dart';
import 'package:wzzff/services/deep_link_handler.dart';
import 'package:wzzff/services/google_ad_service.dart';
import 'package:wzzff/services/firebase_messaging_service.dart';
import 'package:wzzff/services/local_notification_service.dart';
import 'package:wzzff/services/scheduled_notification_service.dart';
import 'package:wzzff/services/daily_notification_service.dart';
import 'package:wzzff/services/smart_notification_manager.dart';
import 'package:wzzff/presentation/screens/jobs/resultsScreen.dart';
import 'package:wzzff/presentation/screens/home.dart';
import 'package:wzzff/core/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';
import 'package:wzzff/services/deep_link_service.dart';
import 'package:wzzff/presentation/screens/home_screen.dart' show HomeScreen;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:wzzff/core/theme/app_theme.dart';
import 'package:wzzff/presentation/widgets/server_error_overlay.dart';
import 'package:wzzff/core/services/user_service.dart';
import 'package:flutter/foundation.dart';

// تعريف Notification مخصصة
class GoToAccountTabNotification extends Notification {}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // شغل التطبيق مباشرة مع SplashScreen
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()..initialize()),
        ChangeNotifierProvider(create: (_) => AppStateProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final GoogleAdService _adService = GoogleAdService();
  bool _isFirstLaunch = true;
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  late DeepLinkHandler _deepLinkHandler;
  bool _servicesInitialized = false;
  bool _isOffline = false;
  late final Connectivity _connectivity;
  late final Stream<ConnectivityResult> _connectivityStream;
  late final StreamSubscription<ConnectivityResult> _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _deepLinkHandler = DeepLinkHandler(navigatorKey: _navigatorKey);
    _deepLinkHandler.initialize();
    _initializeServices();
    //debugPrint('MyApp: initState');

    _connectivity = Connectivity();
    _connectivityStream = _connectivity.onConnectivityChanged;
    _connectivitySubscription = _connectivityStream.listen((result) async {
      final isOffline = result == ConnectivityResult.none;
      //debugPrint('Connectivity changed: result=$result, isOffline=$isOffline, _isOffline=$_isOffline');
      if (isOffline != _isOffline) {
        setState(() {
          _isOffline = isOffline;
         // debugPrint('setState: _isOffline=$_isOffline');
        });
      }
    });
    // تحقق أولي عند بدء التطبيق
    _checkInitialConnectivity();
  }

  Future<void> _initializeServices() async {
    //debugPrint('MyApp: بدء تهيئة الخدمات');
    try {
      await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
      //debugPrint('MyApp: Firebase initialized');
      await FirebaseMessagingService().initialize();
      // debugPrint('MyApp: FirebaseMessagingService initialized');
      await LocalNotificationService().initialize();
      //debugPrint('MyApp: LocalNotificationService initialized');
      await ScheduledNotificationService().initialize();
      //debugPrint('MyApp: ScheduledNotificationService initialized');
      await DailyNotificationService().initialize();
     // debugPrint('MyApp: DailyNotificationService initialized');
      await SmartNotificationManager().initialize();
      //debugPrint('MyApp: SmartNotificationManager initialized');
      await GoogleAdService().initialize();
      //debugPrint('MyApp: GoogleAdService initialized');
      
      // تحميل حالة التطبيق من السيرفر
      if (mounted) {
      //  debugPrint('MyApp: بدء تحميل حالة التطبيق من السيرفر');
        final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
        await appStateProvider.loadAppState();
      //  debugPrint('MyApp: تم تحميل حالة التطبيق بنجاح');
      }
      
      setState(() {
        _servicesInitialized = true;
      });
      // إعادة تفعيل إعلان فتح التطبيق بعد التهيئة
      _adService.showAppOpenAd();
     // debugPrint('MyApp: جميع الخدمات تم تهيئتها وتم عرض إعلان فتح التطبيق');
    } catch (e, st) {
     // debugPrint('MyApp: خطأ أثناء تهيئة الخدمات: $e\n$st');
      // حتى لو فشل تحميل حالة التطبيق، نكمل بعرض التطبيق
      if (mounted) {
        setState(() {
          _servicesInitialized = true;
        });
      }
    }
  }

  Future<void> _checkInitialConnectivity() async {
    final result = await _connectivity.checkConnectivity();
    final isOffline = result == ConnectivityResult.none;
    if (isOffline != _isOffline && mounted) {
      setState(() {
        _isOffline = isOffline;
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _adService.dispose();
    ScheduledNotificationService().dispose();
    _connectivitySubscription.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (!mounted) return;
    if (state == AppLifecycleState.resumed) {
     // debugPrint('App resumed');
      // تم إزالة تحديث AppState هنا لأنه يجب أن يحدث مرة واحدة فقط عند بدء التطبيق
    }
  }

  /// معالجة التنقل المعلق من الإشعارات
  Future<void> _handlePendingNotificationNavigation() async {
    try {
      final pendingNavigation = await DailyNotificationService().getPendingNavigation();
      
      if (pendingNavigation != null && _navigatorKey.currentContext != null) {
        final destination = pendingNavigation['destination'] as String;
        final data = pendingNavigation['data'] as Map<String, dynamic>;
        
     //   debugPrint('معالجة التنقل من الإشعار: $destination');
        
        // انتظار قصير للتأكد من أن التطبيق جاهز
        await Future.delayed(Duration(milliseconds: 500));
        
        _navigateFromNotification(destination, data);
      }
    } catch (e) {
      // debugPrint('خطأ في معالجة التنقل من الإشعار: $e');
    }
  }

  /// التنقل بناءً على نوع الإشعار
  void _navigateFromNotification(String destination, Map<String, dynamic> data) {
    final context = _navigatorKey.currentContext;
    if (context == null) return;

    switch (destination) {
      case 'smart_matched_jobs':
        _navigateToSmartMatchedJobs(context);
        break;
      case 'suggested_jobs':
        _navigateToSuggestedJobs(context);
        break;
      case 'specific_job':
        _navigateToSpecificJob(context, data);
        break;
      case 'job_details':
        _navigateToJobDetails(context, data);
        break;
      case 'profile_insights':
        _navigateToProfileInsights(context);
        break;
      default:
        //debugPrint('وجهة تنقل غير معروفة: $destination');
    }
  }

  /// التنقل للوظائف المطابقة ذكياً
  void _navigateToSmartMatchedJobs(BuildContext context) {
    Navigator.of(context).pushNamed('/smart_matched_jobs');
  }

  /// التنقل للوظائف المقترحة
  void _navigateToSuggestedJobs(BuildContext context) {
    Navigator.of(context).pushNamed('/suggested_jobs');
  }

  /// التنقل لوظيفة محددة
  void _navigateToSpecificJob(BuildContext context, Map<String, dynamic> data) {
    final jobSlug = data['job_slug'] as String?;
    if (jobSlug != null) {
      Navigator.of(context).pushNamed('/job_details', arguments: {'slug': jobSlug});
    }
  }

  /// التنقل لتفاصيل الوظيفة
  void _navigateToJobDetails(BuildContext context, Map<String, dynamic> data) {
    final jobSlug = data['job_slug'] as String?;
    if (jobSlug != null) {
      Navigator.of(context).pushNamed('/job_details', arguments: {'slug': jobSlug});
    }
  }

  /// التنقل لتحليل الملف الشخصي
  void _navigateToProfileInsights(BuildContext context) {
    Navigator.of(context).pushNamed('/profile_insights');
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final appStateProvider = Provider.of<AppStateProvider>(context);

    // إذا لم تكتمل التهيئة بعد، أظهر SplashScreen فقط
    if (!_servicesInitialized) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
        locale: const Locale('ar', 'SA'),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ar', 'SA'),
        ],
        home: const SplashScreen(), // لا تمرر nextScreen هنا
      );
    }

    // معالجة الرابط المؤجل بعد أول build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _deepLinkHandler.processPendingDeepLink();
      _handlePendingNotificationNavigation();
    });

    // التحقق من حالة التطبيق
    if (appStateProvider.isLoading) {
      // إظهار شاشة التحميل
      return const MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    } else if (appStateProvider.appState != null) {
      // التحقق من وضع الصيانة
      if (appStateProvider.isInMaintenanceMode()) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
          home: const MaintenanceScreen(),
        );
      }

      // التحقق من التحديث الإجباري
      if (appStateProvider.needsForceUpdate) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
          home: const UpdateRequiredScreen(isForceUpdate: true),
        );
      }
    }

    // بعد التهيئة، أظهر الصفحة الرئيسية مباشرة
    return Directionality(
      textDirection: TextDirection.rtl,
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: Constants.appName,
        navigatorKey: _navigatorKey,
        locale: const Locale('ar', 'SA'),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ar', 'SA'),
        ],
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
        home: const MysideHaveHome(title: Constants.appName),
        builder: (context, child) {
          final appStateProvider = Provider.of<AppStateProvider>(context);
          return Stack(
            children: [
              child!,
              if (_isOffline) _NoInternetOverlay(),
              if (appStateProvider.showServerError)
                ServerErrorOverlay(
                  message: appStateProvider.serverErrorMessage.isNotEmpty
                      ? appStateProvider.serverErrorMessage
                      : 'نواجه مشاكل في السيرفر، سنعيد المحاولة بعد قليل',
                  retrySeconds: appStateProvider.retrySeconds,
                  onRetry: () {
                    appStateProvider.clearServerError();
                    // إعادة تحميل الحالة أو البيانات حسب الحاجة (يجب أن يتم استدعاء التحميل من الصفحة نفسها)
                  },
                ),
            ],
          );
        },
        onGenerateRoute: (settings) {
          if (settings.name == '/job_details') {
            final dynamic arg = settings.arguments;
            return MaterialPageRoute(
              builder: (context) => FutureBuilder<JobModel?>(
                future: () async {
                  int? jobId;
                  if (arg is int) {
                    jobId = arg;
                  } else if (arg is String) {
                    jobId = DeepLinkService().parseJobId(arg);
                  }
                  if (jobId == null) return null;
                  return await JobsApi.getJobDetails(jobId: jobId.toString());
                }(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Scaffold(body: Center(child: CircularProgressIndicator()));
                  }
                  if (!snapshot.hasData || snapshot.data == null) {
                    return const Scaffold(body: Center(child: Text('لم يتم العثور على الوظيفة')));
                  }
                  final job = snapshot.data!;
                  return DetailJobScreen(
                    title: job.title,
                    code_address: job.code_address,
                    des: job.description,
                    cat: job.cat,
                    city_name: job.city_name,
                    company_name: job.company_name,
                    country_name: job.country_name,
                    created_at_date: job.created_at_date,
                    edu: job.edu,
                    email: job.email,
                    end_at: job.end_at,
                    exp: job.exp,
                    gender: job.gender,
                    job_type_name: job.job_type_name,
                    number: job.number,
                    salary: job.salary,
                    salary_currency: job.salary_currency,
                    slug: job.slug,
                    state_name: job.state_name,
                    time: job.time,
                  );
                },
              ),
            );
          }
          return null;
        },
      ),
    );
  }
}

class MysideHaveHome extends StatefulWidget {
  const MysideHaveHome({super.key, required this.title, this.initialTabIndex = 0});

  final String title;
  final int initialTabIndex;

  @override
  _MysideHaveHomeState createState() => _MysideHaveHomeState();
}

class _MysideHaveHomeState extends State<MysideHaveHome> with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ValueNotifier<bool> _isSearchOpen = ValueNotifier<bool>(false);
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;
  int _selectedIndex = 0;
  bool _welcomeNotified = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    // تأجيل إرسال الإشعارات الترحيبية حتى انتهاء أول إطار بعد تحميل الصفحة الرئيسية بالكامل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 2), _sendWelcomeNotificationsIfNeeded);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _sendWelcomeNotificationsIfNeeded() async {
    if (_welcomeNotified) return;
    final prefs = await SharedPreferences.getInstance();
    final isFirstRun = prefs.getBool('is_first_run') ?? true;
    if (isFirstRun) {
      try {
        final notificationService = LocalNotificationService();
        await notificationService.initialize();
        await notificationService.showNotification(
          id: 1001,
          title: '👋 مرحباً بك في تطبيق وظف دوت كوم',
          body: 'نحن سعداء بانضمامك إلينا! استكشف آلاف الوظائف المتاحة الآن.',
          payload: {'type': 'welcome', 'id': '1001'},
        );
        await Future.delayed(const Duration(seconds: 1));
        await notificationService.showNotification(
          id: 1002,
          title: '📝 أنشئ سيرتك الذاتية مجاناً',
          body: 'يمكنك الآن إنشاء سيرة ذاتية احترافية بتنسيق PDF مجاناً من خلال القائمة الجانبية للتطبيق.',
          payload: {'type': 'cv_feature', 'id': '1002'},
        );
        await prefs.setBool('is_first_run', false);
        _welcomeNotified = true;
      } catch (e) {
        // debugPrint('خطأ في إرسال الإشعارات الترحيبية: $e');
      }
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
      _tabController.animateTo(index);
    });
  }

  Future<bool> _showExitConfirmationDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) {
        final primaryColor = Theme.of(context).colorScheme.primary;
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            'تأكيد الخروج',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
          content: const Text(
            'هل تريد الخروج من التطبيق؟',
            textAlign: TextAlign.center,
          ),
          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton.icon(
                  icon: const Icon(
                    Icons.exit_to_app,
                    color: Colors.white,
                    size: 20,
                  ),
                  label: const Text(
                    'خروج',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: TextButton.styleFrom(
                    backgroundColor: primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  ),
                  onPressed: () {
                    // استخدام SystemNavigator للخروج من التطبيق بشكل كامل
                    if (Platform.isAndroid) {
                      SystemNavigator.pop();
                    } else if (Platform.isIOS) {
                      exit(0);
                    }
                  },
                ),
                TextButton.icon(
                  icon: Icon(
                    Icons.home_outlined,
                    color: primaryColor,
                    size: 20,
                  ),
                  label: Text(
                    'البقاء',
                    style: TextStyle(color: primaryColor),
                  ),
                  style: TextButton.styleFrom(
                    backgroundColor: primaryColor.withAlpha(26), // 0.1 = 26
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  ),
                  onPressed: () => Navigator.of(context).pop(false),
                ),
              ],
            ),
          ],
        );
      },
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        final shouldPop = await _showExitConfirmationDialog();
        if (shouldPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        key: _scaffoldKey,
        appBar: AppBar(
          backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
          toolbarHeight: 70,
          centerTitle: false,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.menu,
              color: Theme.of(context).appBarTheme.foregroundColor,
              size: 28,
            ),
            onPressed: () {
              _scaffoldKey.currentState!.openDrawer();
            },
          ),
          title: ValueListenableBuilder<bool>(
            valueListenable: _isSearchOpen,
            builder: (context, isSearchVisible, child) {
              return Row(
                children: [
                  Expanded(
                    child: Stack(
                      alignment: Alignment.centerLeft,
                      children: [
                        if (!isSearchVisible)
                          Padding(
                            padding: const EdgeInsets.only(left: 50),
                            child: Center(
                              child: Image.asset(
                                "assets/logonewBar.png",
                                height: 40,
                              ),
                            ),
                          ),
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          width: isSearchVisible ? MediaQuery.of(context).size.width - 100 : 50,
                          child: Material(
                            color: Colors.transparent,
                            child: Container(
                              decoration: BoxDecoration(
                                color: isSearchVisible
                                    ? (Theme.of(context).brightness == Brightness.dark
                                        ? Theme.of(context).colorScheme.surface
                                        : Colors.white)
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(25),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (isSearchVisible)
                                    Expanded(
                                      child: TextField(
                                        controller: _searchController,
                                        autofocus: true,
                                        style: TextStyle(
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? Colors.white
                                              : Colors.black
                                        ),
                                        decoration: InputDecoration(
                                          hintText: "بحث سريع...",
                                          hintTextDirection: TextDirection.rtl,
                                          border: InputBorder.none,
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 15),
                                          hintStyle: TextStyle(
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.grey[400]
                                                : Colors.grey
                                          ),
                                        ),
                                        textDirection: TextDirection.rtl,
                                        onSubmitted: (value) {
                                          Navigator.push(context, MaterialPageRoute(builder: (context) {
                                            return ResultsScreen(country: "", city: "", word: value);
                                          }));
                                        },
                                      ),
                                    ),
                                  IconButton(
                                    icon: Icon(
                                      isSearchVisible ? Icons.close : Icons.search,
                                      color: isSearchVisible
                                          ? (Theme.of(context).brightness == Brightness.dark
                                              ? Colors.white
                                              : Colors.black)
                                          : Theme.of(context).appBarTheme.foregroundColor,
                                    ),
                                    onPressed: () {
                                      if (isSearchVisible) {
                                        _searchController.clear();
                                        _isSearchOpen.value = false;
                                      } else {
                                        _isSearchOpen.value = true;
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.account_circle_outlined, color: Colors.white),
              tooltip: 'حسابي',
              onPressed: () {
                _tabController.animateTo(5); // الانتقال لتبويب حسابي
              },
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: NotificationBadge(
               // showUnreadOnly: true,
              ),
            ),
          ],
        ),
        drawer: CustomDrawer(
          onItemSelected: _onItemTapped,
          selectedIndex: _selectedIndex,
        ),
        body: Directionality(
          textDirection: Constants().getOurDir(),
          child: HomeScreen(tabController: _tabController),
        ),
      ),
    );
  }
}

// ويدجت شاشة انقطاع الإنترنت الشفافة
class _NoInternetOverlay extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    //debugPrint('Building _NoInternetOverlay');
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Stack(
      children: [
        // تمنع كل التفاعل مع التطبيق
        ModalBarrier(
          color: Colors.black.withOpacity(0.25),
          dismissible: false,
        ),
        Center(
          child: AbsorbPointer(
            absorbing: true,
            child: Card(
              elevation: 16,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28),
              ),
              color: isDark ? Colors.grey[900] : Colors.white,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 36),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.wifi_off,
                      size: 70,
                      color: Colors.redAccent.withOpacity(0.85),
                    ),
                    const SizedBox(height: 18),
                    Text(
                      'لا يوجد اتصال بالإنترنت',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.black,
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'يرجى التحقق من اتصال الشبكة للمتابعة',
                      style: TextStyle(
                        fontSize: 15,
                        color: isDark ? Colors.white70 : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
} 