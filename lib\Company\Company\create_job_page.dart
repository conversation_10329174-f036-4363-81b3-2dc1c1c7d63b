import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/SpinnerDataModel.dart';
import 'package:wzzff/Apis/CompanyApi.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/Company/create/steps/job_form_data.dart';
import 'package:wzzff/Company/create/steps/step_basic_info.dart';
import 'package:wzzff/Company/create/steps/step_job_details.dart';
import 'package:wzzff/Company/create/steps/step_requirements.dart';
import 'package:wzzff/Company/create/steps/step_preview.dart';
import 'package:wzzff/Company/create/steps/step_confirmation.dart';
import 'package:wzzff/main.dart';

class CreateJobPage extends StatefulWidget {
  @override
  _CreateJobPageState createState() => _CreateJobPageState();
}

class _CreateJobPageState extends State<CreateJobPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  int _currentStep = 0;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  // بيانات النموذج
  late JobFormData _formData;

  // Animation Controllers
  late AnimationController _animationController;
  late AnimationController _stepAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Theme helpers
  bool get _isDarkMode => Theme.of(context).brightness == Brightness.dark;
  Color get _primaryColor => Theme.of(context).colorScheme.primary;
  Color get _backgroundColor => Theme.of(context).scaffoldBackgroundColor;
  Color get _cardColor => _isDarkMode ? Colors.grey[850]! : Colors.white;
  Color get _secondaryTextColor => _isDarkMode ? Colors.grey[300]! : Colors.grey[600]!;

  @override
  void initState() {
    super.initState();
    _formData = JobFormData();
    _setupAnimations();
    _fetchFormData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _stepAnimationController.dispose();
    _formData.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.2, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _stepAnimationController,
      curve: Curves.easeOut,
    ));

    // بدء الانيميشن فوراً للخطوة الأولى
    _stepAnimationController.forward();
  }

  List<SpinnerDataModel> getCountries() {
    return [
      SpinnerDataModel(name: 'السعودية', id: 1),
      SpinnerDataModel(name: 'مصر', id: 2),
      SpinnerDataModel(name: 'الامارات', id: 3),
      SpinnerDataModel(name: 'الأردن', id: 4),
      SpinnerDataModel(name: 'البحرين', id: 5),
      SpinnerDataModel(name: 'الكويت', id: 6),
      SpinnerDataModel(name: 'قطر', id: 7),
      SpinnerDataModel(name: 'عمان', id: 8),
      SpinnerDataModel(name: 'العراق', id: 9),
      SpinnerDataModel(name: 'الجزائر', id: 10),
      SpinnerDataModel(name: 'المغرب', id: 11),
      SpinnerDataModel(name: 'تونس', id: 12),
      SpinnerDataModel(name: 'لبنان', id: 13),
      SpinnerDataModel(name: 'سوريا', id: 14),
      SpinnerDataModel(name: 'السودان', id: 15),
      SpinnerDataModel(name: 'ليبيا', id: 16),
      SpinnerDataModel(name: 'فلسطين', id: 17),
      SpinnerDataModel(name: 'اليمن', id: 18),
    ];
  }

  Future<void> _getCityByCountry(String countryName) async {
    setState(() { _formData.isCitiesLoading = true; });
    try {
      final cities = await JobsApi().getCitesApi(country: countryName);
      setState(() {
        _formData.cities = cities;
        _formData.selectedCity = null;
        _formData.isCitiesLoading = false;
      });
    } catch (e) {
      setState(() {
        _formData.isCitiesLoading = false;
        _formData.cities = [];
      });
    }
  }

  Future<void> _fetchFormData() async {
    setState(() { _isLoading = true; _error = null; });
    
    try {
      // محاكاة تحميل البيانات - تم تقليل الوقت
      await Future.delayed(const Duration(milliseconds: 300));
      
      setState(() {
        _formData.countries = getCountries();
        _formData.selectedCountry = _formData.countries.first;
        _formData.jobTypes = ['دوام كامل', 'دوام جزئي', 'عن بعد', 'تدريب', 'مشروع'];
        _formData.categories = ['تقنية المعلومات', 'التسويق', 'المبيعات', 'الإدارة', 'المحاسبة', 'الهندسة', 'الطب'];
        _formData.educationLevels = ['ثانوي', 'دبلوم', 'بكالوريوس', 'ماجستير', 'دكتوراه'];
        _formData.experienceLevels = ['بدون خبرة', '1-2 سنة', '3-5 سنوات', '6-10 سنوات', 'أكثر من 10 سنوات'];
        _formData.genders = ['ذكر', 'أنثى', 'الجميع'];
        _formData.currencies = ['ريال سعودي', 'دولار أمريكي', 'يورو','جنيه مصري','دينار كويتي','دينار بحريني','ريال عماني','دينار عراقي','دينار تونسي','دينار سوري','دينار ليبي','دينار مغربي'];
        _isLoading = false;
      });
      
      // تحميل المدن بعد تحديد الدولة
      await _getCityByCountry('السعودية');
      
      // بدء انيميشن الصفحة بعد اكتمال تحميل البيانات
      _animationController.forward();
      
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ في تحميل البيانات';
        _isLoading = false;
      });
    }
  }

  void _nextStep() {
    final validation = _formData.validateStep(_currentStep);
    if (!validation.isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(validation.errorMessage,
            style: GoogleFonts.tajawal(fontSize: 13)),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
      return;
    }

    if (_currentStep < 4) {
      setState(() => _currentStep++);
      _stepAnimationController.reset();
      _stepAnimationController.forward();
    }
  }

  void _prevStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _stepAnimationController.reset();
      _stepAnimationController.forward();
    }
  }

  Future<void> _saveJob() async {
    final validation = _formData.validateStep(_currentStep);
    if (!validation.isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(validation.errorMessage,
            style: GoogleFonts.tajawal(fontSize: 13)),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
      return;
    }

    setState(() { _isSaving = true; });
    
    try {
      final response = await CompanyApi().createJob(_formData.toJson());
      
      if (response['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response['message'] ?? 'تم إنشاء الوظيفة بنجاح', 
              style: GoogleFonts.tajawal(fontSize: 13)),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          )
        );
        
        // الانتقال إلى dashboard الشركة مع تحديد تاب "حسابي"
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => MysideHaveHome(
              title: "Wzzff", 
              initialTabIndex: 5, // تاب "حسابي"
            ),
          ),
          (route) => false,
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response['message'] ?? 'حدث خطأ أثناء إنشاء الوظيفة', 
              style: GoogleFonts.tajawal(fontSize: 13)),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          )
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تعذر الاتصال بالخادم، يرجى المحاولة لاحقاً', 
            style: GoogleFonts.tajawal(fontSize: 13)),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        )
      );
    } finally {
      setState(() { _isSaving = false; });
    }
  }

  // التحقق من وجود بيانات مدخلة
  bool _hasAnyData() {
    return _formData.titleController.text.isNotEmpty ||
           _formData.descController.text.isNotEmpty ||
           _formData.reqController.text.isNotEmpty ||
           _formData.salaryController.text.isNotEmpty ||
           _formData.companyEmailController.text.isNotEmpty ||
           _formData.companyPhoneController.text.isNotEmpty ||
           _formData.selectedJobType != null ||
           _formData.selectedCategory != null ||
           _formData.selectedEducation != null ||
           _formData.selectedExperience != null ||
           _formData.selectedGender != null ||
           _formData.selectedCurrency != null ||
           _formData.endDate != null;
  }

  // إظهار حوار التحذير عند الخروج
  Future<bool> _showExitConfirmation() async {
    if (!_hasAnyData()) {
      return true; // السماح بالخروج مباشرة إذا لم يتم إدخال أي بيانات
    }

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          title: Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              Text(
                'تحذير',
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          content: Container(
            constraints: const BoxConstraints(maxWidth: 300),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'لم تكمل إنشاء الوظيفة بعد.',
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إذا خرجت الآن، ستفقد جميع البيانات التي أدخلتها. هل أنت متأكد من رغبتك في الخروج؟',
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'البقاء',
                style: GoogleFonts.tajawal(
                  fontSize: 13,
                  color: _primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              child: Text(
                'فهمت',
                style: GoogleFonts.tajawal(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );

    return result ?? false;
  }

  List<String> _stepTitles = [
    'معلومات الوظيفة',
    'تفاصيل الوظيفة', 
    'متطلبات الوظيفة',
    'معاينة الوظيفة',
    'تأكيد وحفظ',
  ];

  List<IconData> _stepIcons = [
    Icons.info_outline,
    Icons.description,
    Icons.list_alt,
    Icons.preview,
    Icons.check_circle_outline,
  ];

  Widget _buildStepIndicator() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _cardColor,
        boxShadow: [
          BoxShadow(
            color: _isDarkMode ? Colors.black.withOpacity(0.3) : Colors.grey.withOpacity(0.15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(_stepTitles.length, (index) {
          final isActive = _currentStep == index;
          final isCompleted = _currentStep > index;
          
          return Column(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isCompleted
                      ? Colors.green
                      : isActive
                          ? _primaryColor
                          : _isDarkMode ? Colors.grey[600] : Colors.grey[300],
                  boxShadow: isActive || isCompleted
                      ? [
                          BoxShadow(
                            color: (isCompleted ? Colors.green : _primaryColor).withOpacity(0.3),
                            blurRadius: 6,
                            offset: const Offset(0, 1),
                          )
                        ]
                      : [],
                ),
                child: Icon(
                  isCompleted ? Icons.check : _stepIcons[index],
                  color: isActive || isCompleted ? Colors.white : _secondaryTextColor,
                  size: 16,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                _stepTitles[index],
                style: GoogleFonts.tajawal(
                  fontSize: 9,
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  color: isActive || isCompleted 
                      ? _primaryColor 
                      : _secondaryTextColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildStepContent() {
    switch (_currentStep) {
      case 0:
        return StepBasicInfo(
          formData: _formData,
          onCountryChanged: _getCityByCountry,
        );
      case 1:
        return StepJobDetails(formData: _formData);
      case 2:
        return StepRequirements(formData: _formData);
      case 3:
        return StepPreview(formData: _formData);
      case 4:
        return const StepConfirmation();
      default:
        return Container();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        
        final shouldPop = await _showExitConfirmation();
        if (shouldPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: Scaffold(
          backgroundColor: _backgroundColor,
          appBar: AppBar(
            title: Text(
              'إنشاء وظيفة جديدة',
              style: GoogleFonts.tajawal(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            backgroundColor: _primaryColor,
            elevation: 0,
            centerTitle: true,
            toolbarHeight: 50,
            iconTheme: const IconThemeData(color: Colors.white),
            automaticallyImplyLeading: false,
            actions: [
              IconButton(
                icon: const Icon(Icons.arrow_forward, color: Colors.white),
                onPressed: () async {
                  final shouldPop = await _showExitConfirmation();
                  if (shouldPop && context.mounted) {
                    Navigator.of(context).pop();
                  }
                },
              ),
            ],
          ),
          body: _isLoading
              ? Container(
                  color: _backgroundColor,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: _primaryColor),
                        SizedBox(height: 12),
                        Text('جاري تحميل البيانات...', style: GoogleFonts.tajawal(
                          fontSize: 14,
                          color: _isDarkMode ? Colors.white : Colors.black87,
                        )),
                      ],
                    ),
                  ),
                )
              : _error != null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline, size: 48, color: Colors.red),
                          SizedBox(height: 16),
                          Text(_error!, style: GoogleFonts.tajawal(
                              color: Colors.red, 
                              fontSize: 14
                            )),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _fetchFormData,
                            child: Text('إعادة المحاولة', style: GoogleFonts.tajawal()),
                          ),
                        ],
                      ),
                    )
                  : FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          _buildStepIndicator(),
                          Expanded(
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              child: SlideTransition(
                                position: _slideAnimation,
                                child: _buildStepContent(),
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: _cardColor,
                              boxShadow: [
                                BoxShadow(
                                  color: _isDarkMode ? Colors.black.withOpacity(0.3) : Colors.grey.withOpacity(0.15),
                                  blurRadius: 8,
                                  offset: const Offset(0, -1),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                if (_currentStep > 0)
                                  Expanded(
                                    child: OutlinedButton.icon(
                                      onPressed: _prevStep,
                                      icon: const Icon(Icons.arrow_forward, size: 16),
                                      label: Text('السابق', style: GoogleFonts.tajawal(fontSize: 13)),
                                      style: OutlinedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(vertical: 12),
                                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                        side: BorderSide(color: _primaryColor),
                                        foregroundColor: _primaryColor,
                                      ),
                                    ),
                                  ),
                                if (_currentStep > 0) const SizedBox(width: 12),
                                Expanded(
                                  flex: _currentStep > 0 ? 1 : 2,
                                  child: ElevatedButton.icon(
                                    onPressed: _isSaving
                                        ? null
                                        : () {
                                            if (_currentStep == 4) {
                                              _saveJob();
                                            } else {
                                              _nextStep();
                                            }
                                          },
                                    icon: _isSaving
                                        ? const SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                                          )
                                        : Icon(_currentStep == 4 ? Icons.publish : Icons.arrow_back, size: 16),
                                    label: Text(_currentStep == 4 ? 'نشر الوظيفة' : 'التالي', 
                                      style: GoogleFonts.tajawal(fontSize: 13)),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: _currentStep == 4 ? Colors.green : _primaryColor,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(vertical: 12),
                                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
        ),
      ),
    );
  }
}