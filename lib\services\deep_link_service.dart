import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:flutter/services.dart';

class DeepLinkService {
  // Singleton pattern
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  // Stream controller for deep links
  final StreamController<String> _deepLinkStreamController = StreamController<String>.broadcast();
  Stream<String> get deepLinkStream => _deepLinkStreamController.stream;

  // Flag to track initialization
  bool _isInitialized = false;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('Initializing deep link service with app_links');

      // إنشاء كائن AppLinks
      final appLinks = AppLinks();

      // التعامل مع الرابط الأولي (عند فتح التطبيق من رابط)
      final initialLink = await appLinks.getInitialAppLink();
      debugPrint('Initial deep link: $initialLink');

      if (initialLink != null) {
        _handleDeepLink(initialLink.toString());
      }

      // التعامل مع الروابط عندما يكون التطبيق قيد التشغيل بالفعل
      appLinks.uriLinkStream.listen((Uri? uri) {
        debugPrint('Received link from stream: $uri');
        if (uri != null) {
          _handleDeepLink(uri.toString());
        }
      }, onError: (error) {
        debugPrint('Deep link stream error: $error');
      });

      _isInitialized = true;
      debugPrint('Deep link service initialized successfully');
    } on PlatformException catch (e) {
      debugPrint('Deep link initialization error: $e');
    } catch (e) {
      debugPrint('Unexpected error during deep link initialization: $e');
    }
  }

  // Handle deep link
  void _handleDeepLink(String link) {
    debugPrint('Received deep link: $link');
    _deepLinkStreamController.add(link);
  }

  // Test a deep link (for internal testing)
  void testDeepLink(String link) {
    debugPrint('Testing deep link: $link');
    _deepLinkStreamController.add(link);
  }

  // Parse job ID from deep link
  int? parseJobId(String link) {
    debugPrint('Parsing job ID from link: $link');

    // Handle various URL formats
    // https://wzzff.com/job/1183773-wa or https://wzzff.com/job/1183773
    final RegExp jobRegExp = RegExp(r'wzzff\.com\/job\/(\d+)(?:-\w+)?');
    // wzzff://job/1183773
    final RegExp appJobRegExp = RegExp(r'wzzff:\/\/job\/(\d+)');
    // Handle potential URL encoding
    final RegExp encodedJobRegExp = RegExp(r'wzzff\.com%2Fjob%2F(\d+)');

    Match? match = jobRegExp.firstMatch(link);
    match ??= appJobRegExp.firstMatch(link);
    match ??= encodedJobRegExp.firstMatch(link);

    if (match != null && match.groupCount >= 1) {
      final jobId = int.tryParse(match.group(1)!);
      debugPrint('Extracted job ID: $jobId');
      return jobId;
    }

    debugPrint('No job ID found in link');
    return null;
  }

  // Parse article ID from deep link
  int? parseArticleId(String link) {
    debugPrint('Parsing article ID from link: $link');

    // Handle various URL formats
    // https://wzzff.com/blog/show/19
    final RegExp articleRegExp = RegExp(r'wzzff\.com\/blog\/show\/(\d+)');
    // wzzff://blog/19
    final RegExp appArticleRegExp = RegExp(r'wzzff:\/\/blog\/(\d+)');
    // Handle potential URL encoding
    final RegExp encodedArticleRegExp = RegExp(r'wzzff\.com%2Fblog%2Fshow%2F(\d+)');

    Match? match = articleRegExp.firstMatch(link);
    match ??= appArticleRegExp.firstMatch(link);
    match ??= encodedArticleRegExp.firstMatch(link);

    if (match != null && match.groupCount >= 1) {
      final articleId = int.tryParse(match.group(1)!);
      debugPrint('Extracted article ID: $articleId');
      return articleId;
    }

    debugPrint('No article ID found in link');
    return null;
  }

  // Parse job news ID from deep link
  int? parseJobNewsId(String link) {
    debugPrint('تحليل معرف خبر الوظيفة من الرابط: $link');

    // Handle various URL formats
    // https://wzzff.com/jobs_news/1
    final RegExp jobNewsRegExp = RegExp(r'wzzff\.com\/jobs_news\/(\d+)');
    // wzzff://jobs_news/1
    final RegExp appJobNewsRegExp = RegExp(r'wzzff:\/\/jobs_news\/(\d+)');
    // Handle potential URL encoding
    final RegExp encodedJobNewsRegExp = RegExp(r'wzzff\.com%2Fjobs_news%2F(\d+)');

    Match? match = jobNewsRegExp.firstMatch(link);
    match ??= appJobNewsRegExp.firstMatch(link);
    match ??= encodedJobNewsRegExp.firstMatch(link);

    if (match != null && match.groupCount >= 1) {
      final jobNewsId = int.tryParse(match.group(1)!);
      debugPrint('تم استخراج معرف خبر الوظيفة: $jobNewsId');
      return jobNewsId;
    }

    debugPrint('لم يتم العثور على معرف خبر وظيفة في الرابط');
    return null;
  }

  // Dispose resources
  void dispose() {
    _deepLinkStreamController.close();
  }
}
