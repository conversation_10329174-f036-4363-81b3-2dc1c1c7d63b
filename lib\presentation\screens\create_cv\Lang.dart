class Lang {
  static const Map ar = {
    "first_name": "الاسم الاول",
    "label_first_name": "مصطفي عبد العال",
    "first_name_hint": "الاسم الأول",
    "last_name": "اسم العائلة",
    "last_name_hint": "اسم العائلة",
    "location": "العنوان",
    "location_hint": "1 شارع الجمهورية, القاهرة، مصر",
    "label_location": "1 شارع الجمهورية, القاهرة، مصر",
    "choose_image": "اختيار الصورة الشخصية",
    "basic_info": "معلومات اساسية",
    "job_title": "المسمي الوظيفي",
    "job_title_hint": 'مصمم جرافيك',
    "job_name": "المسمى الوظيفي",
    "job_name_hint": "مصمم جرافيك",
    "nationality": "الجنسية",
    "nationality_hint": "مصري",
    "phone": "رقم الهاتف",
    "phone_hint": "+20123456789",
    "email": "البريد الالكتروني",
    "email_hint": "<EMAIL>",
    "next": "التالي",
    "edu": "التعليم",
    "about_me": "نبذة عني",
    "edu_hint": " حاصل علي بكالوريس تجارة ",
    "about_hint": " معلومات عن مسيراتك المهنية باختصار ",
    "exp_from": "بداية الخبرة/العمل",
    "exp_end": "نهاية الخبرة/العمل",
    "choose": "اختر",
    "add_new_exp": " حفظ الخبرة ",
    "exp_title": "اسم الخبرة / الوظيفة",
    "exp_des": "وصف الخبرة / الوظيفة",
    "exp_company_name": "اسم الشركة / مكان الخبرة",
    "exp_title_hint": "مهندس انتاج",
    "exp_company_name_hint": "المتحدة لانتاج الغاز",
    "exp_des_hint": "   كنت مسئولأ عن القيام بكل من اعمال ....  ",
    "edu_from": "بداية الدراسة",
    "edu_end": "نهاية الدراسة",
    "edu_des": "وصف الشهادة الجامعية / الدورة التعليمية",
    "edu_title": "اسم الشهادة الجامعية / التعليمية",
    "edu_level": "التقدير العلمي",
    "edu_place_name": "اسم / مكان/ جهة الحصول على الشهادة",
    "add_more_edu": "اضافة شهادة اضافية",
    "edu_place_name_hint": "المركز الثقافي الروسي",
    "level": "مستوي الاجادة",
    "skill": "المهارة",
    "lang": "اللغة",
    "add_new_lang": "اضف لغة",
    "add_new_skill": "احفظ المهارة",
    "add_new_course": "كورس اضافي",
    "c_from": "بداية الكورس",
    "c_end": "نهاية الكورس",
    "c_des": "وصف الكورس",
    "c_title": "عنوان الكورس",
    "course_place_name": "اسم / مكان/ جهة الحصول على الكورس",
    "face": "فيسبوك",
    "twt": "توتير",
    "linked_in": "لينكد ان",
    "get_reusme": "الحصول علي السيرة",
    "additional_info": "معلومات اضافيه",
    "experience": "الخبرات",
    "education_courses": "التعليم والشهادات",
    "languages_skills": "اللغات والمهادات",
    "courses": "الكورسات",
    "languages": "اللغات",
    "skills": "المهارات",
    "tips": "نصائح",
    "age": "العمر",
    "social_status": "الحالة الاجتماعية",
    "summary": "نبذة شخصية",
    "summary_hint": "اكتب نبذة مختصرة عن خبراتك ومهاراتك",
    "behance": "بيهانس",
    // نصائح للمعلومات الأساسية
    "basic_info_tip1": "استخدم اسمك الكامل كما يظهر في الوثائق الرسمية",
    "basic_info_tip2": "اكتب المسمى الوظيفي الذي تطمح إليه بشكل واضح ودقيق",
    "basic_info_tip3": "تأكد من صحة بيانات الاتصال (البريد الإلكتروني ورقم الهاتف)",
    "basic_info_tip4": "اختر صورة احترافية ذات خلفية محايدة وإضاءة جيدة",
    // نصائح للمعلومات الإضافية
    "additional_info_tip1": "أضف روابط حساباتك المهنية على مواقع التواصل الاجتماعي",
    "additional_info_tip2": "اكتب نبذة شخصية مختصرة تعكس مهاراتك وخبراتك",
    "additional_info_tip3": "تأكد من أن روابط حساباتك المهنية صحيحة وتعمل",
    "additional_info_tip4": "اختر الحالة الاجتماعية والعمر المناسبين لك",
    // نصائح للخبرات
    "experience_tip1": "رتب خبراتك من الأحدث إلى الأقدم",
    "experience_tip2": "استخدم أفعال قوية لوصف مسؤولياتك وإنجازاتك",
    "experience_tip3": "اذكر إنجازاتك الملموسة وتأثيرها على الشركة",
    "experience_tip4": "كن دقيقاً في ذكر فترات العمل والمسميات الوظيفية",
    // نصائح للتعليم
    "education_tip1": "اذكر أعلى شهادة علمية حصلت عليها",
    "education_tip2": "أضف التخصص والتقدير إذا كان مميزاً",
    "education_tip3": "أضف الدورات والشهادات المهنية المرتبطة بمجال عملك",
    "education_tip4": "رتب شهاداتك من الأحدث إلى الأقدم",
    // نصائح للدورات
    "courses_tip1": "ركز على الدورات المرتبطة بمجال عملك أو الوظيفة المستهدفة",
    "courses_tip2": "اذكر الجهة المانحة للشهادة والتاريخ",
    "courses_tip3": "أضف وصفاً مختصراً لمحتوى الدورة وما تعلمته منها",
    "courses_tip4": "أضف الشهادات المعتمدة دولياً إن وجدت",
    // نصائح للغات
    "languages_tip1": "حدد مستوى إتقانك للغة بدقة",
    "languages_tip2": "اذكر الشهادات اللغوية الرسمية إن وجدت",
    "languages_tip3": "ابدأ باللغة الأم ثم اللغات الأخرى حسب مستوى الإتقان",
    "languages_tip4": "لا تبالغ في تقييم مستوى إتقانك للغات",
    // نصائح للمهارات
    "skills_tip1": "ركز على المهارات المرتبطة بمجال عملك",
    "skills_tip2": "اذكر المهارات التقنية والشخصية",
    "skills_tip3": "كن صادقاً في تقييم مستوى مهاراتك",
    "skills_tip4": "أضف المهارات التي تميزك عن غيرك من المتقدمين",
    "fill_all_fields": "يرجى ملء جميع الحقول المطلوبة",
    "save_changes": "حفظ التغييرات",
    "edit_experience": "تعديل الخبرة",
    "added_experiences": "الخبرات المضافة",
    "experience_added": "تم إضافة الخبرة بنجاح",
    "experience_updated": "تم تحديث الخبرة بنجاح",
    "save_and_continue": "حفظ ومتابعة",
    "add_at_least_one_exp": "يجب إضافة خبرة واحدة على الأقل للاستمرار",
    "education_added": "تمت إضافة التعليم بنجاح",
    "education_updated": "تم تعديل التعليم بنجاح",
    "add_at_least_one_edu": "يجب إضافة شهادة تعليمية واحدة على الأقل للاستمرار",
    "course_added": "تمت إضافة الدورة بنجاح",
    "course_updated": "تم تعديل الدورة بنجاح",
    "add_at_least_one_course": "يجب إضافة دورة واحدة على الأقل للاستمرار",
    "language_added": "تمت إضافة اللغة بنجاح",
    "language_updated": "تم تعديل اللغة بنجاح",
    "add_at_least_one_lang": "يجب إضافة لغة واحدة على الأقل للاستمرار",
    "skill_added": "تمت إضافة المهارة بنجاح",
    "skill_updated": "تم تعديل المهارة بنجاح",
    "add_at_least_one_skill": "يجب إضافة مهارة واحدة على الأقل للاستمرار",
    "full_name": "الاسم بالكامل",
    "full_name_hint": "الاسم بالكامل",
    "basic_info_saved": "تم حفظ المعلومات الأساسية بنجاح",
    "additional_info_saved": "تم حفظ المعلومات الإضافية بنجاح",
    "experiences_saved": "تم حفظ الخبرات بنجاح",
    "education_saved": "تم حفظ التعليم بنجاح",
    "courses_saved": "تم حفظ الدورات بنجاح",
    "languages_saved": "تم حفظ اللغات بنجاح",
    "skills_saved": "تم حفظ المهارات بنجاح",
    "profile_picture_required": "الصورة الشخصية مطلوبة لإكمال السيرة الذاتية",
    "attention": "تنبيه",
    "unsaved_experience_data": "لديك بيانات خبرة غير محفوظة. هل تريد إضافتها قبل المتابعة؟",
    "yes_add_continue": "نعم، أضف وتابع",
    "no_just_continue": "لا، تابع فقط",
    "unsaved_education_data": "لديك بيانات تعليم غير محفوظة. هل تريد إضافتها قبل المتابعة؟",
    "unsaved_course_data": "لديك بيانات دورة غير محفوظة. هل تريد إضافتها قبل المتابعة؟",
    "unsaved_language_data": "لديك بيانات لغة غير محفوظة. هل تريد إضافتها قبل المتابعة؟",
    "unsaved_skill_data": "لديك بيانات مهارة غير محفوظة. هل تريد إضافتها قبل المتابعة؟",
    // مفاتيح إضافية للخطوات
    "edit_language": "تعديل اللغة",
    "add_new_language": "حفظ اللغة",
    "edit_skill": "تعديل المهارة",
    "add_skill": "حفظ المهارة",
    "language_hint": "مثال: العربية، الإنجليزية ...",
    "skill_hint": "مثال: برمجة، تصميم ...",
    "add_education": "حفظ الشهادة",
    "add_language": "إضافة لغة",
    "added_education": "الشهادات التعليمية المضافة",
    // مفاتيح صفحة إضافة قالب جديد
    "add_new_template": "اطلب جديد",
    "template_name": "اسم القالب",
    "template_name_hint": "أدخل اسم القالب",
    "template_desc": "وصف القالب",
    "template_desc_hint": "أدخل وصفاً مختصراً للقالب",
    "template_image": "صورة القالب",
    "upload_template_image": "رفع صورة القالب",
    "save_template": "ارسال الطلب",
    "cancel": "إلغاء",
    "contact_us": "اتصل بنا",
    "contact_us_tips": "نصائح قبل التواصل معنا:",
    "contact_us_tip1": "يرجى كتابة بياناتك بشكل صحيح لنتمكن من الرد عليك.",
    "contact_us_tip2": "استخدم بريدًا إلكترونيًا فعالاً لاستلام الرد.",
    "contact_us_tip3": "وضح مشكلتك أو استفسارك بشكل مختصر وواضح.",
    "contact_us_name": "الاسم",
    "contact_us_email": "البريد الإلكتروني",
    "contact_us_phone": "رقم الهاتف",
    "contact_us_message": "رسالتك",
    "contact_us_send": "إرسال",
    "contact_us_success": "تم إرسال رسالتك بنجاح! سنقوم بالرد عليك قريبًا.",
    "contact_us_error": "حدث خطأ أثناء إرسال الرسالة. حاول مرة أخرى.",
  };
  static const Map en = {
    "first_name": "First Name",
    "label_first_name": "Ehab Elzeny",
    "first_name_hint": "First Name",
    "last_name": "Last Name",
    "last_name_hint": "Last Name",
    "location": "Location",
    "location_hint": "14 St Alharam, Giza, Egypt",
    "label_location": "14 St Alharam, Giza, Egypt",
    "choose_image": " choose Profile Image ",
    "basic_info": "Basic Information",
    "job_title": "Job Title",
    "job_title_hint": 'Photographer',
    "job_name": "Job Title",
    "job_name_hint": "Photographer",
    "nationality": "Nationality",
    "nationality_hint": "Egyptian",
    "phone": "Phone",
    "phone_hint": "+20123456789",
    "email": "Email",
    "email_hint": "<EMAIL>",
    "next": "Next",
    "skills": "Skills",
    "languages": "Languages",
    "edu": "Education",
    "add_new_education": "",
    "about_me": "About Me",
    "edu_hint": "Bachelor's degree",
    "about_hint": "  About You Infroramtions  ",
    "exp_from": "Experience From",
    "exp_end": "Experience End",
    "choose": "Choose",
    "add_new_exp": "Add New Experience",
    "exp_title": "Experience Title",
    "exp_des": "Description",
    "exp_company_name": "Company Name",
    "exp_title_hint": "Production Engineer",
    "exp_company_name_hint": "Amazon.com Inc",
    "exp_des_hint": "   .....   ",
    "edu_from": "From Date",
    "edu_end": "To Date",
    "edu_des": "Details",
    "edu_title": "Specialization/Degree",
    "edu_level": "Level",
    "edu_place_name": "School/University/Institute",
    "add_more_edu": "Add One More Education",
    "edu_place_name_hint": "Russian Cultural Center",
    "level": "Level",
    "skill": "Skill",
    "lang": "Language",
    "add_new_lang": "One More Lang",
    "add_new_skill": "Save Skill",
    "add_new_course": "Add One More course",
    "c_from": "From Date",
    "c_end": "To Date",
    "c_des": "Details",
    "c_title": "Course Title",
    "course_place_name": "Russian Cultural Center",
    "face": "Facebook",
    "twt": "Twitter",
    "linked_in": "Linked In",
    "get_reusme": "Download Resume",
    "additional_info": "Additional Information",
    "experience": "Experience",
    "education_courses": "Education & Certificates",
    "courses": "Courses",
    "languages_skills": "Languages & Skills",
    "tips": "Tips",
    "age": "Age",
    "social_status": "Social Status",
    "summary": "Summary",
    "summary_hint": "Write a brief summary about your skills and experience",
    "behance": "Behance",
    // Basic Information Tips
    "basic_info_tip1": "Use your full name as it appears in official documents",
    "basic_info_tip2": "Write the job title you aspire to clearly and precisely",
    "basic_info_tip3": "Ensure your contact information (email and phone) is correct",
    "basic_info_tip4": "Choose a professional photo with a neutral background and good lighting",
    // Additional Information Tips
    "additional_info_tip1": "Add links to your professional social media accounts",
    "additional_info_tip2": "Write a brief personal summary that reflects your skills and experience",
    "additional_info_tip3": "Make sure your professional account links are correct and working",
    "additional_info_tip4": "Choose the appropriate age and social status for you",
    // Experience Tips
    "experience_tip1": "Arrange your experiences from newest to oldest",
    "experience_tip2": "Use strong action verbs to describe your responsibilities and achievements",
    "experience_tip3": "Mention your tangible achievements and their impact on the company",
    "experience_tip4": "Be accurate in mentioning work periods and job titles",
    // Education Tips
    "education_tip1": "Mention the highest academic degree you have obtained",
    "education_tip2": "Add specialization and grade if it is distinguished",
    "education_tip3": "Add professional courses and certificates related to your field of work",
    "education_tip4": "Arrange your certificates from newest to oldest",
    // Courses Tips
    "courses_tip1": "Focus on courses related to your field of work or target job",
    "courses_tip2": "Mention the certificate issuing authority and date",
    "courses_tip3": "Add a brief description of the course content and what you learned from it",
    "courses_tip4": "Add internationally accredited certificates if available",
    // Languages Tips
    "languages_tip1": "Accurately determine your language proficiency level",
    "languages_tip2": "Mention official language certificates if available",
    "languages_tip3": "Start with your native language then other languages according to proficiency level",
    "languages_tip4": "Don't exaggerate in assessing your language proficiency level",
    // Skills Tips
    "skills_tip1": "Focus on skills related to your field of work",
    "skills_tip2": "Mention technical and personal skills",
    "skills_tip3": "Be honest in assessing your skill level",
    "skills_tip4": "Add skills that distinguish you from other applicants",
    "fill_all_fields": "Please fill all fields",
    "save_changes": "Save Changes",
    "edit_experience": "Edit Experience",
    "added_experiences": "Added Experiences",
    "experience_added": "Experience added successfully",
    "experience_updated": "Experience updated successfully",
    "save_and_continue": "Save and Continue",
    "add_at_least_one_exp": "Please add at least one experience to continue",
    "education_added": "Education added successfully",
    "education_updated": "Education updated successfully",
    "add_at_least_one_edu": "Please add at least one education to continue",
    "course_added": "Course added successfully",
    "course_updated": "Course updated successfully",
    "add_at_least_one_course": "Please add at least one course to continue",
    "language_added": "Language added successfully",
    "language_updated": "Language updated successfully",
    "add_at_least_one_lang": "Please add at least one language to continue",
    "skill_added": "Skill added successfully",
    "skill_updated": "Skill updated successfully",
    "add_at_least_one_skill": "Please add at least one skill to continue",
    "full_name": "Full Name",
    "full_name_hint": "Full Name",
    "basic_info_saved": "Basic information saved successfully",
    "additional_info_saved": "Additional information saved successfully",
    "experiences_saved": "Experiences saved successfully",
    "education_saved": "Education saved successfully",
    "courses_saved": "Courses saved successfully",
    "languages_saved": "Languages saved successfully",
    "skills_saved": "Skills saved successfully",
    "profile_picture_required": "Profile picture is required to complete the CV",
    "attention": "Attention",
    "unsaved_experience_data": "You have unsaved experience data. Do you want to add it before continuing?",
    "yes_add_continue": "Yes, add and continue",
    "no_just_continue": "No, just continue",
    "unsaved_education_data": "You have unsaved education data. Do you want to add it before continuing?",
    "unsaved_course_data": "You have unsaved course data. Do you want to add it before continuing?",
    "unsaved_language_data": "You have unsaved language data. Do you want to add it before continuing?",
    "unsaved_skill_data": "You have unsaved skill data. Do you want to add it before continuing?",
    // مفاتيح إضافية للخطوات
    "edit_language": "Edit Language",
    "add_new_language": "Add New Language",
    "edit_skill": "Edit Skill",
    "add_skill": "Add Skill",
    "language_hint": "e.g. Arabic, English ...",
    "skill_hint": "e.g. Programming, Design ...",
    "add_education": "Add Education",
    "add_language": "Add Language",
    "added_education": "Added Education",
    // New Template Form keys
    "add_new_template": "Add New Template",
    "template_name": "Template Name",
    "template_name_hint": "Enter template name",
    "template_desc": "Template Description",
    "template_desc_hint": "Enter a brief description of the template",
    "template_image": "Template Image",
    "upload_template_image": "Upload Template Image",
    "save_template": "Save Template",
    "cancel": "Cancel",
    "contact_us": "Contact Us",
    "contact_us_tips": "Tips before contacting us:",
    "contact_us_tip1": "Please enter your data correctly so we can respond.",
    "contact_us_tip2": "Use an active email to receive our reply.",
    "contact_us_tip3": "Describe your issue or inquiry clearly and briefly.",
    "contact_us_name": "Name",
    "contact_us_email": "Email",
    "contact_us_phone": "Phone",
    "contact_us_message": "Your Message",
    "contact_us_send": "Send",
    "contact_us_success": "Your message has been sent successfully! We will reply soon.",
    "contact_us_error": "An error occurred while sending the message. Please try again.",
  };
  String getWord(keyName, ltrOrNot) {
    if (ltrOrNot == "1") {
      if (en[keyName] != null) {
        return en[keyName];
      }
      return keyName;
    } else {
      if (ar[keyName] != null) {
        return ar[keyName];
      }
      return keyName;
    }
  }
}
