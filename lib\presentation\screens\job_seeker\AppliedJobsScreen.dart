﻿import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/models/JobApplication.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wzzff/services/google_ad_service.dart';
// import 'package:wzzff/Apis/ProfileApi.dart';
import 'package:wzzff/Apis/JobsApi.dart';

class AppliedJobsScreen extends StatefulWidget {
  const AppliedJobsScreen({super.key});

  @override
  State<AppliedJobsScreen> createState() => _AppliedJobsScreenState();
}

class _AppliedJobsScreenState extends State<AppliedJobsScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  final List<JobApplication> _appliedJobs = [];
  String _errorMessage = '';
  late TabController _tabController;
  final GoogleAdService _adService = GoogleAdService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAppliedJobs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAppliedJobs() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final storage = FlutterSecureStorage();
      final apiToken = await storage.read(key: 'api_token');
      if (apiToken == null || apiToken.isEmpty) {
        // مستخدم ضيف: جلب الوظائف من التخزين المحلي
        final prefs = await SharedPreferences.getInstance();
        final List<String> jobs = prefs.getStringList('applied_jobs_guest') ?? [];
        final List<JobApplication> guestJobs = jobs.map((e) {
          final data = Map<String, dynamic>.from(jsonDecode(e));
          final job = data['job'] ?? {};
          final application = data['application'] ?? {};
          return JobApplication(
            id: 0, // ظ‚ظٹظ…ط© ط§ظپطھط±ط§ط¶ظٹط© ظ„ظ„ط¶ظٹظپ
            slug: job['slug'] ?? '',
            title: job['title'] ?? '',
            company_name: job['company_name'] ?? '',
            city_name: job['city_name'] ?? '',
            country_name: job['country_name'] ?? '',
            applied_date: application['applied_date'] ?? '',
            status: ApplicationStatus.pending,
          );
        }).toList();
        guestJobs.sort((a, b) {
          final aDate = DateTime.tryParse(a.applied_date) ?? DateTime(1970);
          final bDate = DateTime.tryParse(b.applied_date) ?? DateTime(1970);
          return bDate.compareTo(aDate);
        });
        setState(() {
          _appliedJobs.clear();
          _appliedJobs.addAll(guestJobs);
          _isLoading = false;
        });
      } else {
        // مستخدم مسجل: جلب الوظائف من السيرفر
        // final profileApi = ProfileApi();
        // final jobs = await profileApi.getAppliedJobs();
        final jobs = <JobApplication>[];
        setState(() {
          _appliedJobs.clear();
          _appliedJobs.addAll(jobs);
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل الوظائف: $e';
        _isLoading = false;
      });
    }
  }

  Widget _buildBody() {
    return FutureBuilder<String?>(
      future: FlutterSecureStorage().read(key: 'api_token'),
      builder: (context, snapshot) {
        final isGuest = (snapshot.data == null || snapshot.data!.isEmpty);
        return Column(
          children: [
            if (isGuest)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 8, top: 8, right: 8, left: 8),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amber.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.info_outline, color: Colors.amber, size: 22),
                    const SizedBox(width: 10),
                    Expanded(
                      child:                     Text(
                      'لتحديث حالات الوظائف المقدم عليها سجل دخول الآن',
                        style: GoogleFonts.tajawal(
                          color: Colors.amber[900],
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            // Container(
            //   alignment: Alignment.center,
            //   margin: const EdgeInsets.only(top: 8, bottom: 8),
            //   child: _adService.createBannerAdWidget(),
            // ),
            Expanded(child: _buildBodyContent()),
          ],
        );
      },
    );
  }

  Widget _buildBodyContent() {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.primary));
    }
    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).brightness == Brightness.dark ? Colors.red[300] : Colors.red,
              size: 48
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : null,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadAppliedJobs,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
              child: Text('إعادة المحاولة', style: GoogleFonts.tajawal()),
            ),
          ],
        ),
      );
    }
    if (_appliedJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_off,
              color: Theme.of(context).brightness == Brightness.dark ? Colors.grey[400] : Colors.grey,
              size: 48
            ),
            const SizedBox(height: 16),
            Text(
              'لم تتقدم لأي وظيفة بعد',
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : null,
              ),
            ),
          ],
        ),
      );
    }
    return TabBarView(
      controller: _tabController,
      children: [
        _buildJobsList(_appliedJobs),
        _buildJobsList(_appliedJobs.where((job) =>
          job.status == ApplicationStatus.pending || job.status == ApplicationStatus.viewed).toList()),
        _buildJobsList(_appliedJobs.where((job) =>
          job.status == ApplicationStatus.accepted || job.status == ApplicationStatus.rejected).toList()),
      ],
    );
  }

  Widget _buildJobsList(List<JobApplication> jobs) {
    return jobs.isEmpty
        ? Center(
            child: Text(
              'لا توجد وظائف في هذه القائمة',
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : null,
              ),
            ),
          )
        : LayoutBuilder(
            builder: (context, constraints) {
              return RefreshIndicator(
                onRefresh: _loadAppliedJobs,
                color: Theme.of(context).colorScheme.primary,
                child: GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: constraints.maxWidth > 600 ? 2 : 1,
                    childAspectRatio: constraints.maxWidth > 600 ? 2.5 : 1.8,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: jobs.length,
                  itemBuilder: (context, index) {
                    final job = jobs[index];
                    return _buildJobCard(job);
                  },
                ),
              );
            }
          );
  }

  Widget _buildJobCard(JobApplication job) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return FutureBuilder<String?>(
      future: FlutterSecureStorage().read(key: 'api_token'),
      builder: (context, snapshot) {
        final isGuest = (snapshot.data == null || snapshot.data!.isEmpty);
        return Card(
          elevation: 3,
          clipBehavior: Clip.antiAliasWithSaveLayer,
          color: isDark ? Theme.of(context).cardTheme.color : null,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: InkWell(
            onTap: () async {
              if (isGuest) {
                // جلب بيانات الوظيفة من SharedPreferences للضيف
                final prefs = await SharedPreferences.getInstance();
                final List<String> jobs = prefs.getStringList('applied_jobs_guest') ?? [];
                final jobData = jobs.firstWhere(
                  (e) {
                    final data = Map<String, dynamic>.from(jsonDecode(e));
                    final jobMap = data['job'] ?? {};
                    return (jobMap['slug'] ?? '') == job.slug;
                  },
                  orElse: () => '',
                );
                if (jobData.isNotEmpty) {
                  final data = Map<String, dynamic>.from(jsonDecode(jobData));
                  final jobMap = data['job'] ?? {};
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => DetailJobScreen(
                        title: jobMap['title'] ?? '',
                        des: jobMap['des'] ?? '',
                        code_address: jobMap['code_address'] ?? '',
                        email: jobMap['email'],
                        number: jobMap['number'],
                        salary_currency: jobMap['salary_currency'] ?? '',
                        salary: jobMap['salary'],
                        cat: jobMap['cat'] ?? '',
                        gender: jobMap['gender'] ?? '',
                        state_name: jobMap['state_name'] ?? '',
                        country_name: jobMap['country_name'] ?? '',
                        job_type_name: jobMap['job_type_name'] ?? '',
                        city_name: jobMap['city_name'] ?? '',
                        company_name: jobMap['company_name'] ?? '',
                        edu: jobMap['edu'] ?? '',
                        exp: jobMap['exp'] ?? '',
                        end_at: jobMap['end_at'] ?? '',
                        time: jobMap['time'] ?? '',
                        created_at_date: jobMap['created_at_date'] ?? '',
                        slug: jobMap['slug'] ?? '',
                      ),
                    ),
                  );
                }
              } else {
                // جلب بيانات الوظيفة من السيرفر للمستخدم المسجل
                try {
                  final jobsApi = JobsApi();
                  final jobDetails = await JobsApi.getJobDetails(jobId: job.id.toString());
                  
                  if (jobDetails != null && context.mounted) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DetailJobScreen(
                          title: jobDetails.title,
                          des: jobDetails.description,
                          code_address: jobDetails.code_address,
                          email: jobDetails.email,
                          number: jobDetails.number,
                          salary_currency: jobDetails.salary_currency,
                          salary: jobDetails.salary,
                          cat: jobDetails.cat,
                          gender: jobDetails.gender,
                          state_name: jobDetails.state_name,
                          country_name: jobDetails.country_name,
                          job_type_name: jobDetails.job_type_name,
                          city_name: jobDetails.city_name,
                          company_name: jobDetails.company_name,
                          edu: jobDetails.edu,
                          exp: jobDetails.exp,
                          end_at: jobDetails.end_at,
                          time: jobDetails.time,
                          created_at_date: jobDetails.created_at_date,
                          slug: jobDetails.slug,
                        ),
                      ),
                    );
                  } else {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'لم يتم العثور على تفاصيل الوظيفة',
                            style: GoogleFonts.tajawal(),
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'حدث خطأ أثناء جلب تفاصيل الوظيفة',
                          style: GoogleFonts.tajawal(),
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(14),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 44,
                        height: 44,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(isDark ? 0.18 : 0.09),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.work_outline,
                          color: Theme.of(context).colorScheme.primary,
                          size: 26,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              job.title,
                              style: GoogleFonts.tajawal(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                                color: isDark
                                    ? Theme.of(context).textTheme.titleMedium?.color
                                    : const Color(0xFF333333),
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            if (job.company_name.isNotEmpty)
                              Text(
                                job.company_name,
                                style: GoogleFonts.tajawal(
                                  color: isDark
                                      ? Theme.of(context).textTheme.bodyMedium?.color
                                      : Colors.grey[700],
                                  fontSize: 13,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            if (job.city_name.isNotEmpty || job.country_name.isNotEmpty)
                              Row(
                                children: [
                                  const Icon(Icons.location_on, size: 13, color: Colors.grey),
                                  const SizedBox(width: 3),
                                  Expanded(
                                    child: Text(
                                      '${job.city_name}${job.city_name.isNotEmpty && job.country_name.isNotEmpty ? ', ' : ''}${job.country_name}',
                                      style: GoogleFonts.tajawal(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            if (job.applied_date.isNotEmpty)
                              Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 13, color: Colors.grey),
                                  const SizedBox(width: 3),
                                  Text(
                                    job.applied_date,
                                    style: GoogleFonts.tajawal(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          color: job.status.color.withOpacity(0.09),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: job.status.color.withOpacity(0.22),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getStatusIcon(job.status),
                              size: 15,
                              color: job.status.color,
                            ),
                            const SizedBox(width: 5),
                            Text(
                              job.status.name,
                              style: GoogleFonts.tajawal(
                                color: job.status.color,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      TextButton.icon(
                        icon: const Icon(Icons.description_outlined, size: 16),
                        label: const Text('بيانات التقديم', style: TextStyle(fontSize: 12)),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          minimumSize: Size(0, 32),
                        ),
                        onPressed: () async {
                          await _showApplicationFormDialog(context, job, isGuest);
                        },
                      ),
                    ],
                  ),
                  if (job.status == ApplicationStatus.accepted && job.interview_date != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 7),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.09),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.green.withOpacity(0.22),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.event_available, size: 16, color: Colors.green),
                            const SizedBox(width: 7),
                            Text(
                              'موعد المقابلة: ${job.interview_date}',
                              style: GoogleFonts.tajawal(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _showApplicationFormDialog(BuildContext context, JobApplication job, bool isGuest) async {
    if (isGuest) {
              // جلب بيانات التقديم من SharedPreferences للضيف
      final prefs = await SharedPreferences.getInstance();
      final List<String> jobs = prefs.getStringList('applied_jobs_guest') ?? [];
      final jobData = jobs.firstWhere(
        (e) {
          final data = Map<String, dynamic>.from(jsonDecode(e));
          final jobMap = data['job'] ?? {};
          return (jobMap['slug'] ?? '') == job.slug;
        },
        orElse: () => '',
      );
      if (jobData.isNotEmpty) {
        final data = Map<String, dynamic>.from(jsonDecode(jobData));
        final applicationData = data['application'] ?? {};
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
                      title: Text(
            'بيانات التقديم',
            style: GoogleFonts.tajawal(fontWeight: FontWeight.bold),
          ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow('العنوان', applicationData['title'] ?? ''),
                  _buildInfoRow('البريد الإلكتروني', applicationData['email'] ?? ''),
                  _buildInfoRow('رقم الهاتف', applicationData['phone'] ?? ''),
                  _buildInfoRow('الراتب المتوقع', applicationData['salary'] ?? ''),
                  _buildInfoRow('الوصف', applicationData['description'] ?? ''),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'ط¥ط؛ظ„ط§ظ‚',
                  style: GoogleFonts.tajawal(),
                ),
              ),
            ],
          ),
        );
      }
    } else {
      // عرض بيانات التقديم للمستخدم المسجل
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'بيانات التقديم',
            style: GoogleFonts.tajawal(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('العنوان', job.application_title ?? ''),
                _buildInfoRow('البريد الإلكتروني', job.application_email ?? ''),
                _buildInfoRow('رقم الهاتف', job.application_phone ?? ''),
                _buildInfoRow('الراتب المتوقع', job.application_salary?.toString() ?? ''),
                _buildInfoRow('الوصف', job.application_description ?? ''),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'ط¥ط؛ظ„ط§ظ‚',
                style: GoogleFonts.tajawal(),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.tajawal(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.tajawal(),
          ),
          const Divider(),
        ],
      ),
    );
  }

  IconData _getStatusIcon(ApplicationStatus status) {
    switch (status) {
      case ApplicationStatus.pending:
        return Icons.hourglass_empty;
      case ApplicationStatus.viewed:
        return Icons.visibility;
      case ApplicationStatus.accepted:
        return Icons.check_circle;
      case ApplicationStatus.rejected:
        return Icons.cancel;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl, // تغيير اتجاه الصفحة من اليمين إلى اليسار
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : Colors.white,
        appBar: AppBar(
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_forward),
              tooltip: 'رجوع',
            ),
          ],
          title: Text(
            'وظائف تقدمت لها',
            style: GoogleFonts.tajawal(
              color: Colors.white,
              //fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Theme.of(context).colorScheme.primary,
          centerTitle: true,
          elevation: 0,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(48),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: TabBar(
                controller: _tabController,
                indicatorColor: Theme.of(context).colorScheme.primary,
                indicatorWeight: 3,
                labelColor: Theme.of(context).colorScheme.primary,
                unselectedLabelColor: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[400]
                    : Colors.grey[600],
                labelStyle: GoogleFonts.tajawal(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                unselectedLabelStyle: GoogleFonts.tajawal(
                  fontSize: 14,
                ),
                tabs: const [
                  Tab(text: 'الكل'),
                  Tab(text: 'قيد المراجعة'),
                  Tab(text: 'تم الرد'),
                ],
              ),
            ),
          ),
        ),
        body: _buildBody(),
      ),
    );
  }
}









