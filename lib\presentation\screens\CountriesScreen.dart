import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:list_load_more/list_load_more/list_load_more.dart';
import 'package:wzzff/presentation/screens/auth/LoginOrRegisterScreen.dart';
import 'package:wzzff/presentation/utils/Constants.dart' as Constants;
import 'package:wzzff/presentation/screens/SearchScreen.dart';
import 'package:wzzff/presentation/components/ListShimmer.dart';

import 'package:wzzff/presentation/components/countryImageName.dart';
import 'package:wzzff/presentation/widgets/custom_theme.dart';
import 'package:wzzff/presentation/widgets/custom_text_field.dart';
import 'package:wzzff/presentation/widgets/job_card.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/presentation/screens/jobs/LoadScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/Notifications.dart';
import 'package:wzzff/presentation/components/MyCustomCruve.dart';
import 'package:wzzff/presentation/widgets/job_card_applied.dart';
import 'package:wzzff/presentation/screens/jobs/LatestJobs.dart';
import 'package:wzzff/presentation/screens/articles/NewsPage.dart';
import 'package:wzzff/main.dart' show GoToAccountTabNotification;
import 'package:wzzff/presentation/screens/jobs/JobsByCountries.dart';

/// شاشة قائمة الدول مع نظام التتبع الذكي المتقدم
/// 
/// المميزات المضافة:
/// 🌍 تتبع استعراض قائمة الدول
/// 🎯 تتبع اختيار دولة معينة
/// 📊 تحليل التفضيلات الجغرافية للمستخدم
/// 🔍 تتبع البحث عن وظائف حسب الدولة
/// ⏱️ تتبع الوقت المقضي في التصفح الجغرافي
/// 📈 تحليل الأسواق المفضلة للعمل
/// 
/// هذا القسم مهم جداً لفهم:
/// - التفضيلات الجغرافية للمستخدمين
/// - الأسواق الأكثر طلباً
/// - أنماط البحث الجغرافي
/// - اهتمامات المستخدمين بأسواق عمل معينة

class CountriesGrid extends StatefulWidget {
  const CountriesGrid({super.key});

  @override
  State<CountriesGrid> createState() => _CountriesGridState();
}

class _CountriesGridState extends State<CountriesGrid> {
  static const List<Map<String, String>> countries = [
    {"name": "السعودية", "image": "sa"},
    {"name": "مصر", "image": "eg"},
    {"name": "قطر", "image": "qa"},
    {"name": "الامارات", "image": "em"},
    {"name": "الاردن", "image": "or"},
    {"name": "الكويت", "image": "ku"},
    {"name": "عمان", "image": "oman"},
    {"name": "البحرين", "image": "bahr"},
    {"name": "العراق", "image": "ir"},
    {"name": "لبنان", "image": "lebanon"},
    {"name": "الجزائر", "image": "ja"},
    {"name": "تونس", "image": "tu"},
    {"name": "السودان", "image": "sudan"},
    {"name": "سوريا", "image": "syria"},
    {"name": "اليمن", "image": "yeman"},
    {"name": "فلسطين", "image": "pl"},
    {"name": "ليبيا", "image": "libya"},
    {"name": "المغرب", "image": "mo"},
  ];

  final ScrollController _scrollController = ScrollController();
  bool _showHeader = true;
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.offset > 10 && _showHeader) {
      setState(() {
        _showHeader = false;
      });
    } else if (_scrollController.offset <= 10 && !_showHeader) {
      setState(() {
        _showHeader = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Scaffold(
      backgroundColor: isDarkMode ? Theme.of(context).scaffoldBackgroundColor : Colors.white,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [
                    primaryColor.withOpacity(0.13),
                    Theme.of(context).scaffoldBackgroundColor,
                  ]
                : [
                    primaryColor.withOpacity(0.10),
                    Colors.white,
                  ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // هيدر علوي متحرك
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: _showHeader ? null : 0,
                child: AnimatedOpacity(
                  opacity: _showHeader ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 200),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: isDarkMode
                            ? [
                                primaryColor.withOpacity(0.85),
                                const Color(0xFF232B3E),
                              ]
                            : [
                                primaryColor,
                                const Color(0xFF2196F3),
                              ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.07),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(24),
                        bottomRight: Radius.circular(24),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          height: 42,
                          width: 42,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(isDarkMode ? 0.10 : 0.13),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.public,
                            size: 22,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 14),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                "الدول",
                                style: GoogleFonts.tajawal(
                                  fontSize: 17,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                "اختر الدولة لعرض الوظائف المتاحة",
                                style: GoogleFonts.tajawal(
                                  fontSize: 13,
                                  color: Colors.white.withOpacity(0.85),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 18),
              // شبكة الدول
              Expanded(
                child: GridView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1.1,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: countries.length,
                  itemBuilder: (context, index) {
                    return _CountryCard(
                      name: countries[index]["name"]!,
                      image: countries[index]["image"]!,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _CountryCard extends StatelessWidget {
  final String name;
  final String image;
  
  const _CountryCard({
    required this.name, 
    required this.image,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    
    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => JobsByCountries(country: name),
          ),
        );
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withOpacity(0.18)
                  : Colors.black.withOpacity(0.06),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: isDarkMode
                      ? [primaryColor.withOpacity(0.18), Colors.black26]
                      : [primaryColor.withOpacity(0.13), Colors.white],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.black.withOpacity(0.18)
                        : Colors.black.withOpacity(0.06),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Image.asset(
                  'assets/$image.jpg',
                  width: 38,
                  height: 38,
                  fit: BoxFit.contain,
                  color: null,
                ),
              ),
            ),
            const SizedBox(height: 14),
            Text(
              name,
              style: GoogleFonts.tajawal(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: isDarkMode
                    ? Theme.of(context).textTheme.bodyLarge?.color
                    : primaryColor,
                letterSpacing: 0.2,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class CountriesScreen extends StatelessWidget {
  const CountriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: CountriesGrid());
  }
}
