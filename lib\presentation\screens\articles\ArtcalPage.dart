
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:wzzff/presentation/widgets/HtmlWidget.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/NewsModel.dart';
import 'package:wzzff/services/google_ad_service.dart';

class ArtcalPage extends StatefulWidget {
  final NewsModel data;

  const ArtcalPage({super.key, required this.data});

  @override
  State<ArtcalPage> createState() => _ArtcalPageState();
}

class _ArtcalPageState extends State<ArtcalPage> {
  final GoogleAdService _adService = GoogleAdService();

  @override
  void initState() {
    super.initState();
    // تهيئة خدمة الإعلانات
    _adService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Scaffold(
      backgroundColor: isDarkMode ? Theme.of(context).scaffoldBackgroundColor : Colors.white,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? Theme.of(context).cardTheme.color?.withAlpha(204)
                  : Colors.white.withAlpha(204),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_forward,
                color: isDarkMode ? Colors.white : primaryColor,
                size: 20,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
              tooltip: 'رجوع',
            ),
          ),
        ],
        title: Text(
          "مقالات ونصائح",
          style: GoogleFonts.tajawal(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 18,
            shadows: [
              Shadow(
                offset: const Offset(0, 1),
                blurRadius: 3.0,
                color: Colors.black.withAlpha(128), // 0.5 = 128
              ),
            ],
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة الغلاف
              Stack(
                children: [
                  // الصورة
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(24),
                      bottomRight: Radius.circular(24),
                    ),
                    child: CachedNetworkImage(
                      imageUrl: "https://wzzff.com/${Uri.encodeFull(widget.data.image)}",
                      height: 250,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      progressIndicatorBuilder: (context, url, downloadProgress) => Center(
                        heightFactor: 10,
                        child: CircularProgressIndicator(
                          color: const Color(0xFF2daae2),
                          value: downloadProgress.progress,
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        height: 250,
                        color: Colors.grey[200],
                        child: const Center(
                          child: Icon(Icons.error, color: Colors.grey),
                        ),
                      ),
                    ),
                  ),
                  // تدرج شفاف أسفل الصورة
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(24),
                          bottomRight: Radius.circular(24),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withAlpha(179), // 0.7 = 179
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              // العنوان
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 24, 20, 8),
                child: Text(
                  widget.data.title,
                  style: GoogleFonts.tajawal(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : const Color(0xFF333333),
                    height: 1.3,
                  ),
                ),
              ),

              // خط فاصل مزخرف
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Container(
                  height: 3,
                  width: 80,
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),

              // معلومات المقال
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 16,
                      color: isDarkMode ? Colors.white70 : Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                                             'نُشر في ${widget.data.time}',
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        color: isDarkMode ? Colors.white70 : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

              // إعلان البانر العلوي
              Container(
                margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(isDarkMode ? 51 : 13),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Container(height: 50) // ���� ������,
                ),
              ),

              // محتوى المقال
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: HtmlWidget(
                                     widget.data.des,
                  textStyle: GoogleFonts.tajawal(
                    fontSize: 16,
                    height: 1.8,
                    color: isDarkMode ? Colors.white : const Color(0xFF333333),
                  ),
                ),
              ),

              // إعلان البانر السفلي
              Container(
                margin: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(isDarkMode ? 51 : 13),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Container(height: 50) // ���� ������,
                ),
              ),

              // مساحة إضافية في الأسفل
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
