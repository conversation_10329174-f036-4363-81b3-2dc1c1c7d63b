import 'package:flutter/material.dart';
import 'package:wzzff/models/SeekerModel.dart';
import 'common_widgets.dart';

class ContactInfoStep extends StatefulWidget {
  final SeekerModel user;
  final Function(Map<String, dynamic>) onDataChanged;

  const ContactInfoStep({
    super.key,
    required this.user,
    required this.onDataChanged,
  });

  @override
  State<ContactInfoStep> createState() => _ContactInfoStepState();
}

class _ContactInfoStepState extends State<ContactInfoStep> {
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fillUserData();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  void _fillUserData() {
    if (_emailController.text.isEmpty) {
      _emailController.text = widget.user.email ?? '';
    }
    if (_phoneController.text.isEmpty) {
      _phoneController.text = widget.user.phone ?? '';
    }
    if (_addressController.text.isEmpty) {
      _addressController.text = widget.user.address ?? '';
    }
    if (_cityController.text.isEmpty) {
      _cityController.text = widget.user.city ?? '';
    }
    _updateData();
  }

  void _updateData() {
    widget.onDataChanged({
      "email": _emailController.text,
      "phone": _phoneController.text,
      "address": _addressController.text,
      "city": _cityController.text,
    });
  }

  @override
  Widget build(BuildContext context) {

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSectionTitle("معلومات الاتصال", Icons.contact_phone),
          const SizedBox(height: 24),

          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: TextFormField(
              controller: _emailController,
              readOnly: true,
              decoration: InputDecoration(
                labelText: "البريد الإلكتروني",
                prefixIcon: const Icon(Icons.email_outlined),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                filled: true,
                fillColor: Theme.of(context).inputDecorationTheme.fillColor,
              ),
              style: Theme.of(context).textTheme.bodyMedium,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "الرجاء إدخال البريد الإلكتروني";
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return "الرجاء إدخال بريد إلكتروني صحيح";
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 16),

          buildInputField(
            controller: _phoneController,
            label: "رقم الهاتف",
            icon: Icons.phone_outlined,
            isRequired: true,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء إدخال رقم الهاتف";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),

          buildInputField(
            controller: _addressController,
            label: "العنوان",
            icon: Icons.location_on_outlined,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء إدخال العنوان";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),

          buildInputField(
            controller: _cityController,
            label: "المدينة",
            icon: Icons.location_city_outlined,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء إدخال المدينة";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
        ],
      ),
    );
  }
}