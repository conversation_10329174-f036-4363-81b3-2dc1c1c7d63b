import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:wzzff/presentation/screens/create_cv/Components/FieldTxt.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';

class LanguagesStep extends StatefulWidget {
  final CvModel cvData;
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic> currentData;
  final List<Map<String, dynamic>> languagesList;
  final Function(List<Map<String, dynamic>>) onLanguagesListChanged;

  const LanguagesStep({
    Key? key,
    required this.cvData,
    required this.onDataChanged,
    required this.currentData,
    required this.languagesList,
    required this.onLanguagesListChanged,
  }) : super(key: key);

  @override
  State<LanguagesStep> createState() => _LanguagesStepState();
}

class _LanguagesStepState extends State<LanguagesStep> {
  late TextEditingController _langController;

  String? _langLevel;
  int? _editingIndex;

  final List<String> langLevelOptions = [
    'اساسيات',
    'جيد',
    'متوسط',
    "اجادة تامة"
  ];

  @override
  void initState() {
    super.initState();
    _langController = TextEditingController(text: widget.currentData['lang']);
    _langLevel = widget.currentData['lang_level'];
  }

  @override
  void dispose() {
    _langController.dispose();
    super.dispose();
  }

  void _updateData(String field, String? value) {
    final newData = Map<String, dynamic>.from(widget.currentData);
    newData[field] = value;
    widget.onDataChanged(newData);
  }

  void _addLanguage() {
    if (_langController.text.isEmpty || _langLevel == null) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(Lang().getWord("fill_all_fields", widget.cvData.ltf)),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final language = {
      "lang": _langController.text,
      "lang_level": _langLevel,
    };

    final newList = List<Map<String, dynamic>>.from(widget.languagesList);

    if (_editingIndex != null) {
      newList[_editingIndex!] = language;
      _editingIndex = null;
    } else {
      newList.add(language);
    }

    widget.onLanguagesListChanged(newList);

    // إعادة تعيين الحقول
    _langController.clear();
    setState(() {
      _langLevel = null;
    });
  }

  void _editLanguage(int index) {
    final language = widget.languagesList[index];

    setState(() {
      _langController.text = language["lang"];
      _langLevel = language["lang_level"];
      _editingIndex = index;
    });
  }

  void _deleteLanguage(int index) {
    final newList = List<Map<String, dynamic>>.from(widget.languagesList);
    newList.removeAt(index);
    widget.onLanguagesListChanged(newList);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // إذا تم العودة من الخطوة التالية، احذف آخر لغة من المصفوفة وأعد تعبئة الحقول
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map && args['action'] == 'back_step' && widget.languagesList.isNotEmpty) {
      final lastLang = widget.languagesList.last;
      widget.onLanguagesListChanged(List<Map<String, dynamic>>.from(widget.languagesList)..removeLast());
      setState(() {
        _langController.text = lastLang["lang"];
        _langLevel = lastLang["lang_level"];
        _editingIndex = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final cardColor = Theme.of(context).cardColor;
    final textColor = Theme.of(context).textTheme.bodyLarge?.color;

    return Directionality(
      textDirection: widget.cvData.ltf == "0" ? TextDirection.rtl : TextDirection.ltr,
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [primaryColor, primaryColor.withOpacity(0.7)],
                ),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.10),
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const Icon(Icons.language, color: Colors.white, size: 22),
                  const SizedBox(width: 8),
                  Text(
                    Lang().getWord("languages", widget.cvData.ltf),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 14),

            // قائمة اللغات المضافة
            if (widget.languagesList.isNotEmpty) ...[
              Text(
                Lang().getWord("added_languages", widget.cvData.ltf),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
              const SizedBox(height: 10),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: widget.languagesList.length,
                itemBuilder: (context, index) {
                  final language = widget.languagesList[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 10),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      title: Text(
                        language["lang"],
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(
                        "${Lang().getWord("level", widget.cvData.ltf)}: ${language["lang_level"]}",
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editLanguage(index),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _deleteLanguage(index),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              const Divider(height: 30),
            ],

            // نموذج إضافة لغة جديدة
            Text(
              _editingIndex != null
                  ? Lang().getWord("edit_language", widget.cvData.ltf)
                  : Lang().getWord("add_new_language", widget.cvData.ltf),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 16),

            // اسم اللغة
            FieldTxt(
              fieldLabel: Lang().getWord("language", widget.cvData.ltf),
              handlerInput: (value) => _updateData('lang', value),
              fieldname: 'lang',
              hint: Lang().getWord("language_hint", widget.cvData.ltf),
              iconField: const Icon(Icons.language),
              inputRequired: widget.cvData.lang == 1,
              textEditingController: _langController,
            ),
            const SizedBox(height: 16),

            // مستوى اللغة
            Text(
              Lang().getWord("lang_level", widget.cvData.ltf),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 8),
            FormBuilderDropdown(
              name: 'lang_level',
              initialValue: _langLevel,
              decoration: InputDecoration(
                filled: true,
                fillColor: cardColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                prefixIcon: const Icon(Icons.grade),
                hintText: Lang().getWord("choose", widget.cvData.ltf),
              ),
              items: langLevelOptions
                  .map((level) => DropdownMenuItem(
                        value: level,
                        child: Text(level),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _langLevel = value;
                });
                _updateData('lang_level', value);
              },
            ),
            const SizedBox(height: 24),

            // أزرار الإضافة فقط (بدون زر التالي)
            Row(
              children: [
                // زر الإضافة
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _addLanguage();
                      // إضافة رسالة تأكيد عند الإضافة بنجاح
                      if (_langController.text.isNotEmpty && _langLevel != null) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(_editingIndex != null
                                ? Lang().getWord("language_updated", widget.cvData.ltf)
                                : Lang().getWord("language_added", widget.cvData.ltf)),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                            margin: const EdgeInsets.all(16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        );
                      }
                    },
                    icon: Icon(_editingIndex != null ? Icons.save : Icons.add),
                    label: Text(
                      _editingIndex != null
                          ? Lang().getWord("save_changes", widget.cvData.ltf)
                          : Lang().getWord("add_new_language", widget.cvData.ltf),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
