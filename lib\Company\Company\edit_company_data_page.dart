import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/core/theme/app_colors.dart';
import 'package:wzzff/Company/Company/edit_company_form_page.dart';
import 'package:wzzff/Apis/CompanyApi.dart';

class EditCompanyDataPage extends StatefulWidget {
  @override
  _EditCompanyDataPageState createState() => _EditCompanyDataPageState();
}

class _EditCompanyDataPageState extends State<EditCompanyDataPage> 
    with TickerProviderStateMixin {
  bool _isLoading = true;
  String? _error;
  Map<String, dynamic>? _companyData;

  // Animation Controllers
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);
  Color get successColor => const Color(0xff4caf50);

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _fetchCompanyData();
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
      }
    });
  }

  Future<void> _fetchCompanyData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });
    
    try {
      final response = await CompanyApi().getCompanyData();
      
      if (response['success'] == true) {
        setState(() {
          _companyData = response['data'];
          _isLoading = false;
        });
        _startAnimations();
      } else {
        setState(() {
          _error = response['message'] ?? 'فشل في جلب بيانات الشركة';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'تعذر الاتصال بالخادم، يرجى المحاولة لاحقاً';
        _isLoading = false;
      });
    }
  }

  void _openEditForm() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditCompanyFormPage(
          companyData: _companyData!,
          onDataUpdated: (updatedData) {
            setState(() {
              _companyData = updatedData;
            });
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = Theme.of(context).scaffoldBackgroundColor;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: Stack(
          children: [
            // خلفية متدرجة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    primaryColor.withOpacity(0.05),
                    backgroundColor,
                    backgroundColor,
                  ],
                  stops: const [0.0, 0.3, 1.0],
                ),
              ),
            ),

            Column(
              children: [
                // Header مخصص
                _buildCustomHeader(isDarkMode),

                // محتوى الصفحة
                Expanded(
                  child: _isLoading
                      ? _buildLoadingView()
                      : _error != null
                          ? _buildErrorView()
                          : _companyData == null
                              ? _buildNoDataView()
                              : FadeTransition(
                                  opacity: _fadeAnimation,
                                  child: SlideTransition(
                                    position: _slideAnimation,
                                    child: RefreshIndicator(
                                      onRefresh: _fetchCompanyData,
                                      child: SingleChildScrollView(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          children: [
                                            // معلومات الشركة الأساسية
                                            _buildCompanyHeader(isDarkMode),
                                            
                                            const SizedBox(height: 20),
                                            
                                            // معلومات الاتصال
                                            _buildContactInfo(isDarkMode),
                                            
                                            const SizedBox(height: 20),
                                            
                                            // تفاصيل الشركة
                                            _buildCompanyDetails(isDarkMode),
                                            
                                            const SizedBox(height: 20),
                                            
                                          
                                            
                                            // روابط التواصل الاجتماعي
                                            if (_hasSocialLinks())
                                              _buildSocialLinks(isDarkMode),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                ),
              ],
            ),
          ],
        ),
        floatingActionButton: !_isLoading && _companyData != null
            ? FloatingActionButton.extended(
                onPressed: _openEditForm,
                backgroundColor: primaryColor,
                icon: Icon(Icons.edit, color: Colors.white),
                label: Text(
                  'تعديل البيانات',
                  style: GoogleFonts.tajawal(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildCustomHeader(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 8,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الشركة
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [primaryColor, accentColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Icon(
              Icons.business,
              color: Colors.white,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // العنوان
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'بيانات الشركة',
                  style: GoogleFonts.tajawal(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'عرض وإدارة معلومات الشركة',
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // زر الرجوع
          Container(
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_forward_ios,
                color: primaryColor,
                size: 20,
              ),
              onPressed: () => Navigator.pop(context),
              tooltip: 'رجوع',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات الشركة...',
            style: GoogleFonts.tajawal(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        margin: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: GoogleFonts.tajawal(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchCompanyData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'إعادة المحاولة',
                style: GoogleFonts.tajawal(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات للشركة',
            style: GoogleFonts.tajawal(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyHeader(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // شعار الشركة
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [primaryColor.withOpacity(0.2), primaryColor.withOpacity(0.1)],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: primaryColor.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: _companyData!['logo'] != null && _companyData!['logo'].isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(18),
                    child: Image.network(
                      _companyData!['logo'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.business,
                        color: primaryColor,
                        size: 40,
                      ),
                    ),
                  )
                : Icon(
                    Icons.business,
                    color: primaryColor,
                    size: 40,
                  ),
          ),
          
          const SizedBox(height: 16),
          
          // اسم الشركة
          Text(
            _companyData!['name'] ?? 'غير محدد',
            style: GoogleFonts.tajawal(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // حالة التحقق
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _companyData!['verified'] == 1 
                  ? successColor.withOpacity(0.1)
                  : Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _companyData!['verified'] == 1 
                    ? successColor.withOpacity(0.3)
                    : Colors.orange.withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _companyData!['verified'] == 1 
                      ? Icons.verified
                      : Icons.pending,
                  size: 16,
                  color: _companyData!['verified'] == 1 
                      ? successColor
                      : Colors.orange,
                ),
                const SizedBox(width: 6),
                Text(
                  _companyData!['verified'] == 1 ? 'شركة موثقة' : 'في انتظار التوثيق',
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: _companyData!['verified'] == 1 
                        ? successColor
                        : Colors.orange,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfo(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.contact_phone,
                  color: primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "معلومات الاتصال",
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildContactItem(
            Icons.email,
            'البريد الإلكتروني',
            _companyData!['email'] ?? 'غير محدد',
            isDarkMode,
          ),
          
          _buildContactItem(
            Icons.phone,
            'رقم الهاتف',
            _companyData!['phone'] ?? 'غير محدد',
            isDarkMode,
          ),
          
          if (_companyData!['fax'] != null && _companyData!['fax'].isNotEmpty)
            _buildContactItem(
              Icons.fax,
              'الفاكس',
              _companyData!['fax'],
              isDarkMode,
            ),
          
          if (_companyData!['website'] != null && _companyData!['website'].isNotEmpty)
            _buildContactItem(
              Icons.language,
              'الموقع الإلكتروني',
              _companyData!['website'],
              isDarkMode,
            ),
          
          _buildContactItem(
            Icons.location_on,
            'الموقع',
            _companyData!['location'] ?? 'غير محدد',
            isDarkMode,
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String label, String value, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 16,
              color: primaryColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyDetails(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.info_outline,
                  color: accentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "تفاصيل الشركة",
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (_companyData!['description'] != null && _companyData!['description'].isNotEmpty) ...[
            Text(
              'نبذة عن الشركة',
              style: GoogleFonts.tajawal(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _companyData!['description'],
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: isDarkMode ? Colors.white70 : Colors.grey[700],
                height: 1.6,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  'المدير التنفيذي',
                  _companyData!['ceo'] ?? 'غير محدد',
                  Icons.person,
                  isDarkMode,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailItem(
                  'سنة التأسيس',
                  _companyData!['established_in'] ?? 'غير محدد',
                  Icons.calendar_today,
                  isDarkMode,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  'عدد الموظفين',
                  _companyData!['no_of_employees']?.toString() ?? 'غير محدد',
                  Icons.people,
                  isDarkMode,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailItem(
                  'عدد المكاتب',
                  _companyData!['no_of_offices']?.toString() ?? 'غير محدد',
                  Icons.business_center,
                  isDarkMode,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: primaryColor,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: GoogleFonts.tajawal(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.tajawal(
              fontSize: 14,
              color: isDarkMode ? Colors.white : Colors.grey[800],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildStatCard(String label, String value, IconData icon, Color color, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.tajawal(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            textAlign: TextAlign.center,
            style: GoogleFonts.tajawal(
              fontSize: 12,
              color: isDarkMode ? Colors.white70 : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialLinks(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.share,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "روابط التواصل الاجتماعي",
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              if (_companyData!['facebook'] != null && _companyData!['facebook'].isNotEmpty)
                _buildSocialButton('Facebook', Icons.facebook, Colors.blue[700]!),
              if (_companyData!['twitter'] != null && _companyData!['twitter'].isNotEmpty)
                _buildSocialButton('Twitter', Icons.alternate_email, Colors.blue[400]!),
              if (_companyData!['linkedin'] != null && _companyData!['linkedin'].isNotEmpty)
                _buildSocialButton('LinkedIn', Icons.business_center, Colors.blue[800]!),
              if (_companyData!['google_plus'] != null && _companyData!['google_plus'].isNotEmpty)
                _buildSocialButton('Google+', Icons.add_circle, Colors.red[600]!),
              if (_companyData!['pinterest'] != null && _companyData!['pinterest'].isNotEmpty)
                _buildSocialButton('Pinterest', Icons.push_pin, Colors.red[400]!),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSocialButton(String name, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            name,
            style: GoogleFonts.tajawal(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  bool _hasSocialLinks() {
    return (_companyData!['facebook'] != null && _companyData!['facebook'].isNotEmpty) ||
           (_companyData!['twitter'] != null && _companyData!['twitter'].isNotEmpty) ||
           (_companyData!['linkedin'] != null && _companyData!['linkedin'].isNotEmpty) ||
           (_companyData!['google_plus'] != null && _companyData!['google_plus'].isNotEmpty) ||
           (_companyData!['pinterest'] != null && _companyData!['pinterest'].isNotEmpty);
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return 'غير محدد';
    
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }
} 