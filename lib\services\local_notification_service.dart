import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:wzzff/models/notification_model.dart' as app_model;
import '../presentation/screens/job_seeker/Notifications.dart';
import 'package:wzzff/services/Notifications.dart';


class LocalNotificationService {
  static final LocalNotificationService _instance = LocalNotificationService._internal();
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  factory LocalNotificationService() {
    return _instance;
  }

  LocalNotificationService._internal();

  Future<void> initialize() async {
    tz.initializeTimeZones();

    const AndroidInitializationSettings androidInitializationSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings iosInitializationSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings = InitializationSettings(
      android: androidInitializationSettings,
      iOS: iosInitializationSettings,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    await _requestPermission();
  }

  Future<void> _requestPermission() async {
    if (Platform.isAndroid) {
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
    } else if (Platform.isIOS) {
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    }
  }

  void _onNotificationTapped(NotificationResponse response) async {
    if (response.payload != null) {
      final Map<String, dynamic> data = jsonDecode(response.payload!);
      final String? notificationId = data['id'];
      await _markNotificationAsRead(notificationId);
    }
    _navigateToNotificationsPage(null);
  }

  Future<void> _markNotificationAsRead(String? notificationId) async {
    if (notificationId == null || notificationId.isEmpty) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList('notifications') ?? [];

      List<app_model.NotificationModel> notifications = notificationsJson
          .map((json) => app_model.NotificationModel.fromJson(json))
          .toList();

      final index = notifications.indexWhere((n) => n.id == notificationId);
      if (index >= 0) {
        notifications[index] = notifications[index].copyWith(isRead: true);

        final updatedNotificationsJson = notifications.map((n) => n.toJson()).toList();
        await prefs.setStringList('notifications', updatedNotificationsJson);

        await _updateUnreadCount(notifications);

        await prefs.setBool('notifications_updated', true);
        await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);
      }
    } catch (e) {
      // Handle error silently
    }
  }

  void _navigateToNotificationsPage(BuildContext? context) {
    if (context != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const Notifications(),
        ),
      );
    }
  }

  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    Map<String, dynamic>? payload,
  }) async {
    try {
      await _saveNotificationToStorage(id, title, body, payload);

      const AndroidNotificationDetails androidNotificationDetails = AndroidNotificationDetails(
        'high_importance_channel',
        'High Importance Notifications',
        channelDescription: 'This channel is used for important notifications.',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        playSound: true,
      );

      const DarwinNotificationDetails iosNotificationDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
        iOS: iosNotificationDetails,
      );

      String? payloadJson;
      if (payload != null) {
        payloadJson = jsonEncode(payload);
      }

      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payloadJson,
      );
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _saveNotificationToStorage(
    int id,
    String title,
    String body,
    Map<String, dynamic>? data,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList('notifications') ?? [];

      List<app_model.NotificationModel> notifications = notificationsJson
          .map((json) => app_model.NotificationModel.fromJson(json))
          .toList();

      final newNotification = app_model.NotificationModel(
        id: id.toString(),
        title: title,
        body: body,
       // type: data?['type'] ?? 'general',
        isRead: false,
        time: DateTime.now(),
        data: data,
      );

      notifications.insert(0, newNotification);

      if (notifications.length > 100) {
        notifications = notifications.take(100).toList();
      }

      final updatedNotificationsJson = notifications.map((n) => n.toJson()).toList();
      await prefs.setStringList('notifications', updatedNotificationsJson);

      await _updateUnreadCount(notifications);

      await prefs.setBool('notifications_updated', true);
      await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _updateUnreadCount(List<app_model.NotificationModel> notifications) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final unreadCount = notifications.where((n) => !n.isRead).length;
      await prefs.setInt('unread_notifications_count', unreadCount);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
    } catch (e) {
      // Handle error silently
    }
  }
} 