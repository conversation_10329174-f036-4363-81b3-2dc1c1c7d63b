import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FieldTxt extends StatefulWidget {
  final String fieldLabel;
  final String? fieldValue;
  final Function(String) handlerInput;
  final String fieldname;
  final Icon? iconField;
  final String hint;
  final int? maxLines;
  final bool inputRequired;
  final TextEditingController? textEditingController;
  final TextInputType? textInputType;
  final String? ltf; // متغير ltf لتحديد اللغة (0 للعربية، 1 للإنجليزية)

  const FieldTxt({
    super.key,
    required this.fieldLabel,
    required this.fieldname,
    this.fieldValue,
    required this.iconField,
    required this.hint,
    required this.handlerInput,
    required this.inputRequired,
    this.maxLines,
    this.textInputType,
    this.textEditingController,
    this.ltf,
  });

  @override
  State<FieldTxt> createState() => _FieldTxtState();
}

class _FieldTxtState extends State<FieldTxt> {
  @override
  Widget build(BuildContext context) {
    if (widget.inputRequired == false) {
      return Container();
    }

    final bool isArabic = widget.ltf == "0";
    final TextDirection textDir = isArabic ? TextDirection.rtl : TextDirection.ltr;
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color primaryColor = Theme.of(context).colorScheme.primary;

    // ألوان موحدة مع buildInputField
    final Color labelColor = isDarkMode ? Colors.grey[400]! : Colors.grey[700]!;
    final Color hintColor = isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
    final Color borderColor = isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
    final Color focusBorderColor = primaryColor;
    final Color errorColor = isDarkMode ? Colors.red[300]! : Colors.red;
    final Color? fillColor = isDarkMode ? Theme.of(context).inputDecorationTheme.fillColor : null;

    return Directionality(
      textDirection: textDir,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 0.0),
        child: TextFormField(
          controller: widget.textEditingController,
          maxLines: widget.maxLines ?? 1,
          keyboardType: widget.textInputType ?? TextInputType.text,
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
          style: GoogleFonts.tajawal(
            color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : Colors.black87,
            fontSize: 16,
          ),
          decoration: InputDecoration(
            labelText: widget.fieldLabel,
            labelStyle: GoogleFonts.tajawal(
              color: labelColor,
              fontSize: 16,
            ),
            hintText: widget.hint,
            hintStyle: GoogleFonts.tajawal(
              color: hintColor,
              fontSize: 15,
            ),
            prefixIcon: !isArabic && widget.iconField != null
                ? Icon(
                    widget.iconField!.icon,
                    color: primaryColor,
                  )
                : null,
            suffixIcon: isArabic && widget.iconField != null
                ? Icon(
                    widget.iconField!.icon,
                    color: primaryColor,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: borderColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: borderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: focusBorderColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: errorColor,
              ),
            ),
            filled: isDarkMode,
            fillColor: fillColor,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            errorStyle: GoogleFonts.tajawal(
              color: Colors.red.shade400,
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return isArabic
                  ? "${widget.fieldLabel} مطلوب"
                  : "${widget.fieldLabel} is required";
            }
            return null;
          },
          onChanged: widget.handlerInput,
        ),
      ),
    );
  }
}
