import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:wzzff/presentation/screens/create_cv/Components/FieldTxt.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';

class SkillsStep extends StatefulWidget {
  final CvModel cvData;
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic> currentData;
  final List<Map<String, dynamic>> skillsList;
  final Function(List<Map<String, dynamic>>) onSkillsListChanged;
  final Function() onSubmit;

  const SkillsStep({
    Key? key,
    required this.cvData,
    required this.onDataChanged,
    required this.currentData,
    required this.skillsList,
    required this.onSkillsListChanged,
    required this.onSubmit,
  }) : super(key: key);

  @override
  State<SkillsStep> createState() => _SkillsStepState();
}

class _SkillsStepState extends State<SkillsStep> {
  late TextEditingController _skillController;

  String? _skillLevel;
  int? _editingIndex;

  final List<String> skillLevelOptions = [
    'اساسيات',
    'جيد',
    'متوسط',
    "اجادة تامة"
  ];

  @override
  void initState() {
    super.initState();
    _skillController = TextEditingController(text: widget.currentData['skill']);
    _skillLevel = widget.currentData['skill_process'];
  }

  @override
  void dispose() {
    _skillController.dispose();
    super.dispose();
  }

  void _updateData(String field, String? value) {
    final newData = Map<String, dynamic>.from(widget.currentData);
    newData[field] = value;
    widget.onDataChanged(newData);
  }

  void _addSkill() {
    if (_skillController.text.isEmpty || _skillLevel == null) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(Lang().getWord("fill_all_fields", widget.cvData.ltf)),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final skill = {
      "skill": _skillController.text,
      "skill_process": _skillLevel,
    };

    final newList = List<Map<String, dynamic>>.from(widget.skillsList);

    if (_editingIndex != null) {
      newList[_editingIndex!] = skill;
      _editingIndex = null;
    } else {
      newList.add(skill);
    }

    widget.onSkillsListChanged(newList);

    // إعادة تعيين الحقول
    _skillController.clear();
    setState(() {
      _skillLevel = null;
    });
  }

  void _editSkill(int index) {
    final skill = widget.skillsList[index];

    setState(() {
      _skillController.text = skill["skill"];
      _skillLevel = skill["skill_process"];
      _editingIndex = index;
    });
  }

  void _deleteSkill(int index) {
    final newList = List<Map<String, dynamic>>.from(widget.skillsList);
    newList.removeAt(index);
    widget.onSkillsListChanged(newList);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // إذا تم العودة من الخطوة التالية، احذف آخر مهارة من المصفوفة وأعد تعبئة الحقول
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map && args['action'] == 'back_step' && widget.skillsList.isNotEmpty) {
      final lastSkill = widget.skillsList.last;
      widget.onSkillsListChanged(List<Map<String, dynamic>>.from(widget.skillsList)..removeLast());
      setState(() {
        _skillController.text = lastSkill["skill"];
        _skillLevel = lastSkill["skill_process"];
        _editingIndex = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final cardColor = Theme.of(context).cardColor;
    final textColor = Theme.of(context).textTheme.bodyLarge?.color;

    return Directionality(
      textDirection: widget.cvData.ltf == "0" ? TextDirection.rtl : TextDirection.ltr,
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [primaryColor, primaryColor.withOpacity(0.7)],
                ),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.10),
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const Icon(Icons.psychology, color: Colors.white, size: 22),
                  const SizedBox(width: 8),
                  Text(
                    Lang().getWord("skills", widget.cvData.ltf),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 14),

            // قائمة المهارات المضافة
            if (widget.skillsList.isNotEmpty) ...[
              Text(
                Lang().getWord("added_skills", widget.cvData.ltf),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
              const SizedBox(height: 10),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: widget.skillsList.length,
                itemBuilder: (context, index) {
                  final skill = widget.skillsList[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 10),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      title: Text(
                        skill["skill"],
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(
                        "${Lang().getWord("level", widget.cvData.ltf)}: ${skill["skill_process"]}",
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editSkill(index),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _deleteSkill(index),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              const Divider(height: 30),
            ],

            // نموذج إضافة مهارة جديدة
            Text(
              _editingIndex != null
                  ? Lang().getWord("edit_skill", widget.cvData.ltf)
                  : Lang().getWord("add_new_skill", widget.cvData.ltf),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 16),

            // اسم المهارة
            FieldTxt(
              fieldLabel: Lang().getWord("skill", widget.cvData.ltf),
              handlerInput: (value) => _updateData('skill', value),
              fieldname: 'skill',
              hint: Lang().getWord("skill_hint", widget.cvData.ltf),
              iconField: const Icon(Icons.psychology),
              inputRequired: widget.cvData.skill == 1,
              textEditingController: _skillController,
            ),
            const SizedBox(height: 16),

            // مستوى المهارة
            Text(
              Lang().getWord("skill_process", widget.cvData.ltf),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 8),
            FormBuilderDropdown(
              name: 'skill_process',
              initialValue: _skillLevel,
              decoration: InputDecoration(
                filled: true,
                fillColor: cardColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                prefixIcon: const Icon(Icons.grade),
                hintText: Lang().getWord("choose", widget.cvData.ltf),
              ),
              items: skillLevelOptions
                  .map((level) => DropdownMenuItem(
                        value: level,
                        child: Text(level),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _skillLevel = value;
                });
                _updateData('skill_process', value);
              },
            ),
            const SizedBox(height: 24),

            // أزرار الإضافة فقط (بدون زر التالي)
            Row(
              children: [
                // زر الإضافة
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _addSkill,
                    icon: Icon(_editingIndex != null ? Icons.save : Icons.add),
                    label: Text(
                      _editingIndex != null
                          ? Lang().getWord("save_changes", widget.cvData.ltf)
                          : Lang().getWord("add_skill", widget.cvData.ltf),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
