import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:wzzff/core/theme/app_colors.dart';
import 'package:wzzff/presentation/screens/job_seeker/profile_steps/common_widgets.dart';
import 'package:wzzff/Apis/CompanyApi.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/models/SpinnerDataModel.dart';
import '../../presentation/screens/privacy_policy/privacy_policy.dart';
import '../../presentation/screens/privacy_policy/terms_OfUsing.dart';
import 'company_dashboard.dart';

class RegisterCompanyPage extends StatefulWidget {
  @override
  _RegisterCompanyPageState createState() => _RegisterCompanyPageState();
}

class _RegisterCompanyPageState extends State<RegisterCompanyPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _agreeToPrivacyPolicy = false;
  bool _agreeToTermsOfUse = false;
  
  // اختيار الدولة والمدينة
  SpinnerDataModel? _selectedCountry;
  SpinnerDataModel? _selectedCity;
  
  // قوائم البيانات
  List<SpinnerDataModel> _countries = [];
  List<SpinnerDataModel> _cities = [];
  bool _isLoadingCities = false;
  
  // Animation Controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  // Form Controllers
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _websiteController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadCountries();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _companyNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _websiteController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _animationController.forward();
  }

  // تحميل قائمة الدول
  void _loadCountries() {
    _countries = [
      SpinnerDataModel(name: 'السعودية', id: 1),
      SpinnerDataModel(name: 'مصر', id: 2),
      SpinnerDataModel(name: 'الامارات', id: 3),
      SpinnerDataModel(name: 'الأردن', id: 4),
      SpinnerDataModel(name: 'البحرين', id: 5),
      SpinnerDataModel(name: 'الكويت', id: 6),
      SpinnerDataModel(name: 'قطر', id: 7),
      SpinnerDataModel(name: 'عمان', id: 8),
      SpinnerDataModel(name: 'العراق', id: 9),
      SpinnerDataModel(name: 'الجزائر', id: 10),
      SpinnerDataModel(name: 'المغرب', id: 11),
      SpinnerDataModel(name: 'تونس', id: 12),
      SpinnerDataModel(name: 'لبنان', id: 13),
      SpinnerDataModel(name: 'سوريا', id: 14),
      SpinnerDataModel(name: 'السودان', id: 15),
      SpinnerDataModel(name: 'ليبيا', id: 16),
      SpinnerDataModel(name: 'فلسطين', id: 17),
      SpinnerDataModel(name: 'اليمن', id: 18),
    ];
  }

  // الحصول على قائمة المدن بناءً على الدولة المختارة
  List<SpinnerDataModel> _getCitiesForCountry() {
    return _cities;
  }
  
  // إعادة تعيين المدينة عند تغيير الدولة
  void _onCountryChanged(SpinnerDataModel? country) async {
    setState(() {
      _selectedCountry = country;
      _selectedCity = null; // إعادة تعيين المدينة
      _isLoadingCities = true;
    });
    
    if (country != null) {
      try {
        final cities = await JobsApi().getCitesApi(country: country.name);
        setState(() {
          _cities = cities;
          _isLoadingCities = false;
        });
      } catch (e) {
        setState(() {
          _cities = [];
          _isLoadingCities = false;
        });
        _showErrorMessage('حدث خطأ أثناء تحميل المدن');
      }
    } else {
      setState(() {
        _cities = [];
        _isLoadingCities = false;
      });
    }
  }

  Future<void> _registerCompany() async {
    if (!_formKey.currentState!.validate()) return;
    
    // التحقق من اختيار الدولة والمدينة
    if (_selectedCountry == null) {
      _showErrorMessage('يرجى اختيار الدولة');
      return;
    }
    
    if (_selectedCity == null) {
      _showErrorMessage('يرجى اختيار المدينة');
      return;
    }
    
    // التحقق من الموافقة على سياسة الخصوصية وشروط الاستخدام
    if (!_agreeToPrivacyPolicy) {
      _showErrorMessage('يرجى الموافقة على سياسة الخصوصية');
      return;
    }
    
    if (!_agreeToTermsOfUse) {
      _showErrorMessage('يرجى الموافقة على شروط الاستخدام');
      return;
    }
    
    setState(() => _isLoading = true);
    
    final companyData = {
      'name': _companyNameController.text.trim(),
      'email': _emailController.text.trim(),
      'password': _passwordController.text,
      'phone': _phoneController.text.trim(),
      'address': _addressController.text.trim(),
      'country': _selectedCountry?.name,
      'state_id': _selectedCity?.id,
      'website': _websiteController.text.trim(),
      'description': _descriptionController.text.trim(),
    };
    
    try {
      final response = await CompanyApi().registerCompany(companyData);
      
      if (response['success'] == true) {
        // إرسال Firebase token بعد نجاح التسجيل
        await _sendFirebaseToken();
        
        _showSuccessDialog();
      } else {
        _showErrorMessage(response['message'] ?? 'حدث خطأ أثناء التسجيل');
      }
    } catch (e) {
      _showErrorMessage('تعذر الاتصال بالخادم، يرجى المحاولة لاحقاً');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // إرسال Firebase token للسيرفر
  Future<void> _sendFirebaseToken() async {
    try {
      // الحصول على Firebase token
      String? firebaseToken = await FirebaseMessaging.instance.getToken();
      
      if (firebaseToken != null) {
        print('🔔 Firebase Token للشركة المسجلة: $firebaseToken');
        
        // إرسال Token للسيرفر
        final response = await CompanyApi().sendFirebaseToken(firebaseToken);
        
        if (response['success'] == true) {
          print('✅ تم إرسال Firebase token بنجاح للشركة المسجلة');
        } else {
          print('❌ فشل في إرسال Firebase token: ${response['message']}');
        }
      } else {
        print('❌ لم يتم الحصول على Firebase token');
      }
    } catch (e) {
      print('❌ خطأ في إرسال Firebase token: $e');
      // لا نعرض رسالة خطأ للمستخدم لأن هذا ليس مهماً لنجاح التسجيل
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.check, color: Colors.white, size: 40),
            ),
            const SizedBox(height: 20),
            Text(
              'تم التسجيل بنجاح!',
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'تم إنشاء حساب الشركة بنجاح. يمكنك الآن الوصول إلى لوحة التحكم الخاصة بالشركة.',
              style: GoogleFonts.tajawal(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: Text('العودة للحساب', style: GoogleFonts.tajawal(color: Colors.green)),
          ),
        ],
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.tajawal()),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى تأكيد كلمة المرور';
    }
    if (value != _passwordController.text) {
      return 'كلمة المرور غير متطابقة';
    }
    return null;
  }

  // دالة لإنشاء dropdown محسنة
  Widget _buildDropdown({
    required String hint,
    required IconData icon,
    required SpinnerDataModel? value,
    required List<SpinnerDataModel> items,
    required Function(SpinnerDataModel?) onChanged,
    bool enabled = true,
  }) {
    final isSelected = value != null;
    final borderColor = !enabled 
        ? Colors.grey.withOpacity(0.3)
        : isSelected 
            ? Theme.of(context).colorScheme.primary.withOpacity(0.5)
            : Theme.of(context).dividerColor.withOpacity(0.3);
    
    return Container(
      decoration: BoxDecoration(
        color: enabled 
            ? AppColors.getCardColor(context)
            : AppColors.getCardColor(context).withOpacity(0.7),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: borderColor,
          width: 1.5,
        ),
        boxShadow: enabled ? [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : [],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2<SpinnerDataModel>(
          isExpanded: true,
          hint: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: enabled 
                      ? (isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : AppColors.getSecondaryTextColor(context))
                      : Colors.grey,
                  size: 22,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    value?.name ?? hint,
                    style: GoogleFonts.tajawal(
                      fontSize: 16,
                      color: enabled 
                          ? (isSelected 
                              ? AppColors.getTextColor(context)
                              : AppColors.getSecondaryTextColor(context))
                          : Colors.grey,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (!enabled)
                  Icon(
                    Icons.lock_outline,
                    color: Colors.grey,
                    size: 18,
                  ),
              ],
            ),
          ),
          items: items.map((SpinnerDataModel item) {
            return DropdownMenuItem<SpinnerDataModel>(
              value: item,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  item.name,
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    color: AppColors.getTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
            );
          }).toList(),
          value: value,
          onChanged: enabled ? onChanged : null,
          dropdownStyleData: DropdownStyleData(
            maxHeight: 300,
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: AppColors.getCardColor(context),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withOpacity(0.3)
                      : Colors.grey.withOpacity(0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
          ),
          menuItemStyleData: MenuItemStyleData(
            height: 48,
            padding: EdgeInsets.zero,
          ),
          buttonStyleData: ButtonStyleData(
            height: 56,
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.getCardColor(context),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الشركة',
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            
            buildInputField(
              controller: _companyNameController,
              label: 'اسم الشركة',
              icon: Icons.business,
              isRequired: true,
              validator: (v) => v == null || v.isEmpty ? 'يرجى إدخال اسم الشركة' : null,
            ),
            
            const SizedBox(height: 16),
            
            buildInputField(
              controller: _emailController,
              label: 'البريد الإلكتروني',
              icon: Icons.email,
              isRequired: true,
              keyboardType: TextInputType.emailAddress,
              validator: _validateEmail,
            ),
            
            const SizedBox(height: 16),
            
            buildInputField(
              controller: _passwordController,
              label: 'كلمة المرور',
              icon: Icons.lock,
              isRequired: true,
              isPassword: !_isPasswordVisible,
              validator: _validatePassword,
              suffixIcon: IconButton(
                icon: Icon(_isPasswordVisible ? Icons.visibility_off : Icons.visibility),
                onPressed: () => setState(() => _isPasswordVisible = !_isPasswordVisible),
              ),
            ),
            
            const SizedBox(height: 16),
            
            buildInputField(
              controller: _confirmPasswordController,
              label: 'تأكيد كلمة المرور',
              icon: Icons.lock_outline,
              isRequired: true,
              isPassword: !_isConfirmPasswordVisible,
              validator: _validateConfirmPassword,
              suffixIcon: IconButton(
                icon: Icon(_isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility),
                onPressed: () => setState(() => _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
              ),
            ),
            
            const SizedBox(height: 16),
            
            buildInputField(
              controller: _phoneController,
              label: 'رقم الهاتف',
              icon: Icons.phone,
              isRequired: true,
              keyboardType: TextInputType.phone,
              validator: (v) => v == null || v.isEmpty ? 'يرجى إدخال رقم الهاتف' : null,
            ),
            
            const SizedBox(height: 16),
            
            buildInputField(
              controller: _addressController,
              label: 'عنوان الشركة',
              icon: Icons.location_on,
              isRequired: true,
              validator: (v) => v == null || v.isEmpty ? 'يرجى إدخال عنوان الشركة' : null,
            ),
            
            const SizedBox(height: 24),
            
            // قسم الموقع الجغرافي
            Text(
              'الموقع الجغرافي',
              style: GoogleFonts.tajawal(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // اختيار الدولة
            _buildDropdown(
              hint: 'اختر الدولة',
              icon: Icons.public,
              value: _selectedCountry,
              items: _countries,
              onChanged: _onCountryChanged,
            ),
            
            const SizedBox(height: 16),
            
            // اختيار المدينة
            _buildDropdown(
              hint: _selectedCountry == null 
                  ? 'اختر الدولة أولاً' 
                  : _isLoadingCities 
                      ? 'جاري تحميل المدن...' 
                      : 'اختر المدينة',
              icon: Icons.location_city,
              value: _selectedCity,
              items: _getCitiesForCountry(),
              enabled: _selectedCountry != null && !_isLoadingCities,
              onChanged: (city) {
                setState(() {
                  _selectedCity = city;
                });
              },
            ),
            
            const SizedBox(height: 24),
            
            buildInputField(
              controller: _websiteController,
              label: 'الموقع الإلكتروني (اختياري)',
              icon: Icons.language,
              keyboardType: TextInputType.url,
            ),
            
            const SizedBox(height: 16),
            
            buildInputField(
              controller: _descriptionController,
              label: 'وصف الشركة (اختياري)',
              icon: Icons.description,
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xff2daae2),
                  const Color(0xff2daae2).withOpacity(0.8),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xff2daae2).withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: const Icon(Icons.business_center, color: Colors.white, size: 40),
          ),
          const SizedBox(height: 20),
          Text(
            'تسجيل شركة جديدة',
            style: GoogleFonts.tajawal(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppColors.getTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'انضم إلى منصتنا وابدأ في نشر الوظائف والعثور على أفضل المواهب',
            style: GoogleFonts.tajawal(
              fontSize: 16,
              color: AppColors.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: isDarkMode 
            ? Theme.of(context).scaffoldBackgroundColor
            : const Color(0xFFF8FAFC),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: Icon(
                Icons.arrow_forward,
                color: AppColors.getTextColor(context),
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildFormCard(),
                  const SizedBox(height: 32),
                  
                  // سياسة الخصوصية وشروط الاستخدام
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      color: AppColors.getCardColor(context),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.black.withOpacity(0.3)
                              : Colors.grey.withOpacity(0.15),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الموافقة والشروط',
                            style: GoogleFonts.tajawal(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // موافقة سياسة الخصوصية
                          Container(
                            decoration: BoxDecoration(
                              color: _agreeToPrivacyPolicy 
                                  ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _agreeToPrivacyPolicy 
                                    ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
                                    : Theme.of(context).dividerColor.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: CheckboxListTile(
                              value: _agreeToPrivacyPolicy,
                              onChanged: (value) {
                                setState(() {
                                  _agreeToPrivacyPolicy = value!;
                                });
                              },
                              title: Row(
                                children: [
                                  Icon(
                                    Icons.privacy_tip_outlined,
                                    size: 20,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const privacy_policy(),
                                          ),
                                        );
                                      },
                                      child: Text(
                                        'الموافقة على سياسة الخصوصية',
                                        style: GoogleFonts.tajawal(
                                          fontSize: 14,
                                          color: Theme.of(context).colorScheme.primary,
                                          decoration: TextDecoration.underline,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              activeColor: Theme.of(context).colorScheme.primary,
                              checkColor: Colors.white,
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // موافقة شروط الاستخدام
                          Container(
                            decoration: BoxDecoration(
                              color: _agreeToTermsOfUse 
                                  ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _agreeToTermsOfUse 
                                    ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
                                    : Theme.of(context).dividerColor.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: CheckboxListTile(
                              value: _agreeToTermsOfUse,
                              onChanged: (value) {
                                setState(() {
                                  _agreeToTermsOfUse = value!;
                                });
                              },
                              title: Row(
                                children: [
                                  Icon(
                                    Icons.description_outlined,
                                    size: 20,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const Terms_ofUsing(),
                                          ),
                                        );
                                      },
                                      child: Text(
                                        'الموافقة على شروط الاستخدام',
                                        style: GoogleFonts.tajawal(
                                          fontSize: 14,
                                          color: Theme.of(context).colorScheme.primary,
                                          decoration: TextDecoration.underline,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              activeColor: Theme.of(context).colorScheme.primary,
                              checkColor: Colors.white,
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _registerCompany,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 8,
                        shadowColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : Text(
                              'تسجيل الشركة',
                              style: GoogleFonts.tajawal(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'لديك حساب بالفعل؟ تسجيل الدخول',
                      style: GoogleFonts.tajawal(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 