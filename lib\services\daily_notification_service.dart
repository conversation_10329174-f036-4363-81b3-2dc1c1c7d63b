import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

/// خدمة الإشعارات المحلية اليومية للوظائف المقترحة
class DailyNotificationService {
  static final DailyNotificationService _instance = DailyNotificationService._internal();
  factory DailyNotificationService() => _instance;
  DailyNotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = 
      FlutterLocalNotificationsPlugin();

  static const int _dailyJobNotificationId = 1000;
  static const String _notificationEnabledKey = 'daily_job_notifications_enabled';

  /// تهيئة نظام الإشعارات
  Future<void> initialize() async {
    try {
      // تهيئة المناطق الزمنية
      tz.initializeTimeZones();
      
      // إعدادات Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTap,
      );

      // طلب إذن الإشعارات على Android 13+
      if (Platform.isAndroid) {
        await _requestAndroidPermissions();
      }

      debugPrint('تم تهيئة نظام الإشعارات المحلية بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة الإشعارات: $e');
    }
  }

  /// طلب إذن الإشعارات على Android
  Future<void> _requestAndroidPermissions() async {
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (androidImplementation != null) {
      await androidImplementation.requestNotificationsPermission();
    }
  }

  /// تفعيل الإشعارات اليومية
  Future<void> enableDailyNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_notificationEnabledKey, true);
      
      await _scheduleDailyNotification();
      debugPrint('تم تفعيل الإشعارات اليومية');
    } catch (e) {
      debugPrint('خطأ في تفعيل الإشعارات اليومية: $e');
    }
  }

  /// إلغاء الإشعارات اليومية
  Future<void> disableDailyNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_notificationEnabledKey, false);
      
      await _flutterLocalNotificationsPlugin.cancel(_dailyJobNotificationId);
      debugPrint('تم إلغاء الإشعارات اليومية');
    } catch (e) {
      debugPrint('خطأ في إلغاء الإشعارات اليومية: $e');
    }
  }

  /// فحص حالة تفعيل الإشعارات
  Future<bool> areNotificationsEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_notificationEnabledKey) ?? true; // مفعل افتراضياً
    } catch (e) {
      debugPrint('خطأ في فحص حالة الإشعارات: $e');
      return false;
    }
  }

  /// جدولة الإشعار اليومي
  Future<void> _scheduleDailyNotification() async {
    try {
      // إلغاء الإشعار السابق إن وجد
      await _flutterLocalNotificationsPlugin.cancel(_dailyJobNotificationId);

      // تحديد موعد الإشعار (9 صباحاً كل يوم)
      final tz.TZDateTime scheduledDate = _nextInstanceOf9AM();

      // إعدادات الإشعار
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'daily_job_suggestions',
        'الوظائف المقترحة اليومية',
        channelDescription: 'إشعار يومي بأفضل الوظائف المطابقة لك',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        styleInformation: BigTextStyleInformation(''),
        enableVibration: true,
        playSound: true,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // جدولة الإشعار
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        _dailyJobNotificationId,
        'وظائف جديدة مناسبة لك! 🔥',
        'تم العثور على وظائف مطابقة لملفك الشخصي. اضغط لعرضها.',
        scheduledDate,
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time, // تكرار يومي
        payload: json.encode({
          'type': 'daily_job_suggestion',
          'action': 'open_suggested_jobs',
        }),
      );

      debugPrint('تم جدولة الإشعار اليومي للساعة 9 صباحاً');
    } catch (e) {
      debugPrint('خطأ في جدولة الإشعار اليومي: $e');
    }
  }

  /// حساب موعد 9 صباحاً القادم
  tz.TZDateTime _nextInstanceOf9AM() {
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    tz.TZDateTime scheduledDate = tz.TZDateTime(tz.local, now.year, now.month, now.day, 9);
    
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
    
    return scheduledDate;
  }

  /// معالجة الضغط على الإشعار
  void _onNotificationTap(NotificationResponse notificationResponse) {
    try {
      if (notificationResponse.payload != null) {
        final payload = json.decode(notificationResponse.payload!);
        final type = payload['type'] as String?;
        
        // التنقل المباشر حسب نوع الإشعار
        _handleNotificationNavigation(payload);
      }
    } catch (e) {
      debugPrint('خطأ في معالجة الضغط على الإشعار: $e');
    }
  }

  /// التعامل مع التنقل بناءً على نوع الإشعار
  void _handleNotificationNavigation(Map<String, dynamic> data) {
    final notificationType = data['type'] as String?;

    switch (notificationType) {
      case 'smart_matched_jobs':
        // التنقل للوظائف المطابقة ذكياً
        _saveNavigationRequest('smart_matched_jobs', data);
        break;
      case 'daily_job_suggestion':
        // التنقل للوظائف المقترحة
        _saveNavigationRequest('suggested_jobs', data);
        break;
      case 'high_match_job':
        // التنقل لوظيفة محددة عالية التطابق
        _saveNavigationRequest('specific_job', data);
        break;
      case 'intelligence_update':
        // التنقل لتحليل الملف الشخصي
        _saveNavigationRequest('profile_insights', data);
        break;
      case 'best_job_notification':
        // التنقل لتفاصيل الوظيفة الأفضل
        _saveNavigationRequest('job_details', data);
        break;
      default:
        debugPrint('نوع إشعار غير معروف: $notificationType');
    }
  }

  /// حفظ طلب التنقل لمعالجته عند فتح التطبيق
  Future<void> _saveNavigationRequest(String destination, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final navigationData = {
        'destination': destination,
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      };
      await prefs.setString('pending_navigation', json.encode(navigationData));
      debugPrint('تم حفظ طلب التنقل إلى: $destination');
    } catch (e) {
      debugPrint('خطأ في حفظ طلب التنقل: $e');
    }
  }

  /// فحص وتنفيذ التنقل المعلق عند فتح التطبيق
  Future<Map<String, dynamic>?> getPendingNavigation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final navigationJson = prefs.getString('pending_navigation');
      
      if (navigationJson != null) {
        // مسح التنقل المعلق
        await prefs.remove('pending_navigation');
        
        final navigationData = json.decode(navigationJson);
        
        // التحقق من أن التنقل حديث (أقل من ساعة)
        final timestamp = DateTime.parse(navigationData['timestamp']);
        final now = DateTime.now();
        
        if (now.difference(timestamp).inHours < 1) {
          return navigationData;
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('خطأ في فحص التنقل المعلق: $e');
      return null;
    }
  }

  /// تشغيل الإشعارات اليومية إذا كانت مفعلة
  Future<void> startDailyNotificationsIfEnabled() async {
    try {
      final isEnabled = await areNotificationsEnabled();
      if (isEnabled) {
        await _scheduleDailyNotification();
        debugPrint('تم تشغيل الإشعارات اليومية');
      }
    } catch (e) {
      debugPrint('خطأ في تشغيل الإشعارات اليومية: $e');
    }
  }

  /// إرسال إشعار للوظائف المطابقة ذكياً
  Future<void> sendSmartMatchedJobsNotification(int jobsCount) async {
    try {
      final isEnabled = await areNotificationsEnabled();
      if (!isEnabled) return;

      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'smart_matched_jobs',
        'الوظائف المطابقة ذكياً',
        channelDescription: 'إشعارات الوظائف المحفوظة تلقائياً بنسبة تطابق عالية',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        styleInformation: BigTextStyleInformation(''),
        enableVibration: true,
        playSound: true,
        color: const Color(0xFF4CAF50),
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.show(
        _dailyJobNotificationId + 2,
        '🧠 وظائف مطابقة ذكياً!',
        'تم العثور على $jobsCount وظيفة عالية التطابق مع ملفك الشخصي. اضغط للعرض.',
        platformChannelSpecifics,
        payload: json.encode({
          'type': 'smart_matched_jobs',
          'jobs_count': jobsCount,
          'action': 'open_smart_matched_jobs',
        }),
      );

      debugPrint('تم إرسال إشعار الوظائف المطابقة ذكياً: $jobsCount وظيفة');
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار الوظائف المطابقة ذكياً: $e');
    }
  }


  /// إرسال إشعار تحديث الذكاء الشخصي
  Future<void> sendIntelligenceUpdateNotification(double newScore, String newLevel) async {
    try {
      final isEnabled = await areNotificationsEnabled();
      if (!isEnabled) return;

      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'intelligence_update',
        'تحديث الذكاء الشخصي',
        channelDescription: 'إشعار بتحديث نقاط الذكاء ومستوى المستخدم',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        styleInformation: BigTextStyleInformation(''),
        enableVibration: true,
        playSound: true,
        color: const Color(0xFF9C27B0),
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      String levelName = _getLevelDisplayName(newLevel);

      await _flutterLocalNotificationsPlugin.show(
        _dailyJobNotificationId + 4,
        '📈 تحسن في نقاط الذكاء!',
        'نقاطك الآن ${(newScore * 100).toStringAsFixed(1)}% - مستوى $levelName\nاضغط لعرض التحليل المفصل',
        platformChannelSpecifics,
        payload: json.encode({
          'type': 'intelligence_update',
          'new_score': newScore,
          'new_level': newLevel,
          'action': 'open_profile_insights',
        }),
      );

      debugPrint('تم إرسال إشعار تحديث الذكاء: ${(newScore * 100).toStringAsFixed(1)}% - $levelName');
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار تحديث الذكاء: $e');
    }
  }

  /// الحصول على اسم المستوى بالعربية
  String _getLevelDisplayName(String level) {
    switch (level) {
      case 'expert': return 'خبير';
      case 'advanced': return 'متقدم';
      case 'intermediate': return 'متوسط';
      case 'beginner': return 'مبتدئ';
      default: return 'غير محدد';
    }
  }

  /// إحصائيات الإشعارات
  Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isEnabled = await areNotificationsEnabled();
      
      return {
        'enabled': isEnabled,
        'next_notification': _nextInstanceOf9AM().toString(),
        'notification_id': _dailyJobNotificationId,
      };
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات الإشعارات: $e');
      return {
        'enabled': false,
        'next_notification': '',
        'notification_id': _dailyJobNotificationId,
      };
    }
  }
} 