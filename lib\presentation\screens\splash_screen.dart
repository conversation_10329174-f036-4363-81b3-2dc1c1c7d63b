﻿import 'dart:math';
import 'package:flutter/material.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Register as an observer to listen to app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // إنشاء متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500), // أبطأ
    );

    // إنشاء رسم متحرك للظهور التدريجي
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    // بدء الرسم المتحرك
    _animationController.repeat(); // تكرار مستمر

  }

  @override
  void dispose() {
    // Remove observer when the widget is disposed
    WidgetsBinding.instance.removeObserver(this);
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage("assets/background2.png"),
            fit: BoxFit.cover,
          ),
        ),
        child: Center(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                // نبض أكثر نعومة (تدرج أهدأ)
                final scale = 0.98 + 0.04 * (0.5 + 0.5 *
                  sin(_animationController.value * 2 * 3.1415926535));
                return Transform.scale(
                  scale: scale,
                  child: SizedBox(
                    height: 200,
                    width: 200,
                    child: Image.asset("assets/logonew.png"),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
