import 'package:wzzff/models/NewsModel.dart';
import 'package:wzzff/Apis/JobsApi.dart';

class NewsRepository {
  final JobsApi _api = JobsApi();

  Future<List<NewsModel>> getNews() async {
    try {
      // معطل مؤقتاً - يحتاج إضافة getNews إلى JobsApi
      // return await _api.getNews();
      return <NewsModel>[]; // قائمة فارغة مؤقتاً
    } catch (e) {
      // يمكن إضافة معالجة الأخطاء هنا
      rethrow;
    }
  }
}