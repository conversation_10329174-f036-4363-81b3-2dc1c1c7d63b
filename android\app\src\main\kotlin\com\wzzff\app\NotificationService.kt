package com.wzzff.app

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL

/**
 * خدمة الإشعارات للأندرويد
 *
 * تستخدم واجهة برمجة التطبيقات الأصلية للأندرويد لعرض الإشعارات
 */
class NotificationService(private val context: Context, private val flutterEngine: FlutterEngine) : MethodChannel.MethodCallHandler {
    private val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "com.wzzff.app/notifications")
    private val notificationManager = NotificationManagerCompat.from(context)

    init {
        methodChannel.setMethodCallHandler(this)
    }

    /**
     * معالجة استدعاءات الدوال من Flutter
     */
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "createNotificationChannel" -> {
                val id = call.argument<String>("id")
                val name = call.argument<String>("name")
                val description = call.argument<String>("description")
                val importance = call.argument<Int>("importance") ?: NotificationManager.IMPORTANCE_DEFAULT
                val enableVibration = call.argument<Boolean>("enableVibration") ?: true
                val enableSound = call.argument<Boolean>("enableSound") ?: true
                val showBadge = call.argument<Boolean>("showBadge") ?: true

                if (id != null && name != null) {
                    createNotificationChannel(id, name, description, importance, enableVibration, enableSound, showBadge)
                    result.success(true)
                } else {
                    result.error("INVALID_ARGUMENTS", "Missing required arguments", null)
                }
            }
            "showNotification" -> {
                val id = call.argument<Int>("id") ?: System.currentTimeMillis().toInt()
                val title = call.argument<String>("title") ?: "إشعار جديد"
                val body = call.argument<String>("body") ?: ""
                val channelId = call.argument<String>("channelId") ?: "high_importance_channel"
                val imageUrl = call.argument<String>("imageUrl")
                val data = call.argument<String>("data")

                CoroutineScope(Dispatchers.Main).launch {
                    showNotification(id, title, body, channelId, imageUrl, data)
                    result.success(true)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    /**
     * إنشاء قناة الإشعارات للأندرويد 8.0 وما فوق
     */
    private fun createNotificationChannel(
        id: String,
        name: String,
        description: String?,
        importance: Int,
        enableVibration: Boolean,
        enableSound: Boolean,
        showBadge: Boolean
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(id, name, importance).apply {
                this.description = description
                this.enableVibration(enableVibration)
                this.setShowBadge(showBadge)

                if (!enableSound) {
                    setSound(null, null)
                }
            }

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * عرض الإشعار
     */
    private suspend fun showNotification(
        id: Int,
        title: String,
        body: String,
        channelId: String,
        imageUrl: String?,
        data: String?
    ) {
        // إنشاء Intent لفتح التطبيق عند النقر على الإشعار
        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)?.apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            if (data != null) {
                putExtra("notification_data", data)
            }
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            id,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // صوت الإشعار الافتراضي
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

        // بناء الإشعار
        val notificationBuilder = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(title)
            .setContentText(body)
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)

        // إضافة الصورة إذا كانت متوفرة
        if (!imageUrl.isNullOrEmpty()) {
            try {
                val bitmap = getBitmapFromUrl(imageUrl)
                if (bitmap != null) {
                    notificationBuilder.setLargeIcon(bitmap)
                    notificationBuilder.setStyle(NotificationCompat.BigPictureStyle().bigPicture(bitmap))
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // عرض الإشعار
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (notificationManager.areNotificationsEnabled()) {
                notificationManager.notify(id, notificationBuilder.build())
            }
        } else {
            notificationManager.notify(id, notificationBuilder.build())
        }
    }

    /**
     * الحصول على صورة من URL
     */
    private suspend fun getBitmapFromUrl(imageUrl: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val url = URL(imageUrl)
            val connection = url.openConnection() as HttpURLConnection
            connection.doInput = true
            connection.connect()
            val input: InputStream = connection.inputStream
            BitmapFactory.decodeStream(input)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}
