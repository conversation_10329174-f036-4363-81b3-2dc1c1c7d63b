import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wzzff/services/google_ad_service.dart';

class ShowCVScreen extends StatefulWidget {
  final String file;
  final String? ltf; // دعم اتجاه اللغة

  const ShowCVScreen({
    Key? key,
    required this.file,
    this.ltf,
  }) : super(key: key);

  @override
  State<ShowCVScreen> createState() => _ShowCVScreenState();
}

class _ShowCVScreenState extends State<ShowCVScreen> {
  bool adShow = false;
  double countDataReceived = 0;
  final GoogleAdService _adService = GoogleAdService();

  @override
  void initState() {
    super.initState();
    // تهيئة خدمة الإعلانات
    _adService.initialize();
  }

  void _showInterstitialAdAndNavigate() async {
    await _adService.showInterstitialAd();
    _navigateToPdfView();
  }

  // وظيفة للتنقل إلى عرض PDF
  void _navigateToPdfView() {
    // يمكن إضافة أي منطق إضافي هنا إذا لزم الأمر
  }

  Future<void> _openUrl(String url) async {
    final Uri launchUri = Uri(
      scheme: 'https',
      path: url,
    );
    await launchUrl(launchUri, mode: LaunchMode.externalApplication);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final ltf = widget.ltf ?? (Directionality.of(context) == TextDirection.rtl ? "0" : "1");
    final isArabic = ltf == "0";
    final primaryColor = isDark ? Colors.deepPurple[200]! : Colors.deepPurple;
    final backgroundColor = isDark ? const Color(0xFF181828) : const Color(0xFFF7F7FA);
    final cardColor = isDark ? const Color(0xFF23233A) : Colors.white;
    final textStyle = isArabic
        ? GoogleFonts.tajawal(fontWeight: FontWeight.bold)
        : GoogleFonts.roboto(fontWeight: FontWeight.bold);

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Text(
          isArabic ? "السيرة الذاتية" : "My CV",
          style: textStyle.copyWith(fontSize: 22, color: primaryColor),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            tooltip: isArabic ? "تحميل" : "Download",
            icon: Icon(Icons.download, color: primaryColor),
            onPressed: () async {
              final url = widget.file.startsWith("http") ? widget.file : "https://${widget.file}";
              if (await canLaunchUrl(Uri.parse(url))) {
                await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
              }
            },
          ),
          IconButton(
            tooltip: isArabic ? "مشاركة" : "Share",
            icon: Icon(Icons.share, color: primaryColor),
            onPressed: () async {
              await Share.share(widget.file, subject: isArabic ? "السيرة الذاتية" : "My CV");
            },
          ),
          IconButton(
            icon: Icon(Icons.arrow_forward, color: primaryColor),
            onPressed: () => Navigator.pop(context),
            tooltip: isArabic ? "رجوع" : "Back",
          ),
        ],
      ),
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          child: Card(
            elevation: 6,
            color: cardColor,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(18),
              child: PDF(
                enableSwipe: true,
                swipeHorizontal: false,
                autoSpacing: true,
                pageFling: true,
              ).fromUrl(
                widget.file.startsWith("http") ? widget.file : "https://${widget.file}",
                placeholder: (progress) => Center(
                  child: Container(
                    padding: const EdgeInsets.all(25),
                    width: 320,
                    decoration: BoxDecoration(
                      color: cardColor,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(20),
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.picture_as_pdf_rounded, size: 50, color: Colors.deepPurple),
                        const SizedBox(height: 20),
                        Text(
                          isArabic ? "جاري تحميل السيرة الذاتية" : "Loading CV...",
                          style: textStyle.copyWith(color: primaryColor, fontSize: 18),
                        ),
                        const SizedBox(height: 25),
                        LinearPercentIndicator(
                          lineHeight: 12.0,
                          width: 220,
                          percent: (progress / 100).clamp(0.0, 1.0),
                          backgroundColor: Colors.grey.withAlpha(51),
                          progressColor: primaryColor,
                          barRadius: const Radius.circular(10),
                          padding: EdgeInsets.zero,
                          animation: true,
                          animateFromLastPercent: true,
                          center: Text(
                            "${progress.toStringAsFixed(0)}%",
                            style: textStyle.copyWith(color: Colors.white, fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                errorWidget: (error) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 50),
                      const SizedBox(height: 10),
                      Text(
                        isArabic ? 'حدث خطأ أثناء تحميل الملف: $error' : 'Error loading file: $error',
                        style: textStyle.copyWith(fontSize: 16, color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton.icon(
                        onPressed: () => setState(() {}),
                        icon: const Icon(Icons.refresh),
                        label: Text(isArabic ? "إعادة المحاولة" : "Retry", style: textStyle),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
