# توثيق مشروع وظف دوت كوم

## هيكل المجلدات

### 1. مجلد `lib/Apis`
يحتوي على ملفات التعامل مع واجهات برمجة التطبيق (APIs)

#### `LoginAndCheckAndRegi.dart`
- **الدوال الرئيسية:**
  - `seeker_login`: تسجيل دخول الباحث عن عمل
    - تستخدم في: `LoginScreenPage.dart`
    - الوظيفة: التحقق من بيانات الدخول وإرسال الوظائف المخزنة محلياً بعد تسجيل الدخول
  
  - `rigisterSeeker`: تسجيل باحث عن عمل جديد
    - تستخدم في: `Registeration_page.dart`
    - الوظيفة: إنشاء حساب جديد للباحث عن عمل
  
  - `deleteAccount`: حذف الحساب
    - تستخدم في: `SeekerScreenProfile.dart`
    - الوظيفة: حذف بيانات المستخدم من التخزين المحلي
  
  - `add_to_fiv`: إضافة وظيفة للمفضلة
    - تستخدم في: `DetailJobScreen.dart`
    - الوظيفة: إضافة وظيفة إلى قائمة المفضلة للمستخدم

#### `ProfileApi.dart`
- **الدوال الرئيسية:**
  - `sendGuestAppliedJobsToServer`: إرسال الوظائف المخزنة محلياً إلى السيرفر
    - تستخدم في: `LoginAndCheckAndRegi.dart`
    - الوظيفة: نقل الوظائف التي تقدم لها المستخدم وهو ضيف إلى السيرفر بعد تسجيل الدخول
  
  - `getAppliedJobs`: جلب الوظائف التي تقدم لها المستخدم
    - تستخدم في: `AppliedJobsScreen.dart`
    - الوظيفة: جلب قائمة الوظائف التي تقدم لها المستخدم من السيرفر
  
  - `updateProfile`: تحديث الملف الشخصي
    - تستخدم في: `SeekerScreenProfile.dart`
    - الوظيفة: تحديث بيانات الملف الشخصي للمستخدم

### 2. مجلد `lib/models`
يحتوي على نماذج البيانات المستخدمة في التطبيق

#### `JobApplication.dart`
- **الخصائص:**
  - `id`: معرف الوظيفة
  - `slug`: الرابط المختصر للوظيفة
  - `title`: عنوان الوظيفة
  - `company_name`: اسم الشركة
  - `status`: حالة التقديم
  - وغيرها من البيانات المتعلقة بالوظيفة والتقديم

#### `JobModel.dart`
- **الخصائص:**
  - `title`: عنوان الوظيفة
  - `description`: وصف الوظيفة
  - `salary`: الراتب
  - وغيرها من بيانات الوظيفة

### 3. مجلد `lib/presentation/screens`
يحتوي على شاشات التطبيق

#### `job_seeker/AppliedJobsScreen.dart`
- **الوظائف:**
  - عرض الوظائف التي تقدم لها المستخدم
  - تصفية الوظائف حسب الحالة
  - عرض تفاصيل التقديم لكل وظيفة

#### `job_seeker/SeekerScreenProfile.dart`
- **الوظائف:**
  - عرض وتعديل الملف الشخصي
  - إدارة السيرة الذاتية
  - حذف الحساب

#### `jobs/DetailJobScreen.dart`
- **الوظائف:**
  - عرض تفاصيل الوظيفة
  - التقديم على الوظيفة
  - إضافة الوظيفة للمفضلة

### 4. مجلد `lib/services`
يحتوي على خدمات التطبيق

#### `google_ad_service.dart`
- **الوظائف:**
  - إدارة إعلانات Google
  - تهيئة الإعلانات
  - عرض الإعلانات في التطبيق

#### `firebase_messaging_service.dart`
- **الوظائف:**
  - إدارة الإشعارات
  - الاشتراك في المواضيع
  - معالجة الإشعارات الواردة

### 5. مجلد `lib/core`
يحتوي على المكونات الأساسية للتطبيق

#### `constants/Constants.dart`
- **المحتوى:**
  - روابط API
  - الثوابت العامة
  - إعدادات التطبيق

#### `providers/app_state_provider.dart`
- **الوظائف:**
  - إدارة حالة التطبيق
  - تخزين البيانات المشتركة
  - توفير البيانات للشاشات

#### `utils/app_messages.dart`
- **الوظائف:**
  - عرض رسائل النجاح
  - عرض رسائل الخطأ
  - إدارة التنبيهات 