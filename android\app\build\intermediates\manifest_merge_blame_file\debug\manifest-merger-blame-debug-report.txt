1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.wzzff"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:2:5-66
15-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:2:22-64
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-77
16-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-74
17    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
17-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-89
17-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-86
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
18-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-78
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-66
19-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-63
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-80
20-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-77
21
22    <queries>
22-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-15:15
23        <intent>
23-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-14:18
24            <action android:name="android.intent.action.GET_CONTENT" />
24-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-72
24-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-69
25
26            <data android:mimeType="*/*" />
26-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-44
26-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-5.5.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:19-41
27        </intent>
28    </queries>
29
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-68
30-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-65
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-79
31-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-76
32    <uses-permission
32-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:10:5-12:38
33        android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
33-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:11:9-77
34        android:maxSdkVersion="22" /> <!-- Required by older versions of Google Play services to create IID tokens -->
34-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:12:9-35
35    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
35-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
35-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
36
37    <permission
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.wzzff.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.wzzff.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
42    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
43    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
44    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
45    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
46    <!-- for Samsung -->
47    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
47-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
47-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
48    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
49    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
50    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
50-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
50-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
51    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
51-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
51-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
52    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
52-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
52-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
53    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
53-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
53-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
54    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
54-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
54-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
55    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
55-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
55-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
56    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
56-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
56-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
57    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
57-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
57-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
58    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
58-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
58-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
59    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
59-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
59-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
60    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
60-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
60-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
61    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
61-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
61-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
62    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\transforms-3\8dc93f4607da95b7f9c5249b3f8fa8c5\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
63
64    <application
64-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:3:4-36:19
65        android:name="androidx.multidex.MultiDexApplication"
65-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:4:9-61
66        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
66-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
67        android:debuggable="true"
68        android:extractNativeLibs="true"
69        android:icon="@mipmap/ic_launcher"
69-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:6:9-43
70        android:label="وظف دوت كوم" >
70-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:5:9-36
71        <activity
71-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:7:9-27:20
72            android:name="com.example.wzzff.MainActivity"
72-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:8:13-41
73            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
73-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:12:13-163
74            android:exported="true"
74-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:9:13-36
75            android:hardwareAccelerated="true"
75-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:13:13-47
76            android:launchMode="singleTop"
76-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:10:13-43
77            android:theme="@style/LaunchTheme"
77-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:11:13-47
78            android:windowSoftInputMode="adjustResize" >
78-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:14:13-55
79
80            <!--
81                 Specifies an Android theme to apply to this Activity as soon as
82                 the Android process has started. This theme is visible to the user
83                 while the Flutter UI initializes. After that, this theme continues
84                 to determine the Window background behind the Flutter UI.
85            -->
86            <meta-data
86-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:19:13-22:17
87                android:name="io.flutter.embedding.android.NormalTheme"
87-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:20:15-70
88                android:resource="@style/NormalTheme" />
88-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:21:15-52
89
90            <intent-filter>
90-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:23:13-26:29
91                <action android:name="android.intent.action.MAIN" />
91-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:24:17-68
91-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:24:25-66
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:25:17-76
93-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:25:27-74
94            </intent-filter>
95        </activity>
96        <!--
97             Don't delete the meta-data below.
98             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
99        -->
100        <meta-data
100-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:30:9-32:33
101            android:name="flutterEmbedding"
101-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:31:13-44
102            android:value="2" />
102-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:32:13-30
103        <meta-data
103-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:33:9-35:69
104            android:name="com.google.android.gms.ads.APPLICATION_ID"
104-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:34:13-69
105            android:value="ca-app-pub-3940256099942544~3347511713" />
105-->C:\myapp\wzzff\android\app\src\main\AndroidManifest.xml:35:13-67
106
107        <receiver
107-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-15:39
108            android:name="me.carda.awesome_notifications.DartNotificationActionReceiver"
108-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-89
109            android:exported="true" />
109-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-36
110        <receiver
110-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-18:39
111            android:name="me.carda.awesome_notifications.DartDismissedNotificationReceiver"
111-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-92
112            android:exported="true" />
112-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-36
113        <receiver
113-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-21:39
114            android:name="me.carda.awesome_notifications.DartScheduledNotificationReceiver"
114-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-92
115            android:exported="true" />
115-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
116        <receiver
116-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:9-36:20
117            android:name="me.carda.awesome_notifications.DartRefreshSchedulesReceiver"
117-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-87
118            android:enabled="true"
118-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-35
119            android:exported="true" >
119-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-36
120            <intent-filter>
120-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-35:29
121                <category android:name="android.intent.category.DEFAULT" />
121-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:17-76
121-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:27-73
122
123                <action android:name="android.intent.action.BOOT_COMPLETED" />
123-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-79
123-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:25-76
124                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
124-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-86
124-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-83
125                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
125-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-84
125-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-81
126                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
126-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-82
126-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:25-79
127                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
127-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:17-82
127-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:25-79
128                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
128-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:17-107
128-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:25-104
129            </intent-filter>
130        </receiver>
131
132        <service
132-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:9-41:72
133            android:name="me.carda.awesome_notifications.DartBackgroundService"
133-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:13-80
134            android:exported="false"
134-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:13-37
135            android:permission="android.permission.BIND_JOB_SERVICE" />
135-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:13-69
136        <service
136-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:9-47:43
137            android:name="me.carda.awesome_notifications.core.services.ForegroundService"
137-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-90
138            android:enabled="true"
138-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-35
139            android:exported="false"
139-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-37
140            android:foregroundServiceType="phoneCall"
140-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-54
141            android:stopWithTask="true" />
141-->[:awesome_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\awesome_notifications-0.8.3\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-40
142        <service
142-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
143            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
143-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
144            android:exported="false"
144-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
145            android:permission="android.permission.BIND_JOB_SERVICE" />
145-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
146        <service
146-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
147            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
147-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
148            android:exported="false" >
148-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
149            <intent-filter>
149-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
150                <action android:name="com.google.firebase.MESSAGING_EVENT" />
150-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
150-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
151            </intent-filter>
152        </service>
153
154        <receiver
154-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
155            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
155-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
156            android:exported="true"
156-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
157            android:permission="com.google.android.c2dm.permission.SEND" >
157-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
158            <intent-filter>
158-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
159                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
159-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
159-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
160            </intent-filter>
161        </receiver>
162
163        <service
163-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:9-39:19
164            android:name="com.google.firebase.components.ComponentDiscoveryService"
164-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:18-89
165            android:directBootAware="true"
165-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
166            android:exported="false" >
166-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
167            <meta-data
167-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
168                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
168-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
170            <meta-data
170-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
171                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
171-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
173            <meta-data
173-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
174                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
174-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
176            <meta-data
176-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
177                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
177-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
179            <meta-data
179-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d4a00debe7102074e4cebb1b35f958f\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
180                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
180-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d4a00debe7102074e4cebb1b35f958f\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d4a00debe7102074e4cebb1b35f958f\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
182            <meta-data
182-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8b54019fc6fba2c5c4052db92937ea\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
183                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
183-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8b54019fc6fba2c5c4052db92937ea\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8b54019fc6fba2c5c4052db92937ea\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
185            <meta-data
185-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8b54019fc6fba2c5c4052db92937ea\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
186                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
186-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8b54019fc6fba2c5c4052db92937ea\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b8b54019fc6fba2c5c4052db92937ea\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
188            <meta-data
188-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\d6c21975f32693b669d0062b23b2a7e0\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
189                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
189-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\d6c21975f32693b669d0062b23b2a7e0\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\d6c21975f32693b669d0062b23b2a7e0\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
191            <meta-data
191-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
192                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
192-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
194            <meta-data
194-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\76ba32662b8300299ddeeeab6960d0d8\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
195                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
195-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\76ba32662b8300299ddeeeab6960d0d8\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\76ba32662b8300299ddeeeab6960d0d8\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
197        </service>
198
199        <provider
199-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
200            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
200-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
201            android:authorities="com.example.wzzff.flutterfirebasemessaginginitprovider"
201-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
202            android:exported="false"
202-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
203            android:initOrder="99" />
203-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
204
205        <activity
205-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.14\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
206            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
206-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.14\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
207            android:exported="false"
207-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.14\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
208            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
208-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.14\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
209
210        <service
210-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:21:9-28:19
211            android:name="me.carda.awesome_notifications.core.managers.StatusBarManager"
211-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:22:13-89
212            android:exported="true"
212-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:23:13-36
213            android:label="My Notification Listener Service" >
213-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:24:13-61
214            <intent-filter>
214-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:25:13-27:29
215                <action android:name="android.service.notification.NotificationListenerService" />
215-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:26:17-99
215-->[me.carda:AndroidAwnCore:0.8.3] C:\Users\<USER>\.gradle\caches\transforms-3\b38dbe597880c41572c470bf1f45a777\transformed\jetified-AndroidAwnCore-0.8.3\AndroidManifest.xml:26:25-96
216            </intent-filter>
217        </service>
218        <service
218-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8478d99fbbcb5c29686ee6eb91797679\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
219            android:name="androidx.room.MultiInstanceInvalidationService"
219-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8478d99fbbcb5c29686ee6eb91797679\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
220            android:directBootAware="true"
220-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8478d99fbbcb5c29686ee6eb91797679\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
221            android:exported="false" />
221-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8478d99fbbcb5c29686ee6eb91797679\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
222
223        <receiver
223-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
224            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
224-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
225            android:exported="true"
225-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
226            android:permission="com.google.android.c2dm.permission.SEND" >
226-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
227            <intent-filter>
227-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
228                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
228-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
228-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
229            </intent-filter>
230
231            <meta-data
231-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
232                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
232-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
233                android:value="true" />
233-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
234        </receiver>
235        <!--
236             FirebaseMessagingService performs security checks at runtime,
237             but set to not exported to explicitly avoid allowing another app to call it.
238        -->
239        <service
239-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
240            android:name="com.google.firebase.messaging.FirebaseMessagingService"
240-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
241            android:directBootAware="true"
241-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
242            android:exported="false" >
242-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bec3857ab93f1d6de7e3fafcb52e8f03\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
243            <intent-filter android:priority="-500" >
243-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
244                <action android:name="com.google.firebase.MESSAGING_EVENT" />
244-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
244-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-14.7.10\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
245            </intent-filter>
246        </service>
247
248        <uses-library
248-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
249            android:name="androidx.window.extensions"
249-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
250            android:required="false" />
250-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
251        <uses-library
251-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
252            android:name="androidx.window.sidecar"
252-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
253            android:required="false" />
253-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
254
255        <activity
255-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6dadd0bfa2d44ddeab91e958645034df\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
256            android:name="com.google.android.gms.common.api.GoogleApiActivity"
256-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6dadd0bfa2d44ddeab91e958645034df\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
257            android:exported="false"
257-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6dadd0bfa2d44ddeab91e958645034df\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
258            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
258-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6dadd0bfa2d44ddeab91e958645034df\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
259
260        <provider
260-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
261            android:name="com.google.firebase.provider.FirebaseInitProvider"
261-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
262            android:authorities="com.example.wzzff.firebaseinitprovider"
262-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
263            android:directBootAware="true"
263-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
264            android:exported="false"
264-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
265            android:initOrder="100" />
265-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\2de76bdd9c0b8aa97cf0e8003945b523\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
266        <provider
266-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3559f99e4813a171b5493c8f32a3eaf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
267            android:name="androidx.startup.InitializationProvider"
267-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3559f99e4813a171b5493c8f32a3eaf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
268            android:authorities="com.example.wzzff.androidx-startup"
268-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3559f99e4813a171b5493c8f32a3eaf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
269            android:exported="false" >
269-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3559f99e4813a171b5493c8f32a3eaf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
270            <meta-data
270-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3559f99e4813a171b5493c8f32a3eaf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
271                android:name="androidx.emoji2.text.EmojiCompatInitializer"
271-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3559f99e4813a171b5493c8f32a3eaf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
272                android:value="androidx.startup" />
272-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3559f99e4813a171b5493c8f32a3eaf\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
273            <meta-data
273-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
274                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
274-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
275                android:value="androidx.startup" />
275-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
276            <meta-data
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
277                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
278                android:value="androidx.startup" />
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
279        </provider>
280
281        <meta-data
281-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\96004e15647bc525170b7742251f128d\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
282            android:name="com.google.android.gms.version"
282-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\96004e15647bc525170b7742251f128d\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
283            android:value="@integer/google_play_services_version" />
283-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\96004e15647bc525170b7742251f128d\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
284
285        <receiver
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
286            android:name="androidx.profileinstaller.ProfileInstallReceiver"
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
287            android:directBootAware="false"
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
288            android:enabled="true"
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
289            android:exported="true"
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
290            android:permission="android.permission.DUMP" >
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
291            <intent-filter>
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
292                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
293            </intent-filter>
294            <intent-filter>
294-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
295                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
295-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
295-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
296            </intent-filter>
297            <intent-filter>
297-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
298                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
299            </intent-filter>
300            <intent-filter>
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
301                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
302            </intent-filter>
303        </receiver>
304
305        <service
305-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\58a9381ab1c88a84febf9ed547c1ee3d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
306            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
306-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\58a9381ab1c88a84febf9ed547c1ee3d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
307            android:exported="false" >
307-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\58a9381ab1c88a84febf9ed547c1ee3d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
308            <meta-data
308-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\58a9381ab1c88a84febf9ed547c1ee3d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
309                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
309-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\58a9381ab1c88a84febf9ed547c1ee3d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
310                android:value="cct" />
310-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\58a9381ab1c88a84febf9ed547c1ee3d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
311        </service>
312        <service
312-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\4a0d58238ce333437c7732eb884ff985\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
313            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
313-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\4a0d58238ce333437c7732eb884ff985\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
314            android:exported="false"
314-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\4a0d58238ce333437c7732eb884ff985\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
315            android:permission="android.permission.BIND_JOB_SERVICE" >
315-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\4a0d58238ce333437c7732eb884ff985\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
316        </service>
317
318        <receiver
318-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\4a0d58238ce333437c7732eb884ff985\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
319            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
319-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\4a0d58238ce333437c7732eb884ff985\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
320            android:exported="false" />
320-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\4a0d58238ce333437c7732eb884ff985\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
321    </application>
322
323</manifest>
