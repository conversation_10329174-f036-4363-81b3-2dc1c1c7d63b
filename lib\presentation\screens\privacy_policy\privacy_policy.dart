import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class privacy_policy extends StatelessWidget {
  const privacy_policy({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xff2daae2),
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
            ),
            onPressed: () => Navigator.pop(context),
            tooltip: 'رجوع',
          ),
        ],
        title: Text(
          'سياسة الخصوصية',
          style: GoogleFonts.tajawal(
            fontSize: 20, 
            fontWeight: FontWeight.bold,
            color: Colors.white
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xff2daae2).withOpacity(0.05),
              Colors.white,
            ],
          ),
        ),
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: ListView(
              children: [
                _buildHeaderCard(),
                
                _buildSectionTitle('سياسة الخصوصية'),
                _buildSectionContent(
                  'نحن نحمي خصوصيتك طوال الوقت بأعلي درجات الأمان المتوفر عالميا بأقوي السيرفرات المؤمنة بشكل كامل ولا نقوم بنقل او طرح اى من بيانات الشخصية التى تقوم بتزويدنا بها.\n\nيلتزم تطبيق ذا فوكس الإلكتروني بمسؤولية معالجة المعلومات والبيانات التي نجمعها من خلال استخدامك لمنصة التعليم الإلكتروني. لا يجمع تطبيق منصة ذا فوكس اب معلومات تعريفية شخصية.'
                ),
                
                _buildSectionTitle('المعلومات التي تُجمع عنك'),
                _buildSectionContent(
                  'إذا كنت عضوًا مسجلاً في المنصة، فستكون قد قدمت معلومات تعريفية شخصية إلى المنصة مثل رقم هاتفك واسمك. فإننا لن نستخدم هذه المعلومات الا لتقديم الخدمات التعليمية وتحسين جودة التعليم والتواصل معك من خلال المركز او من خلال المحاضر.'
                ),
                
                _buildSectionTitle('ملفات تعريف الارتباط'),
                _buildSectionContent(
                  'نستخدم ملفات تعريف الارتباط لتحسين تجربة المستخدم وتخصيص المحتوى. يمكنك تعطيل ملفات تعريف الارتباط في إعدادات المتصفح الخاص بك، ولكن قد يؤثر ذلك على وظائف معينة في التطبيق.'
                ),
                
                _buildSectionTitle('تخزين المعلومات'),
                _buildSectionContent(
                  'نعالج بسريةٍ تامةٍ المعلومات التي نعلمها عنك عند زيارة موقعنا الإلكتروني. وتُخزّن المعلومات الخاصة، كإحداثيات البريد الإلكتروني، في قاعدة بيانات المنصة، وتخضع للحماية بموجب خدمات منع محركات البحث من قراءة المحتوى أو الاطلاع غير المشروع.'
                ),
                
                _buildSectionTitle('حذف معلوماتك الشخصية أو تصحيحها'),
                _buildSectionContent(
                  'بصفتك مستخدمًا للمنصة، يمكنك تسجيل الدخول باستخدام رقم هاتفك "جوالك الشخصي" وكلمة مرورك لعرض معلوماتك الشخصية من خلال ملف المستخدم الخاص بك. يبذل القائمون على منصة ذا فوكس التعليمية كافة الجهود للحفاظ على خصوصيتك وسلامتك عبر التحقق من هويتك عندما تستخدم نظامنا.\n\nيمكنك الاتصال بالدعم الفني الخاص بالمنصة في أي وقت، وذلك عبر إرسال رسالة إلكترونية إلى مكتب دعم المنصة على العنوان الألكتروني <EMAIL> لطرح أي أسئلة أو مخاوف.'
                ),
                
                _buildSectionTitle('الإفصاح عن المعلومات'),
                _buildSectionContent(
                  'يلتزم تطبيق ذا فوكس اب للتعليم الإلكتروني بمعالجة المعلومات والبيانات التي نجمعها من خلال المنصة بصورة مسؤولة. لا نبيع معلوماتك التعريفية الشخصية أو نتاجر بها.\n\nإنك توافق على أنه يجوز للمنصة الوصول إلى أي معلوماتٍ أو محتوى تقدمه والاحتفاظ به والإفصاح عنه إذا طُلب منا ذلك بموجب القانون أو عند الاعتقاد بحسن نية بأن هذا الوصول أو الاحتفاظ أو الإفصاح ضروريٌ على نحوٍ معقول من أجل:\n\n- الامتثال للإجراءات القانونية\n- إنفاذ هذه الاتفاقية\n- الرد على الادعاءات بأن أي محتوى ينتهك حقوق أطراف ثالثة\n- الرد على طلباتك لتلقي الخدمة\n- حماية حقوق مستخدمي المنصة الخاصة والعامة أو ممتلكاتهم أو سلامتهم الشخصية\n\nويشمل ذلك محتوى الإشهار المعروض على صفحة ملفك التعريفي الخاص بالمستخدم.'
                ),
                
                _buildSectionTitle('مواقع الويب الخاصة بالأطراف الخارجية'),
                _buildSectionContent(
                  'يمكن أن تضم المنصة روابط إلى مواقع لأطراف خارجية محددة. نملك مثل هذه المواقع المرتبطة أو يراقبها أو يشغلها، وهو غير مسؤولٍ عن سياسات الخصوصية لمثل تلك المواقع أو إجراءاتها.\n\nحيث يمكن أن تختلف سياسات الخصوصية وإجراءاتها في تلك المواقع عن سياسات الخصوصية وإجراءاتها لدينا. وإنك تستخدم هذه المواقع المرتبطة على مسؤوليتك الخاصة بالكامل. نحثك على قراءة سياسات الخصوصية لهذه المواقع المرتبطة قبل الكشف عن معلوماتك التعريفية الشخصية عليها.'
                ),
                
                _buildSectionTitle('المخاوف'),
                _buildSectionContent(
                  'إذا كانت لديك مخاوف بشأن المنصة، فيرجى الاتصال بفريق المنصة الوطنية للتعليم الإلكتروني على العنوان: <EMAIL>.'
                ),
                
                _buildSectionTitle('تبادل البيانات والمعلومات'),
                _buildSectionContent(
                  'يجوز للمنصة تبادل البيانات والمعلومات الخاصة بك مع طرف ثالث في حدود إرسال رسائل تعريفية وتوعوية وتعليمية ونحوها أو لأغراض تحليلية.'
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'سياسة الخصوصية',
              style: GoogleFonts.tajawal(
                fontSize: 24, 
                fontWeight: FontWeight.bold,
                color: const Color(0xff2daae2)
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'نحن نقدر خصوصيتك! تعرف على سياسات الخصوصية الخاصة بنا.',
              style: GoogleFonts.tajawal(
                fontSize: 16, 
                color: Colors.black
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Text(
        title,
        style: GoogleFonts.tajawal(
          fontSize: 20, 
          fontWeight: FontWeight.bold,
          color: const Color(0xff2daae2)
        ),
      ),
    );
  }

  Widget _buildSectionContent(String content) {
    return Text(
      content,
      style: GoogleFonts.tajawal(
        fontSize: 16, 
        color: Colors.black87,
        height: 1.5,
      ),
      textAlign: TextAlign.justify,
    );
  }
}
