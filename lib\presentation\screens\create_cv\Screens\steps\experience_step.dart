import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:wzzff/presentation/screens/create_cv/Components/FieldTxt.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';

class ExperienceStep extends StatefulWidget {
  final CvModel cvData;
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic> currentData;
  final List<Map<String, dynamic>> experienceList;
  final Function(List<Map<String, dynamic>>) onExperienceListChanged;

  const ExperienceStep({
    Key? key,
    required this.cvData,
    required this.onDataChanged,
    required this.currentData,
    required this.experienceList,
    required this.onExperienceListChanged,
  }) : super(key: key);

  @override
  State<ExperienceStep> createState() => _ExperienceStepState();
}

class _ExperienceStepState extends State<ExperienceStep> {
  late TextEditingController _expTitleController;
  late TextEditingController _expDesController;
  late TextEditingController _expCompanyNameController;

  String? _expFrom;
  String? _expEnd;
  int? _editingIndex;

  final List<String> yearsOption = [for (int i = 1980; i <= 2025; i++) i.toString()];

  @override
  void initState() {
    super.initState();
    _expTitleController = TextEditingController(text: widget.currentData['exp_title']);
    _expDesController = TextEditingController(text: widget.currentData['exp_des']);
    _expCompanyNameController = TextEditingController(text: widget.currentData['exp_company_name']);

    _expFrom = widget.currentData['exp_from'];
    _expEnd = widget.currentData['exp_end'];
  }

  @override
  void dispose() {
    _expTitleController.dispose();
    _expDesController.dispose();
    _expCompanyNameController.dispose();
    super.dispose();
  }

  void _updateData(String field, String? value) {
    final newData = Map<String, dynamic>.from(widget.currentData);
    newData[field] = value;
    widget.onDataChanged(newData);
  }

  void _addExperience() {
    if (_expFrom == null || _expEnd == null ||
        _expTitleController.text.isEmpty ||
        _expDesController.text.isEmpty ||
        _expCompanyNameController.text.isEmpty) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(Lang().getWord("fill_all_fields", widget.cvData.ltf)),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final experience = {
      "exp_from": _expFrom,
      "exp_end": _expEnd,
      "exp_title": _expTitleController.text,
      "exp_des": _expDesController.text,
      "exp_company_name": _expCompanyNameController.text,
    };

    final newList = List<Map<String, dynamic>>.from(widget.experienceList);

    if (_editingIndex != null) {
      newList[_editingIndex!] = experience;
      _editingIndex = null;
    } else {
      newList.add(experience);
    }

    widget.onExperienceListChanged(newList);

    // إعادة تعيين الحقول
    _expTitleController.clear();
    _expDesController.clear();
    _expCompanyNameController.clear();
    setState(() {
      _expFrom = null;
      _expEnd = null;
    });
  }

  void _editExperience(int index) {
    final experience = widget.experienceList[index];

    setState(() {
      _expFrom = experience["exp_from"];
      _expEnd = experience["exp_end"];
      _expTitleController.text = experience["exp_title"];
      _expDesController.text = experience["exp_des"];
      _expCompanyNameController.text = experience["exp_company_name"];
      _editingIndex = index;
    });
  }

  void _deleteExperience(int index) {
    final newList = List<Map<String, dynamic>>.from(widget.experienceList);
    newList.removeAt(index);
    widget.onExperienceListChanged(newList);
  }

  Widget _buildTipsSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = Theme.of(context).textTheme.bodyLarge?.color;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Theme.of(context).cardColor.withValues(alpha: 128)
            : const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDarkMode
              ? Colors.grey.withValues(red: 100, green: 100, blue: 100, alpha: 77)
              : Colors.grey.withValues(red: 200, green: 200, blue: 200, alpha: 77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb_outline,
                color: Color(0xFFFF9800),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                "${Lang().getWord("tips", widget.cvData.ltf)} ${Lang().getWord("experience", widget.cvData.ltf)}",
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          _buildTipItem(Lang().getWord("experience_tip1", widget.cvData.ltf)),
          _buildTipItem(Lang().getWord("experience_tip2", widget.cvData.ltf)),
          _buildTipItem(Lang().getWord("experience_tip3", widget.cvData.ltf)),
          _buildTipItem(Lang().getWord("experience_tip4", widget.cvData.ltf)),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    final textColor = Theme.of(context).textTheme.bodyMedium?.color;

    return Padding(
      padding: const EdgeInsets.only(bottom: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check_circle,
            color: Color(0xFF4CAF50),
            size: 14,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final cardColor = Theme.of(context).cardColor;
    final textColor = Theme.of(context).textTheme.bodyLarge?.color;

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 15),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [primaryColor, primaryColor.withOpacity(0.7)],
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.10),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(Icons.work_outline, color: Colors.white, size: 28),
                const SizedBox(width: 10),
                Text(
                  Lang().getWord("experience", widget.cvData.ltf),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 18),

          // قسم النصائح
          _buildTipsSection(),
          const SizedBox(height: 20),

          // قائمة الخبرات المضافة
          if (widget.experienceList.isNotEmpty) ...[
            Text(
              Lang().getWord("added_experiences", widget.cvData.ltf),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 10),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: widget.experienceList.length,
              itemBuilder: (context, index) {
                final experience = widget.experienceList[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 10),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    title: Text(
                      experience["exp_title"],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(experience["exp_company_name"]),
                        Text("${experience["exp_from"]} - ${experience["exp_end"]}"),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () => _editExperience(index),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _deleteExperience(index),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            const Divider(height: 30),
          ],

          // نموذج إضافة خبرة جديدة
          Text(
            _editingIndex != null
                ? Lang().getWord("edit_experience", widget.cvData.ltf)
                : Lang().getWord("add_new_exp", widget.cvData.ltf),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 16),

          // سنة البداية
          Text(
            "${Lang().getWord("exp_from", widget.cvData.ltf)}*",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          FormBuilderDropdown(
            name: 'exp_from',
            initialValue: _expFrom,
            decoration: InputDecoration(
              filled: true,
              fillColor: cardColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              prefixIcon: const Icon(Icons.calendar_today),
              hintText: Lang().getWord("choose", widget.cvData.ltf),
            ),
            items: yearsOption
                .map((year) => DropdownMenuItem(
                      value: year,
                      child: Text(year),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _expFrom = value;
              });
              _updateData('exp_from', value);
            },
          ),
          const SizedBox(height: 16),

          // سنة النهاية
          Text(
            "${Lang().getWord("exp_end", widget.cvData.ltf)}*",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          FormBuilderDropdown(
            name: 'exp_end',
            initialValue: _expEnd,
            decoration: InputDecoration(
              filled: true,
              fillColor: cardColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              prefixIcon: const Icon(Icons.calendar_today),
              hintText: Lang().getWord("choose", widget.cvData.ltf),
            ),
            items: yearsOption
                .map((year) => DropdownMenuItem(
                      value: year,
                      child: Text(year),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _expEnd = value;
              });
              _updateData('exp_end', value);
            },
          ),
          const SizedBox(height: 16),

          // المسمى الوظيفي
          FieldTxt(
            fieldLabel: Lang().getWord("exp_title", widget.cvData.ltf),
            handlerInput: (value) => _updateData('exp_title', value),
            fieldname: 'exp_title',
            hint: Lang().getWord("exp_title_hint", widget.cvData.ltf),
            iconField: const Icon(Icons.work),
            inputRequired: widget.cvData.exp_title == 1,
            textEditingController: _expTitleController,
            ltf: widget.cvData.ltf,
          ),
          const SizedBox(height: 16),

          // وصف الوظيفة
          Text(
            Lang().getWord("exp_des", widget.cvData.ltf),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _expDesController,
            maxLines: 3,
            style: Theme.of(context).textTheme.bodyLarge,
            decoration: InputDecoration(
              filled: isDarkMode,
              fillColor: isDarkMode ? Theme.of(context).inputDecorationTheme.fillColor : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: primaryColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: isDarkMode ? Colors.red[300]! : Colors.red),
              ),
              hintText: Lang().getWord("exp_des_hint", widget.cvData.ltf),
              hintStyle: TextStyle(
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                fontSize: 15,
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
            onChanged: (value) => _updateData('exp_des', value),
          ),
          const SizedBox(height: 16),

          // اسم الشركة
          FieldTxt(
            fieldLabel: Lang().getWord("exp_company_name", widget.cvData.ltf),
            handlerInput: (value) => _updateData('exp_company_name', value),
            fieldname: "exp_company_name",
            hint: Lang().getWord("exp_company_name_hint", widget.cvData.ltf),
            iconField: const Icon(Icons.business),
            inputRequired: true,
            textEditingController: _expCompanyNameController,
            ltf: widget.cvData.ltf,
          ),
          const SizedBox(height: 24),

          // أزرار الإضافة فقط (بدون زر التالي)
          Row(
            children: [
              // زر الإضافة
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    _addExperience();
                    // إضافة رسالة تأكيد عند الإضافة بنجاح
                    if (_expFrom != null && _expEnd != null &&
                        _expTitleController.text.isNotEmpty &&
                        _expDesController.text.isNotEmpty &&
                        _expCompanyNameController.text.isNotEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(_editingIndex != null
                              ? Lang().getWord("experience_updated", widget.cvData.ltf)
                              : Lang().getWord("experience_added", widget.cvData.ltf)),
                          backgroundColor: Colors.green,
                          behavior: SnackBarBehavior.floating,
                          margin: const EdgeInsets.all(16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      );
                    }
                  },
                  icon: Icon(_editingIndex != null ? Icons.save : Icons.add),
                  label: Text(
                    _editingIndex != null
                        ? Lang().getWord("save_changes", widget.cvData.ltf)
                        : Lang().getWord("add_new_exp", widget.cvData.ltf),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // إذا تم العودة من الخطوة التالية، احذف آخر خبرة من المصفوفة وأعد تعبئة الحقول
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map && args['action'] == 'back_step' && widget.experienceList.isNotEmpty) {
      final lastExp = widget.experienceList.last;
      widget.onExperienceListChanged(List<Map<String, dynamic>>.from(widget.experienceList)..removeLast());
      setState(() {
        _expFrom = lastExp["exp_from"];
        _expEnd = lastExp["exp_end"];
        _expTitleController.text = lastExp["exp_title"];
        _expDesController.text = lastExp["exp_des"];
        _expCompanyNameController.text = lastExp["exp_company_name"];
        _editingIndex = null;
      });
    }
  }
}
