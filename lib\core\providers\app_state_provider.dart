import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/theme_provider.dart';
import 'package:wzzff/models/app_state_model.dart';
import 'package:wzzff/services/app_state_service.dart';

/// مزود لإدارة حالة التطبيق
class AppStateProvider extends ChangeNotifier {
  final AppStateService _appStateService = AppStateService();

  AppStateModel? _appState;
  bool _isLoading = false;
  String? _error;
  bool _hasOptionalUpdate = false;
  bool _needsForceUpdate = false;
  bool _showServerError = false;
  String _serverErrorMessage = '';
  int _retrySeconds = 5;
  VoidCallback? _retryCallback;

  /// الحصول على حالة التطبيق
  AppStateModel? get appState => _appState;

  /// التحقق مما إذا كانت البيانات قيد التحميل
  bool get isLoading => _isLoading;

  /// الحصول على رسالة الخطأ
  String? get error => _error;

  /// التحقق مما إذا كان هناك تحديث اختياري متاح
  bool get hasOptionalUpdate => _hasOptionalUpdate;

  /// التحقق مما إذا كان التطبيق يحتاج إلى تحديث إجباري
  bool get needsForceUpdate => _needsForceUpdate;

  bool get showServerError => _showServerError;
  String get serverErrorMessage => _serverErrorMessage;
  int get retrySeconds => _retrySeconds;

  /// تحميل بيانات حالة التطبيق
  Future<void> loadAppState({bool forceRefresh = false}) async {
    debugPrint('AppStateProvider: بدء تحميل حالة التطبيق...');
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // الحصول على بيانات حالة التطبيق
      debugPrint('AppStateProvider: استدعاء AppStateService...');
      _appState = await _appStateService.getAppState(forceRefresh: forceRefresh);
      
      debugPrint('AppStateProvider: تم الحصول على البيانات من AppStateService');
      debugPrint('AppStateProvider: announcement.enabled = ${_appState!.announcement.enabled}');
      debugPrint('AppStateProvider: announcement.title = ${_appState!.announcement.title}');

      // التحقق من حالة التحديث
      _needsForceUpdate = await _appState!.needsForceUpdate();
      _hasOptionalUpdate = !_needsForceUpdate && await _appState!.hasOptionalUpdate();

      debugPrint('AppStateProvider: needsForceUpdate = $_needsForceUpdate');
      debugPrint('AppStateProvider: hasOptionalUpdate = $_hasOptionalUpdate');

      _isLoading = false;
      debugPrint('AppStateProvider: تم تحميل حالة التطبيق بنجاح');
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'حدث خطأ أثناء تحميل بيانات حالة التطبيق: $e';
      debugPrint('AppStateProvider: خطأ - $_error');
      notifyListeners();
    }
  }

  /// تطبيق وضع السمة (الوضع الليلي أو النهاري)
  void applyThemeMode(BuildContext context) {
    if (_appState != null) {
      final isDarkMode = _appState!.darkMode;
      final currentTheme = Theme.of(context).brightness;
      final shouldBeDark = isDarkMode ? Brightness.dark : Brightness.light;

      // تطبيق وضع السمة على التطبيق
      if (currentTheme != shouldBeDark) {
        debugPrint('تطبيق وضع السمة: ${isDarkMode ? "الوضع الليلي" : "الوضع النهاري"}');

        // الحصول على مزود السمة وتطبيق الوضع الليلي أو النهاري
        final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
        themeProvider.setDarkMode(isDarkMode);
      }
    }
  }

  /// التحقق من حالة التسجيل
  bool isRegistrationEnabled() {
    return _appState?.authControl.registrationEnabled ?? true;
  }

  /// التحقق من حالة تسجيل الدخول
  bool isLoginEnabled() {
    return _appState?.authControl.loginEnabled ?? true;
  }

  /// التحقق من وضع الصيانة
  bool isInMaintenanceMode() {
    return _appState?.maintenanceMode ?? false;
  }

  /// التحقق من وجود إعلان
  bool hasAnnouncement() {
    return _appState?.announcement.enabled ?? false;
  }

  /// الحصول على بيانات الإعلان
  AppAnnouncement? getAnnouncement() {
    return _appState?.announcement;
  }

  /// الحصول على رابط التحديث
  String getUpdateUrl() {
    return _appState?.getUpdateUrl() ?? '';
  }

  /// تعيين خطأ السيرفر مع دالة إعادة المحاولة
  void setServerError(String message, VoidCallback retryCallback, {int retrySeconds = 5}) {
    _showServerError = true;
    _serverErrorMessage = message;
    _retrySeconds = retrySeconds;
    _retryCallback = retryCallback;
    notifyListeners();
    _startCountdown();
  }

  void clearServerError() {
    _showServerError = false;
    _serverErrorMessage = '';
    _retryCallback = null;
    _retrySeconds = 5;
    notifyListeners();
  }

  void _startCountdown() async {
    while (_retrySeconds > 0 && _showServerError) {
      await Future.delayed(const Duration(seconds: 1));
      if (!_showServerError) return;
      _retrySeconds--;
      notifyListeners();
    }
    if (_showServerError && _retryCallback != null) {
      _retryCallback!();
    }
  }
}
