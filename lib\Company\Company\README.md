# Company Pages Documentation

## Overview
This directory contains the pages related to company management within the application. Each page is designed to provide a seamless experience for users managing their company profiles, job listings, and applications. The design accommodates both light and dark themes to ensure usability in various lighting conditions.

## Pages

### 1. Register Company Page
- **File:** `register_company_page.dart`
- **Description:** This page provides a form for users to register a new company. It includes fields for:
  - Company Name
  - Email
  - Password
  - Other relevant details
- **Design:** The layout is visually appealing and responsive, ensuring a good user experience in both light and dark themes.

### 2. Create Job Page
- **File:** `create_job_page.dart`
- **Description:** This page allows users to create a new job listing. It features input fields for:
  - Job Title
  - Job Description
  - Requirements
  - Other job-related information
- **Design:** The page is designed to be user-friendly and visually appealing, accommodating both light and dark themes.

### 3. Company Data Page
- **File:** `company_data_page.dart`
- **Description:** This page displays the company's information, including:
  - Company Name
  - Address
  - Contact Details
  - Other relevant data
- **Design:** The layout is intuitive and visually appealing in both light and dark modes, ensuring easy access to company information.

### 4. Edit Company Data Page
- **File:** `edit_company_data_page.dart`
- **Description:** This page provides a form for editing existing company information. Users can update their details, including:
  - Company Name
  - Address
  - Contact Information
- **Design:** The design is consistent with the overall theme of the application, ensuring a cohesive user experience.

### 5. Job Applications Page
- **File:** `job_applications_page.dart`
- **Description:** This page displays a list of job applications submitted for the jobs created by the company. Users can:
  - View application details
  - Change the status of each application (e.g., reviewed, accepted, rejected)
- **Design:** The page is visually appealing and designed for ease of use in both themes.

## Usage
To use these pages, ensure that the necessary routes are set up in your application. Each page can be navigated to from the main application interface, allowing users to manage their company effectively.

## Conclusion
These company management pages are designed to provide a comprehensive and user-friendly experience for managing company profiles and job listings. The attention to detail in design ensures that users can navigate and utilize the features effectively, regardless of the theme they prefer.