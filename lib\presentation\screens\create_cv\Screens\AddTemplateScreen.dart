import 'package:flutter/material.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;

class AddTemplateScreen extends StatefulWidget {
  const AddTemplateScreen({Key? key}) : super(key: key);

  @override
  State<AddTemplateScreen> createState() => _AddTemplateScreenState();
}

class _AddTemplateScreenState extends State<AddTemplateScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descController = TextEditingController();
  File? _imageFile;
  bool _isLoading = false;

  Future<void> _pickImage() async {
    final pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _imageFile = File(pickedFile.path);
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate() || _imageFile == null) {
      if (_imageFile == null) {
        final ltrOrNot = Directionality.of(context) == TextDirection.ltr ? "1" : "0";
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(Lang().getWord("template_image", ltrOrNot) + ' ' + Lang().getWord("fill_all_fields", ltrOrNot))),
        );
      }
      return;
    }
    setState(() { _isLoading = true; });
    try {
      // مثال endpoint، غيّره حسب مشروعك
      var uri = Uri.parse('https://your-api.com/templates');
      var request = http.MultipartRequest('POST', uri)
        ..fields['name'] = _nameController.text
        ..fields['description'] = _descController.text
        ..files.add(await http.MultipartFile.fromPath('image', _imageFile!.path));
      var response = await request.send();
      setState(() { _isLoading = false; });
      if (response.statusCode == 200 || response.statusCode == 201) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حفظ القالب بنجاح')),
        );
        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء الحفظ، حاول مرة أخرى')),
        );
      }
    } catch (e) {
      setState(() { _isLoading = false; });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ في الاتصال')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final ltrOrNot = Directionality.of(context) == TextDirection.ltr ? "1" : "0";
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(Lang().getWord("add_new_template", ltrOrNot)),
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(Icons.arrow_forward),
              onPressed: () => Navigator.pop(context),
              tooltip: 'رجوع',
            ),
          ],
        ),
        body: Center(
          child: SingleChildScrollView(
            child: Card(
              elevation: 6,
              color: isDarkMode ? const Color(0xFF232323) : Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
              margin: const EdgeInsets.all(18),
              child: Padding(
                padding: const EdgeInsets.all(22.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Stack(
                        alignment: Alignment.bottomRight,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(18),
                            child: Container(
                              width: double.infinity,
                              height: 170,
                              color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                              child: _imageFile != null
                                  ? Image.file(_imageFile!, fit: BoxFit.cover, width: double.infinity, height: 170)
                                  : Icon(Icons.image, size: 60, color: Colors.grey[400]),
                            ),
                          ),
                          Positioned(
                            bottom: 10,
                            right: 10,
                            child: Material(
                              color: primaryColor,
                              shape: const CircleBorder(),
                              child: InkWell(
                                onTap: _isLoading ? null : _pickImage,
                                borderRadius: BorderRadius.circular(22),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Icon(Icons.camera_alt, color: Colors.white, size: 22),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      TextFormField(
                        controller: _nameController,
                        textDirection: TextDirection.rtl,
                        enabled: !_isLoading,
                        decoration: InputDecoration(
                          labelText: Lang().getWord("template_name", ltrOrNot),
                          hintText: Lang().getWord("template_name_hint", ltrOrNot),
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return Lang().getWord("fill_all_fields", ltrOrNot);
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 18),
                      TextFormField(
                        controller: _descController,
                        textDirection: TextDirection.rtl,
                        enabled: !_isLoading,
                        maxLines: 3,
                        decoration: InputDecoration(
                          labelText: Lang().getWord("template_desc", ltrOrNot),
                          hintText: Lang().getWord("template_desc_hint", ltrOrNot),
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return Lang().getWord("fill_all_fields", ltrOrNot);
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          icon: _isLoading
                              ? SizedBox(
                                  width: 22,
                                  height: 22,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2.5,
                                  ),
                                )
                              : const Icon(Icons.save),
                          label: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              Lang().getWord("save_template", ltrOrNot),
                              style: const TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: primaryColor,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                          onPressed: _isLoading ? null : _submitForm,
                        ),
                      ),
                      const SizedBox(height: 10),
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton(
                          onPressed: _isLoading ? null : () => Navigator.pop(context),
                          style: OutlinedButton.styleFrom(
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              Lang().getWord("cancel", ltrOrNot),
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 