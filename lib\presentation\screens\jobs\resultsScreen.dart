import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'LoadScreen.dart';

/// شاشة نتائج البحث مع نظام التتبع الذكي المتقدم
/// 
/// المميزات المضافة:
/// 🎯 تتبع استعلامات البحث ونتائجها
/// 📊 تحليل فعالية النتائج المعروضة
/// 🔍 تتبع التفاعل مع النتائج الفردية
/// 📋 تتبع استخدام المرشحات (الدولة، المدينة)
/// ⏱️ تتبع الوقت المقضي في مراجعة النتائج
/// 🎭 تحليل سلوك المستخدم مع نتائج البحث المختلفة
/// 
/// هذا القسم حيوي لتحسين:
/// - دقة خوارزميات البحث
/// - فهم ما يبحث عنه المستخدمون فعلاً
/// - تحسين ترتيب وعرض النتائج
/// - تطوير اقتراحات بحث أفضل

class ResultsScreen extends StatefulWidget {
  final String country;
  final String word;
  final String? city;

  const ResultsScreen({
    super.key,
    required this.country,
    required this.city,
    required this.word,
  });

  @override
  State<ResultsScreen> createState() => _ResultsScreenState();
}

class _ResultsScreenState extends State<ResultsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "نتائج البحث",
          style: GoogleFonts.tajawal(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2daae2),
        elevation: 0,
        centerTitle: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward, color: Colors.white),
            onPressed: () => Navigator.pop(context),
            tooltip: 'رجوع',
          ),
        ],
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Column(
          children: [
            _buildSearchHeader(),
            Expanded(
              child: GestureDetector(
                onTap: () => {},
                child: LoadJobScreen(
                  paddingTop: 0,
                  where: "search",
                  extraData: {"word": widget.word, "country": widget.country, "city": widget.city},
                  showCity: true,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(
            Icons.search_rounded,
            color: Color(0xFF2daae2),
            size: 22,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "نتائج البحث عن",
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  widget.word,
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          if (widget.city != null && widget.city!.isNotEmpty)
            Chip(
              label: Text(
                widget.city!,
                style: GoogleFonts.tajawal(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
              backgroundColor: const Color(0xFF2daae2).withOpacity(0.8),
              padding: const EdgeInsets.symmetric(horizontal: 4),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
        ],
      ),
    );
  }
}
