import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
//import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wzzff/core/constants/Constants.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/presentation/screens/CountriesScreen.dart';
import 'package:wzzff/presentation/screens/articles/NewsPage.dart';
import 'package:wzzff/presentation/screens/auth/LoginOrRegisterScreen.dart';
import 'package:wzzff/presentation/screens/MessagesScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/SavedJobs.dart';
import 'package:wzzff/presentation/widgets/announcement_banner.dart';
import 'package:wzzff/presentation/widgets/custom_theme.dart';
import 'package:wzzff/presentation/screens/home_screen.dart';
import 'package:wzzff/presentation/screens/jobs_news/jobs_news.dart';

import 'jobs/detail_job_screen.dart';

class Home extends StatefulWidget {
  final String title;
  final int initialTabIndex;

  const Home({super.key, required this.title, this.initialTabIndex = 0});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> with SingleTickerProviderStateMixin {
  late int _selectedIndex;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    //MobileAds.instance.initialize();
    _selectedIndex = widget.initialTabIndex;
    _tabController = TabController(length: 6, vsync: this);

    // عرض الإعلان بعد بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showAnnouncementIfAvailable();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // عرض الإعلان إذا كان متاحاً
  void _showAnnouncementIfAvailable() {
    if (!mounted) return;

    final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
    if (appStateProvider.hasAnnouncement()) {
      final announcement = appStateProvider.getAnnouncement();
      if (announcement != null && announcement.enabled) {
        // عرض الإعلان في شريط منبثق
        ScaffoldMessenger.of(context).showMaterialBanner(
          MaterialBanner(
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  announcement.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(announcement.body),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentMaterialBanner();
                },
                child: const Text('إغلاق'),
              ),
              if (announcement.link.isNotEmpty)
                TextButton(
                  onPressed: () async {
                    final Uri uri = Uri.parse(announcement.link);
                    if (await canLaunchUrl(uri)) {
                      await launchUrl(uri, mode: LaunchMode.externalApplication);
                    }
                  },
                  child: const Text('عرض'),
                ),
            ],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = <Widget>[
      HomeScreen(tabController: _tabController),
      const CountriesScreen(),
      const NewsPage(),
      const LoginOrRegisterScreen(),
    ];
    return Scaffold(
      body: Directionality(
        textDirection: Constants().getOurDir(),
        child: Scaffold(
          body: pages[_selectedIndex],
        ),
      ),
    );
  }
}


