<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.wzzff.app">
    <!-- أذونات الإنترنت والملفات -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- أذونات الإشعارات -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
   <application
        android:name="androidx.multidex.MultiDexApplication"
        android:label="وظف دوت كوم"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <!-- إعدادات الروابط العميقة للويب (App Links) -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- الروابط العميقة للوظائف -->
                <data
                    android:scheme="https"
                    android:host="wzzff.com"
                    android:pathPrefix="/job/" />

                <!-- الروابط العميقة للمقالات -->
                <data
                    android:scheme="https"
                    android:host="wzzff.com"
                    android:pathPrefix="/blog/show/" />

                <!-- الروابط العميقة لأخبار الوظائف -->
                <data
                    android:scheme="https"
                    android:host="wzzff.com"
                    android:pathPrefix="/jobs_news/" />
            </intent-filter>

            <!-- دعم الروابط العميقة بصيغة wzzff:// (Custom URL Scheme) -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- مخطط URL المخصص للوظائف -->
                <data
                    android:scheme="wzzff"
                    android:host="job"
                    android:pathPattern=".*" />

                <!-- مخطط URL المخصص للمقالات -->
                <data
                    android:scheme="wzzff"
                    android:host="blog"
                    android:pathPattern=".*" />

                <!-- مخطط URL المخصص لأخبار الوظائف -->
                <data
                    android:scheme="wzzff"
                    android:host="jobs_news"
                    android:pathPattern=".*" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~3347511713"/>

        <!-- إعدادات Firebase Messaging -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="high_importance_channel" />

        <!-- أيقونة الإشعارات الافتراضية -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />

        <!-- لون الإشعارات الافتراضي -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification_color" />

        <!-- معالج استقبال الإشعارات عند إعادة تشغيل الجهاز -->
        <receiver
            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>
    </application>
</manifest>

