import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/SeekerModel.dart';
import 'common_widgets.dart';

class PreferencesStep extends StatefulWidget {
  final SeekerModel user;
  final Function(Map<String, dynamic>) onDataChanged;
  final VoidCallback onSubmit;

  const PreferencesStep({
    super.key,
    required this.user,
    required this.onDataChanged,
    required this.onSubmit,
  });

  @override
  State<PreferencesStep> createState() => _PreferencesStepState();
}

class _PreferencesStepState extends State<PreferencesStep> {
  final _jobTitleController = TextEditingController();
  final _salaryController = TextEditingController();
  final _currentSalaryController = TextEditingController();
  String? selectedValueJobType;
  String? selectedValueWorkLocation;
  bool isRemoteWork = false;

  List<String> jobTypes = [
    'دوام كامل',
    'دوام جزئي',
    'عمل حر',
    'تدريب',
    'تطوع'
  ];

  List<String> workLocations = [
    'في المكتب',
    'عن بعد',
    'مختلط'
  ];

  @override
  void initState() {
    super.initState();
    _fillUserData();
  }

  @override
  void dispose() {
    _jobTitleController.dispose();
    _salaryController.dispose();
    _currentSalaryController.dispose();
    super.dispose();
  }

  void _fillUserData() {
    if (_jobTitleController.text.isEmpty) {
      _jobTitleController.text = widget.user.job_title ?? '';
    }
    if (_salaryController.text.isEmpty) {
      _salaryController.text = widget.user.expected_salary?.toString() ?? '';
    }
    if (_currentSalaryController.text.isEmpty) {
      _currentSalaryController.text = widget.user.current_salary?.toString() ?? '';
    }
    if (selectedValueJobType == null) {
      selectedValueJobType = widget.user.job_type;
    }
    if (selectedValueWorkLocation == null) {
      selectedValueWorkLocation = widget.user.work_location;
    }
    isRemoteWork = widget.user.is_remote_work ?? false;
    _updateData();
  }

  void _updateData() {
    int? salary;
    if (_salaryController.text.isNotEmpty) {
      salary = int.tryParse(_salaryController.text);
    }
    int? currentSalary;
    if (_currentSalaryController.text.isNotEmpty) {
      currentSalary = int.tryParse(_currentSalaryController.text);
    }
    
    widget.onDataChanged({
      "job_title": _jobTitleController.text,
      "expected_salary": salary,
      "current_salary": currentSalary,
      "job_type": selectedValueJobType,
      "work_location": selectedValueWorkLocation,
      "is_remote_work": isRemoteWork,
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSectionTitle("تفضيلات العمل", Icons.work),
          const SizedBox(height: 24),
          
          buildInputField(
            controller: _jobTitleController,
            label: "المسمى الوظيفي المطلوب",
            icon: Icons.badge_outlined,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء إدخال المسمى الوظيفي";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),
          
          buildDropdownField(
            label: "نوع الوظيفة",
            value: selectedValueJobType,
            items: jobTypes,
            onChanged: (value) {
              setState(() {
                selectedValueJobType = value;
                _updateData();
              });
            },
            icon: Icons.work_outline,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء اختيار نوع الوظيفة";
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          buildInputField(
            controller: _salaryController,
            label: "الراتب المتوقع",
            icon: Icons.attach_money,
            keyboardType: TextInputType.number,
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),
          
          buildInputField(
            controller: _currentSalaryController,
            label: "الراتب الحالي (اختياري)",
            icon: Icons.money,
            keyboardType: TextInputType.number,
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),
          
          buildDropdownField(
            label: "مكان العمل",
            value: selectedValueWorkLocation,
            items: workLocations,
            onChanged: (value) {
              setState(() {
                selectedValueWorkLocation = value;
                _updateData();
              });
            },
            icon: Icons.location_on_outlined,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء اختيار مكان العمل";
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          buildSwitchField(
            label: "مستعد للعمل عن بعد",
            value: isRemoteWork,
            onChanged: (value) {
              setState(() {
                isRemoteWork = value;
                _updateData();
              });
            },
            icon: Icons.laptop_outlined,
          ),
          
          const SizedBox(height: 32),
          
        
        ],
      ),
    );
  }
}