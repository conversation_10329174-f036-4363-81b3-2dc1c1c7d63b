import 'package:flutter/material.dart';

// colors
const Color grey = Color(0xff7F879E);
const Color lightGrey = Color(0xfff9f9f9);
const Color black = Color(0xff1B2124);
const Color white = Color(0xffFFFFFF);
const Color backgroundWhite = Color(0xffF2F2FF);
const Color blue = Color(0xff3860E2);
const Color cyan = Color(0xff67C1F4);
const Color orange = Color(0xffF59A74);

const String pinterestDescription =
    'Senior UI/UX Designer needed, for collaborate with team and developer as full time designer. by having good communication skills,';
const String discordDescription =
    'Junior UI Designer needed, for redesign many page in discord web, desktop and mobile app so the app can work good';
const String discordDescription2 =
    'Tech Lead needed, for managing web, desktop and mobile app';

const List<String> responsibilities = [
  'Collaborate with product manager and teach throughout the design life-cycle such as product wireframes',
  'Design new product, new interfaces and experience.',
  'Create a design theme that promotes a strong brand affiliation.',
  'Hands-on experience with creating short videos and editing',
];

const List<String> requirements = [
  'On-site in California',
  'Have good communication skills and team working skill.',
  'Know the principal of animation and you can create high quality prototypes.',
  'Fi<PERSON>, Xd & Sketch know about this app.',
];
