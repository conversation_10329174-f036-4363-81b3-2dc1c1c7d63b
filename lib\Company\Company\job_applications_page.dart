import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/core/theme/app_colors.dart';
import 'package:wzzff/Company/Company/job_applicants_page.dart';
import 'package:wzzff/Apis/CompanyApi.dart';

class JobApplicationsPage extends StatefulWidget {
  const JobApplicationsPage({Key? key}) : super(key: key);

  @override
  State<JobApplicationsPage> createState() => _JobApplicationsPageState();
}

class _JobApplicationsPageState extends State<JobApplicationsPage> with TickerProviderStateMixin {
  bool _isLoading = true;
  String? _error;
  List<Map<String, dynamic>> _jobs = [];
  String _selectedFilter = 'الكل';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<String> _filterOptions = ['الكل', 'نشطة', 'مغلقة', 'منتهية'];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _fetchJobs();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
  }

  Future<void> _fetchJobs() async {
    setState(() { 
      _isLoading = true; 
      _error = null; 
    });
    
    try {
      final response = await CompanyApi().getCompanyJobs();
      
      if (response['success'] == true && response['data'] != null) {
        setState(() {
          _jobs = _parseJobsData(response['data']);
          _isLoading = false;
        });
        _animationController.forward();
      } else {
        setState(() {
          _error = response['message'] ?? 'فشل في جلب الوظائف';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'تعذر الاتصال بالخادم، يرجى المحاولة لاحقاً';
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _parseJobsData(dynamic data) {
    List<Map<String, dynamic>> parsedJobs = [];
    
    try {
      if (data is List) {
        for (var job in data) {
          parsedJobs.add({
            'id': job['id'] ?? 0,
            'title': job['title'] ?? 'وظيفة غير محددة',
            'status': _mapJobStatus(job['status']),
            'applicants': job['applications_count'] ?? 0,
            'date': job['created_at'] ?? '',
            'desc': job['description'] ?? 'لا يوجد وصف',
            'salary': _formatSalary(job['salary_from'], job['salary_to'], job['currency']),
            'jobType': job['job_type'] ?? 'غير محدد',
            'department': job['category'] ?? 'غير محدد',
            'location': '${job['city'] ?? ''}',
            'daysRemaining': _calculateDaysRemaining(job['deadline']),
            'viewsCount': job['views_count'] ?? 0,
            'deadline': job['deadline'],
            'created_at': job['created_at'],
            'updated_at': job['updated_at'],
          });
        }
      }
    } catch (e) {
      print('خطأ في تحليل البيانات: $e');
    }
    
    return parsedJobs;
  }

  String _mapJobStatus(dynamic status) {
    if (status == null) return 'غير محدد';
    
    switch (status.toString().toLowerCase()) {
      case 'active':
      case '1':
      case 'open':
        return 'نشطة';
      case 'closed':
      case '0':
      case 'paused':
        return 'مغلقة';
      case 'expired':
      case 'finished':
        return 'منتهية';
      default:
        return 'غير محدد';
    }
  }

  String _formatSalary(dynamic salaryMin, dynamic salaryMax, dynamic currency) {
    try {
      if (salaryMin != null && salaryMax != null) {
        return '$salaryMin - $salaryMax ${currency ?? 'ريال'}';
      } else if (salaryMin != null) {
        return 'من $salaryMin ${currency ?? 'ريال'}';
      } else if (salaryMax != null) {
        return 'حتى $salaryMax ${currency ?? 'ريال'}';
      } else {
        return 'غير محدد';
      }
    } catch (e) {
      return 'غير محدد';
    }
  }

  int _calculateDaysRemaining(dynamic deadline) {
    try {
      if (deadline == null) return 0;
      
      DateTime deadlineDate = DateTime.parse(deadline.toString());
      DateTime now = DateTime.now();
      
      int difference = deadlineDate.difference(now).inDays;
      return difference > 0 ? difference : 0;
    } catch (e) {
      return 0;
    }
  }

  Future<void> _refreshJobs() async {
    await _fetchJobs();
  }

  List<Map<String, dynamic>> get _filteredJobs {
    if (_selectedFilter == 'الكل') return _jobs;
    return _jobs.where((job) => job['status'] == _selectedFilter).toList();
  }

  Color _statusColor(String status) {
    switch (status) {
      case 'نشطة':
        return Colors.green;
      case 'مغلقة':
        return Colors.orange;
      case 'منتهية':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _statusIcon(String status) {
    switch (status) {
      case 'نشطة':
        return Icons.play_circle_fill;
      case 'مغلقة':
        return Icons.pause_circle_filled;
      case 'منتهية':
        return Icons.stop_circle;
      default:
        return Icons.help;
    }
  }

  Widget _buildStatsCard() {
    final totalJobs = _jobs.length;
    final activeJobs = _jobs.where((job) => job['status'] == 'نشطة').length;
    final totalApplicants = _jobs.fold<int>(0, (sum, job) => sum + (job['applicants'] as int));
    final totalViews = _jobs.fold<int>(0, (sum, job) => sum + (job['viewsCount'] as int));

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              'إجمالي الوظائف',
              totalJobs.toString(),
              Icons.work,
              Colors.white,
            ),
          ),
          Container(
            width: 1,
            height: 50,
            color: Colors.white.withOpacity(0.3),
          ),
          Expanded(
            child: _buildStatItem(
              'الوظائف النشطة',
              activeJobs.toString(),
              Icons.trending_up,
              Colors.white,
            ),
          ),
          Container(
            width: 1,
            height: 50,
            color: Colors.white.withOpacity(0.3),
          ),
          Expanded(
            child: _buildStatItem(
              'إجمالي المتقدمين',
              totalApplicants.toString(),
              Icons.people,
              Colors.white,
            ),
          ),
          Container(
            width: 1,
            height: 50,
            color: Colors.white.withOpacity(0.3),
          ),
          Expanded(
            child: _buildStatItem(
              'المشاهدات',
              totalViews.toString(),
              Icons.visibility,
              Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: color.withOpacity(0.9),
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filterOptions.length,
        itemBuilder: (context, index) {
          final filter = _filterOptions[index];
          final isSelected = _selectedFilter == filter;
          
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              checkmarkColor: Theme.of(context).colorScheme.primary,
              labelStyle: TextStyle(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary 
                    : AppColors.getSecondaryTextColor(context),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary 
                    : Colors.grey.withOpacity(0.3),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildJobCard(Map<String, dynamic> job, int index) {
    final cardColor = AppColors.getCardColor(context);
    final textColor = AppColors.getTextColor(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: isDarkMode 
                    ? Colors.black.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.15),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => JobApplicantsPage(
                        jobId: job['id'],
                        jobTitle: job['title'],
                        jobData: job,
                      ),
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  job['title'],
                                  style: TextStyle(
                                    color: textColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  job['department'],
                                  style: TextStyle(
                                    color: AppColors.getSecondaryTextColor(context),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: _statusColor(job['status']).withOpacity(0.15),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _statusIcon(job['status']),
                                  color: _statusColor(job['status']),
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  job['status'],
                                  style: TextStyle(
                                    color: _statusColor(job['status']),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Description
                      Text(
                        job['desc'],
                        style: TextStyle(
                          color: AppColors.getSecondaryTextColor(context),
                          fontSize: 14,
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Job details row
                      Row(
                        children: [
                          _buildDetailChip(
                            Icons.location_on,
                            job['location'],
                            Colors.blue,
                          ),
                          const SizedBox(width: 8),
                          _buildDetailChip(
                            Icons.access_time,
                            job['jobType'],
                            Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          _buildDetailChip(
                            Icons.monetization_on,
                            job['salary'],
                            Colors.green,
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Stats row
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.people,
                                    color: Theme.of(context).colorScheme.primary,
                                    size: 18,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '${job['applicants']} متقدم',
                                      style: TextStyle(
                                        color: textColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    Text(
                                      '${job['viewsCount']} مشاهدة',
                                      style: TextStyle(
                                        color: AppColors.getSecondaryTextColor(context),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              if (job['status'] == 'نشطة' && job['daysRemaining'] > 0)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: job['daysRemaining'] <= 5 
                                        ? Colors.red.withOpacity(0.1)
                                        : Colors.green.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    'باقي ${job['daysRemaining']} يوم',
                                    style: TextStyle(
                                      color: job['daysRemaining'] <= 5 ? Colors.red : Colors.green,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              const SizedBox(height: 4),
                              Text(
                                job['date'],
                                style: TextStyle(
                                  color: AppColors.getSecondaryTextColor(context),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Action button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.visibility, size: 20),
                          label: const Text('عرض المتقدمين'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => JobApplicantsPage(
                                  jobId: job['id'],
                                  jobTitle: job['title'],
                                  jobData: job,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = AppColors.getTextColor(context);
    
    return Scaffold(
      backgroundColor: isDarkMode 
          ? Theme.of(context).scaffoldBackgroundColor
          : const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Text(
          'الوظائف المنشورة',
          style: GoogleFonts.tajawal(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Icon(
              Icons.arrow_forward,
              color: Colors.white,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      body: _isLoading
          ? Container(
              color: isDarkMode 
                  ? Theme.of(context).scaffoldBackgroundColor
                  : const Color(0xFFF8FAFC),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تحميل الوظائف...',
                      style: GoogleFonts.tajawal(
                        color: textColor,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            )
          : _error != null
              ? Container(
                  padding: const EdgeInsets.all(20),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red.withOpacity(0.7),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'حدث خطأ',
                          style: GoogleFonts.tajawal(
                            color: Colors.red,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _error!,
                          style: GoogleFonts.tajawal(
                            color: textColor,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _refreshJobs,
                          icon: const Icon(Icons.refresh),
                          label: Text(
                            'إعادة المحاولة',
                            style: GoogleFonts.tajawal(fontSize: 14),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : _jobs.isEmpty
                  ? Container(
                      padding: const EdgeInsets.all(20),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.work_off,
                              size: 64,
                              color: textColor.withOpacity(0.5),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد وظائف منشورة',
                              style: GoogleFonts.tajawal(
                                color: textColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'ابدأ بإنشاء أول وظيفة لشركتك',
                              style: GoogleFonts.tajawal(
                                color: AppColors.getSecondaryTextColor(context),
                                fontSize: 14,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton.icon(
                              onPressed: () {
                                // Navigate to create job page
                              },
                              icon: const Icon(Icons.add),
                              label: Text(
                                'إنشاء وظيفة جديدة',
                                style: GoogleFonts.tajawal(fontSize: 14),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _refreshJobs,
                      color: Theme.of(context).colorScheme.primary,
                      child: CustomScrollView(
                        slivers: [
                          SliverToBoxAdapter(child: _buildStatsCard()),
                          SliverToBoxAdapter(child: const SizedBox(height: 16)),
                          SliverToBoxAdapter(child: _buildFilterChips()),
                          SliverToBoxAdapter(child: const SizedBox(height: 16)),
                          SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final filteredJobs = _filteredJobs;
                                if (index >= filteredJobs.length) return null;
                                return _buildJobCard(filteredJobs[index], index);
                              },
                              childCount: _filteredJobs.length,
                            ),
                          ),
                          const SliverToBoxAdapter(child: SizedBox(height: 20)),
                        ],
                      ),
                    ),
    );
  }
}