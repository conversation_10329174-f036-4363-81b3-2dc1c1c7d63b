import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wzzff/services/google_ad_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/core/utils/app_messages.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class ReportJobScreen extends StatefulWidget {
  final String jobId;

  const ReportJobScreen({super.key, required this.jobId});

  @override
  State<ReportJobScreen> createState() => _ReportJobScreenState();
}

class _ReportJobScreenState extends State<ReportJobScreen> {
  // خدمة الإعلانات المركزية
  final GoogleAdService _adService = GoogleAdService();
  
  final _reportController = TextEditingController();
  final _jobTitleController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  String? _selectedReason;
  final List<String> _reportReasons = [
    'محتوى غير لائق',
    'وظيفة وهمية',
    'معلومات خاطئة',
    'انتهاك حقوق الطبع والنشر',
    'إعلان مخادع',
    'أخرى',
  ];

  bool _isSubmitting = false;

  @override
  void dispose() {
    _reportController.dispose();
    _jobTitleController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _submitReport() async {
    if (_selectedReason == null || 
        _reportController.text.trim().isEmpty ||
        _jobTitleController.text.trim().isEmpty ||
        _emailController.text.trim().isEmpty ||
        _phoneController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // الحصول على FCM Token من Firebase مباشرة
      String? fcmToken = await FirebaseMessaging.instance.getToken();
      
      final jobsApi = JobsApi();
      final success = await jobsApi.reportJobProblem(
        jobTitle: _jobTitleController.text.trim(),
        jobSlug: widget.jobId.toString(),
        problem: '$_selectedReason: ${_reportController.text.trim()}',
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        fcmToken: fcmToken ?? '',
      );

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال البلاغ بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إرسال البلاغ'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Scaffold(
      backgroundColor: isDarkMode ? Theme.of(context).scaffoldBackgroundColor : Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'الإبلاغ عن الوظيفة',
          style: GoogleFonts.tajawal(fontWeight: FontWeight.bold),
        ),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إعلان البانر
            Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.only(bottom: 16),
             // child: _adService.createBannerAdWidget(),
            ),

            // باقي محتوى الصفحة
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'عنوان الوظيفة',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _jobTitleController,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintText: 'أدخل عنوان الوظيفة',
                        hintStyle: GoogleFonts.tajawal(),
                      ),
                      style: GoogleFonts.tajawal(),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'البريد الإلكتروني',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintText: 'أدخل بريدك الإلكتروني',
                        hintStyle: GoogleFonts.tajawal(),
                      ),
                      style: GoogleFonts.tajawal(),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'رقم الهاتف',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintText: 'أدخل رقم هاتفك',
                        hintStyle: GoogleFonts.tajawal(),
                      ),
                      style: GoogleFonts.tajawal(),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'سبب الإبلاغ',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    DropdownButtonFormField<String>(
                      value: _selectedReason,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintText: 'اختر سبب الإبلاغ',
                        hintStyle: GoogleFonts.tajawal(),
                      ),
                      style: GoogleFonts.tajawal(
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                      items: _reportReasons.map((reason) {
                        return DropdownMenuItem(
                          value: reason,
                          child: Text(reason),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedReason = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'تفاصيل البلاغ',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _reportController,
                      maxLines: 5,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        hintText: 'اكتب تفاصيل البلاغ هنا...',
                        hintStyle: GoogleFonts.tajawal(),
                      ),
                      style: GoogleFonts.tajawal(),
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSubmitting ? null : _submitReport,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: _isSubmitting
                            ? const CircularProgressIndicator(
                                color: Colors.white,
                              )
                            : Text(
                                'إرسال البلاغ',
                                style: GoogleFonts.tajawal(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
