import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wzzff/services/daily_notification_service.dart';
//import 'package:wzzff/services/comprehensive_user_analyzer.dart';
import 'package:wzzff/models/JobModel.dart';

/// 🧠 مدير الإشعارات الذكية الشامل
/// يدير جميع أنواع الإشعارات الذكية ويحدد متى وكيف يتم إرسالها
class SmartNotificationManager {
  static final SmartNotificationManager _instance = SmartNotificationManager._internal();
  factory SmartNotificationManager() => _instance;
  SmartNotificationManager._internal();

  final DailyNotificationService _dailyNotificationService = DailyNotificationService();
  //final ComprehensiveUserAnalyzer _comprehensiveAnalyzer = ComprehensiveUserAnalyzer();

  static const String _lastNotificationCheckKey = 'last_notification_check';
  static const String _lastIntelligenceScoreKey = 'last_intelligence_score';
  static const String _notificationHistoryKey = 'notification_history';

  /// تهيئة مدير الإشعارات الذكية
  Future<void> initialize() async {
    try {
      await _dailyNotificationService.initialize();
      debugPrint('✅ تم تهيئة مدير الإشعارات الذكية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير الإشعارات الذكية: $e');
    }
  }

  /// فحص وإرسال الإشعارات الذكية
  Future<void> checkAndSendSmartNotifications() async {
    try {
      final now = DateTime.now();
      final lastCheck = await _getLastNotificationCheck();
      
      // فحص كل 6 ساعات فقط
      if (lastCheck != null && now.difference(lastCheck).inHours < 6) {
        debugPrint('⏰ لم يحن وقت فحص الإشعارات بعد');
        return;
      }

      debugPrint('🔍 بدء فحص الإشعارات الذكية');

   
  
      // حفظ وقت آخر فحص
      await _saveLastNotificationCheck(now);

      debugPrint('✅ تم الانتهاء من فحص الإشعارات الذكية');
    } catch (e) {
      debugPrint('❌ خطأ في فحص الإشعارات الذكية: $e');
    }
  }




  

  /// تفعيل/إلغاء تفعيل الإشعارات الذكية
  Future<void> toggleSmartNotifications(bool enabled) async {
    try {
      if (enabled) {
        await _dailyNotificationService.enableDailyNotifications();
      } else {
        await _dailyNotificationService.disableDailyNotifications();
      }
      debugPrint('🔔 تم ${enabled ? 'تفعيل' : 'إلغاء'} الإشعارات الذكية');
    } catch (e) {
      debugPrint('❌ خطأ في تغيير حالة الإشعارات: $e');
    }
  }

  /// فحص حالة الإشعارات
  Future<bool> areSmartNotificationsEnabled() async {
    try {
      return await _dailyNotificationService.areNotificationsEnabled();
    } catch (e) {
      debugPrint('❌ خطأ في فحص حالة الإشعارات: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الإشعارات الذكية
  Future<Map<String, dynamic>> getSmartNotificationStats() async {
    try {
      final basicStats = await _dailyNotificationService.getNotificationStats();
      final history = await _getNotificationHistory();
      final lastCheck = await _getLastNotificationCheck();
      
      return {
        ...basicStats,
        'notification_history_count': history.length,
        'last_check': lastCheck?.toIso8601String(),
        'smart_notifications_sent_today': _countTodayNotifications(history),
        'intelligence_notifications_count': _countNotificationsByType(history, 'intelligence_update'),
        'high_match_notifications_count': _countNotificationsByType(history, 'high_match_job'),
        'smart_matched_notifications_count': _countNotificationsByType(history, 'smart_matched_jobs'),
      };
    } catch (e) {
      debugPrint('❌ خطأ في جلب إحصائيات الإشعارات الذكية: $e');
      return {
        'enabled': false,
        'error': e.toString(),
      };
    }
  }

  /// مسح تاريخ الإشعارات
  Future<void> clearNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_notificationHistoryKey);
      debugPrint('🗑️ تم مسح تاريخ الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في مسح تاريخ الإشعارات: $e');
    }
  }

  // === دوال مساعدة خاصة ===

  Future<DateTime?> _getLastNotificationCheck() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final checkString = prefs.getString(_lastNotificationCheckKey);
      return checkString != null ? DateTime.tryParse(checkString) : null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _saveLastNotificationCheck(DateTime dateTime) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastNotificationCheckKey, dateTime.toIso8601String());
    } catch (e) {
      debugPrint('❌ خطأ في حفظ وقت آخر فحص: $e');
    }
  }

  Future<double?> _getLastIntelligenceScore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(_lastIntelligenceScoreKey);
    } catch (e) {
      return null;
    }
  }

  Future<void> _saveLastIntelligenceScore(double score) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_lastIntelligenceScoreKey, score);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ نقاط الذكاء: $e');
    }
  }

  Future<void> _saveNotificationHistory(String type, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = await _getNotificationHistory();
      
      history.add({
        'type': type,
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // الاحتفاظ بآخر 100 إشعار فقط
      if (history.length > 100) {
        history.removeRange(0, history.length - 100);
      }
      
      await prefs.setString(_notificationHistoryKey, json.encode(history));
    } catch (e) {
      debugPrint('❌ خطأ في حفظ تاريخ الإشعار: $e');
    }
  }

  Future<List<Map<String, dynamic>>> _getNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyString = prefs.getString(_notificationHistoryKey);
      
      if (historyString != null) {
        final List<dynamic> historyList = json.decode(historyString);
        return historyList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      debugPrint('❌ خطأ في جلب تاريخ الإشعارات: $e');
      return [];
    }
  }

  int _countTodayNotifications(List<Map<String, dynamic>> history) {
    final today = DateTime.now();
    return history.where((notification) {
      try {
        final timestamp = DateTime.tryParse(notification['timestamp']?.toString() ?? '') ?? DateTime.now();
        return timestamp.year == today.year &&
               timestamp.month == today.month &&
               timestamp.day == today.day;
      } catch (e) {
        return false;
      }
    }).length;
  }

  int _countNotificationsByType(List<Map<String, dynamic>> history, String type) {
    return history.where((notification) => notification['type'] == type).length;
  }

  /// تحديد مستوى الذكاء بناءً على النقاط
  String _determineIntelligenceLevel(double score) {
    if (score >= 0.9) return 'expert';
    if (score >= 0.7) return 'advanced';
    if (score >= 0.5) return 'intermediate';
    return 'beginner';
  }
} 