class NewsModel {
  int id;

  String image;

  String time;

  String title;

  String des;

  NewsModel({
    required this.id,
    required this.title,
    required this.time,
    required this.des,
    required this.image,
  });

  factory NewsModel.fromJson(n) {
    return NewsModel(
      id: (n["id"] ?? 0) is int ? n["id"] ?? 0 : int.tryParse(n["id"]?.toString() ?? '0') ?? 0,
      title: n["title"]?.toString() ?? '',
      time: n["time"]?.toString() ?? '',
      des: n["des"]?.toString() ?? '',
      image: n["image"]?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'time': time,
      'des': des,
      'image': image,
    };
  }
}
