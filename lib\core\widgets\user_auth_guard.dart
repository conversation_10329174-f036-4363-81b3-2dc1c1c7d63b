import 'package:flutter/material.dart';
import '../services/user_service.dart';

/// Widget لحماية الصفحات والتأكد من صلاحية الوصول
class UserAuthGuard extends StatefulWidget {
  final Widget child;
  final String requiredUserType; // 'company' أو 'seeker'
  final Widget? fallbackWidget; // Widget بديل في حالة عدم وجود صلاحية

  const UserAuthGuard({
    Key? key,
    required this.child,
    required this.requiredUserType,
    this.fallbackWidget,
  }) : super(key: key);

  @override
  _UserAuthGuardState createState() => _UserAuthGuardState();
}

class _UserAuthGuardState extends State<UserAuthGuard> {
  bool _isLoading = true;
  bool _hasAccess = false;

  @override
  void initState() {
    super.initState();
    _checkAccess();
  }

  Future<void> _checkAccess() async {
    try {
      final userType = await UserService.getCurrentUserType();
      
      setState(() {
        _hasAccess = userType == widget.requiredUserType;
        _isLoading = false;
      });

      // إذا لم يكن لديه صلاحية، توجيه للصفحة المناسبة
      if (!_hasAccess && mounted) {
        if (userType == null) {
          // لا يوجد مستخدم مسجل دخول
          Navigator.pushReplacementNamed(context, '/login');
        } else {
          // المستخدم من نوع مختلف، توجيه للداشبورد المناسب
          await UserService.navigateToUserDashboard(context);
        }
      }
    } catch (e) {
      setState(() {
        _hasAccess = false;
        _isLoading = false;
      });
      
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_hasAccess) {
      return widget.child;
    }

    return widget.fallbackWidget ?? 
           const Scaffold(
             body: Center(
               child: Text('ليس لديك صلاحية للوصول لهذه الصفحة'),
             ),
           );
  }
}

/// Widget خاص بحماية صفحات الشركة
class CompanyAuthGuard extends StatelessWidget {
  final Widget child;
  final Widget? fallbackWidget;

  const CompanyAuthGuard({
    Key? key,
    required this.child,
    this.fallbackWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UserAuthGuard(
      requiredUserType: 'company',
      fallbackWidget: fallbackWidget,
      child: child,
    );
  }
}

/// Widget خاص بحماية صفحات الباحث عن عمل
class SeekerAuthGuard extends StatelessWidget {
  final Widget child;
  final Widget? fallbackWidget;

  const SeekerAuthGuard({
    Key? key,
    required this.child,
    this.fallbackWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UserAuthGuard(
      requiredUserType: 'seeker',
      fallbackWidget: fallbackWidget,
      child: child,
    );
  }
} 