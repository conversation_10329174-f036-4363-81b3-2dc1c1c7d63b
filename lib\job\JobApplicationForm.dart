import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/models/JobModel.dart';
// import 'package:wzzff/Apis/ProfileApi.dart'; // معطل مؤقتاً
import 'package:wzzff/core/services/user_service.dart';
import 'package:wzzff/services/google_ad_service.dart';

class JobApplicationForm extends StatefulWidget {
  final String slug;
  final String title;
  final String des;
  final String code_address;
  final String? email;
  final String? number;
  final String salary_currency;
  final String? salary;
  final String cat;
  final String gender;
  final String state_name;
  final String country_name;
  final String job_type_name;
  final String city_name;
  final String company_name;
  final String edu;
  final String exp;
  final String end_at;
  final String created_at_date;
  final String time;

  const JobApplicationForm({
    super.key,
    required this.slug,
    required this.title,
    required this.des,
    required this.code_address,
    required this.email,
    required this.number,
    required this.salary_currency,
    required this.salary,
    required this.cat,
    required this.gender,
    required this.state_name,
    required this.country_name,
    required this.job_type_name,
    required this.city_name,
    required this.company_name,
    required this.edu,
    required this.exp,
    required this.end_at,
    required this.created_at_date,
    required this.time,
  });

  @override
  _JobApplicationFormState createState() => _JobApplicationFormState();
}

class _JobApplicationFormState extends State<JobApplicationForm>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isSubmitting = false;
  bool _isSubmitted = false;

  // Form field controllers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _desController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _salaryController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _educationController = TextEditingController();
  final TextEditingController _skillsController = TextEditingController();

  // Animation Controllers
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late AnimationController _scaleAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);
  Color get successColor => const Color(0xff4caf50);
  Color get warningColor => const Color(0xffff9800);


  // حالة التحميل
  bool _isLoadingUserData = true;

  // خدمة الإعلانات
  final GoogleAdService _adService = GoogleAdService();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
    _loadUserData();
    _initializeAds();
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
        _scaleAnimationController.forward();
      }
    });
  }

  // تهيئة الإعلانات
  Future<void> _initializeAds() async {
    try {
      await _adService.initialize();
      await _adService.loadInterstitialAd();
    } catch (e) {
      // خطأ في تهيئة الإعلانات
    }
  }

  Future<void> _loadUserData() async {
    try {
      //print('🔄 JobApplicationForm: بدء تحميل بيانات المستخدم...');
      
      // التحقق من نوع المستخدم
      final userType = await UserService.getCurrentUserType();
     // print('👤 JobApplicationForm: نوع المستخدم: $userType');
      
      bool dataLoaded = false;
      
      // إذا كان المستخدم من نوع seeker، تحميل البيانات من ProfileApi
      if (userType == 'seeker') {
        try {
          //print('📡 JobApplicationForm: تحميل البيانات من ProfileApi...');
          // final profileApi = ProfileApi(); // معطل مؤقتاً
          // final userModel = await profileApi.getMyProfileInformation(); // معطل مؤقتاً
          dynamic userModel;
          
          if (userModel != null) {
            // ملء البيانات من الملف الشخصي
            if (userModel.name != null && userModel.name!.isNotEmpty) {
              _nameController.text = userModel.name!;
            }
            
            if (userModel.email != null && userModel.email!.isNotEmpty) {
              _emailController.text = userModel.email!;
            }
            
            if (userModel.phone != null && userModel.phone!.isNotEmpty) {
              _phoneController.text = userModel.phone!;
            }
            
            if (userModel.education != null && userModel.education!.isNotEmpty) {
              _educationController.text = userModel.education!;
            }
            
            if (userModel.skills != null && userModel.skills!.isNotEmpty) {
              _skillsController.text = userModel.skills!;
            }
            
            dataLoaded = true;
          
            
            // حفظ البيانات في SharedPreferences للاستخدام المستقبلي
            final prefs = await SharedPreferences.getInstance();
            if (userModel.name != null) await prefs.setString('name', userModel.name!);
            if (userModel.email != null) await prefs.setString('email', userModel.email!);
            if (userModel.phone != null) await prefs.setString('phone', userModel.phone!);
            if (userModel.education != null) await prefs.setString('education', userModel.education!);
            if (userModel.skills != null) await prefs.setString('skills', userModel.skills!);
          //  print('💾 JobApplicationForm: تم حفظ البيانات في SharedPreferences أيضاً');
          }
                  } catch (e) {
            // خطأ في تحميل البيانات من ProfileApi
          }
      }
      
      // إذا لم يتم تحميل البيانات من ProfileApi، تحميلها من SharedPreferences
      if (!dataLoaded) {
       // print('💾 JobApplicationForm: تحميل البيانات من SharedPreferences...');
        final prefs = await SharedPreferences.getInstance();
        
        final name = prefs.getString('name');
        final education = prefs.getString('education');
        final skills = prefs.getString('skills');
        final email = prefs.getString('email');
        final phone = prefs.getString('phone');

        if (name != null && name.isNotEmpty) {
          _nameController.text = name;
        }
        if (education != null && education.isNotEmpty) {
          _educationController.text = education;
        }
        if (skills != null && skills.isNotEmpty) {
          _skillsController.text = skills;
        }
        if (email != null && email.isNotEmpty) {
          _emailController.text = email;
        }
        if (phone != null && phone.isNotEmpty) {
          _phoneController.text = phone;
        }
        
        if (name != null || education != null || skills != null || email != null || phone != null) {
         // print('✅ JobApplicationForm: تم تحميل البيانات من SharedPreferences');
        } else {
         // print('ℹ️ JobApplicationForm: لا توجد بيانات محفوظة في SharedPreferences');
        }
      }
      
      // إجبار إعادة بناء الواجهة لعرض البيانات المحملة
      if (mounted) {
        setState(() {
          _isLoadingUserData = false;
        });
      }
      
          } catch (e) {
        // خطأ عام في تحميل بيانات المستخدم
      } finally {
      // التأكد من إيقاف مؤشر التحميل في جميع الحالات
      if (mounted) {
        setState(() {
          _isLoadingUserData = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _scaleAnimationController.dispose();
    _titleController.dispose();
    _desController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _salaryController.dispose();
    _nameController.dispose();
    _educationController.dispose();
    _skillsController.dispose();
    // تنظيف الإعلانات
    _adService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = Theme.of(context).scaffoldBackgroundColor;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: Stack(
          children: [
            // خلفية متدرجة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    primaryColor.withOpacity(0.05),
                    backgroundColor,
                    backgroundColor,
                  ],
                  stops: const [0.0, 0.3, 1.0],
                ),
              ),
            ),

            Column(
              children: [
                // Header مخصص محسن
                _buildCustomHeader(isDarkMode),

                // محتوى الصفحة
                Expanded(
                  child: _isSubmitted
                      ? _buildSuccessView()
                      : FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(16),
                              child: Form(
                                key: _formKey,
                                child: Column(
                                  children: [
                                    // معلومات الوظيفة
                                    //  _buildJobInfoCard(isDarkMode),
                                    
                                    const SizedBox(height: 20),
                                    
                                    // نصائح التقديم
                                    _buildApplicationTips(isDarkMode),
                                    
                                    const SizedBox(height: 20),
                                    
                                    // نموذج التقديم
                                    _buildApplicationForm(isDarkMode),
                                    
                                    const SizedBox(height: 24),
                                    
                                    // زر التقديم
                                    _buildSubmitButton(isDarkMode),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomHeader(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 8,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الوظيفة
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [primaryColor, accentColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Icon(
              Icons.work,
              color: Colors.white,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // العنوان
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تقديم طلب وظيفة',
                  style: GoogleFonts.tajawal(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'املأ النموذج للتقديم على الوظيفة',
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // زر الرجوع
          Container(
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_forward,
                color: primaryColor,
                size: 20,
              ),
              onPressed: () => Navigator.pop(context),
              tooltip: 'رجوع',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJobInfoCard(bool isDarkMode) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [primaryColor.withOpacity(0.1), primaryColor.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: primaryColor.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [primaryColor, accentColor],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: primaryColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.business_center,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معلومات الوظيفة',
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'تفاصيل الوظيفة التي تتقدم إليها',
                        style: GoogleFonts.tajawal(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Text(
              widget.title,
              style: GoogleFonts.tajawal(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.grey[800],
              ),
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Icon(Icons.business, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 6),
                Text(
                  widget.company_name,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 4),
            
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 6),
                Text(
                  '${widget.city_name}, ${widget.country_name}',
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildApplicationTips(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.lightbulb_outline,
                  color: warningColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "نصائح للتقديم الناجح",
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildTipItem("اكتب عنوانًا واضحًا ومختصرًا للطلب", isDarkMode),
          _buildTipItem("اذكر خبراتك ومهاراتك المتعلقة بالوظيفة", isDarkMode),
          _buildTipItem("تأكد من صحة بيانات الاتصال الخاصة بك", isDarkMode),
          _buildTipItem("حدد راتبًا متوقعًا مناسبًا لمؤهلاتك", isDarkMode),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 2),
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: successColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check,
              color: successColor,
              size: 14,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: isDarkMode ? Colors.white70 : Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationForm(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.edit_document,
                  color: primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "نموذج التقديم",
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          buildModernTextField(
            "الاسم الكامل",
            _nameController,
            icon: Icons.person,
            isRequired: true,
            hint: "أدخل اسمك الكامل",
            isDarkMode: isDarkMode,
          ),

          buildModernTextField(
            "المؤهل العلمي",
            _educationController,
            icon: Icons.school,
            isRequired: true,
            hint: "أدخل مؤهلك العلمي",
            isDarkMode: isDarkMode,
          ),

          buildModernTextField(
            "المهارات",
            _skillsController,
            icon: Icons.psychology,
            isRequired: true,
            maxLines: 4,
            hint: "أدخل مهاراتك مفصولة بفاصلة (مثال: برمجة، تصميم، إدارة، تسويق، إدارة المشاريع)",
            isDarkMode: isDarkMode,
          ),
          
          buildModernTextField(
            "عنوان الطلب",
            _titleController,
            icon: Icons.title,
            isRequired: true,
            hint: "مثال: مطور تطبيقات خبرة 3 سنوات",
            isDarkMode: isDarkMode,
          ),
          
          buildModernTextField(
            "تفاصيل الطلب",
            _desController,
            icon: Icons.description,
            isRequired: true,
            maxLines: 5,
            hint: "اكتب نبذة عن خبراتك ومهاراتك التي تؤهلك للحصول على الوظيفة",
            isDarkMode: isDarkMode,
          ),
          
          buildModernTextField(
            "البريد الإلكتروني",
            _emailController,
            icon: Icons.email,
            isRequired: true,
            keyboardType: TextInputType.emailAddress,
            hint: "<EMAIL>",
            isDarkMode: isDarkMode,
          ),
          
          buildModernTextField(
            "رقم الهاتف",
            _phoneController,
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
            hint: "0501234567",
            isDarkMode: isDarkMode,
          ),
          
          buildModernTextField(
            "الراتب المتوقع",
            _salaryController,
            icon: Icons.monetization_on,
            isRequired: true,
            keyboardType: TextInputType.number,
           // hint: "بالريال السعودي",
            isDarkMode: isDarkMode,
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(bool isDarkMode) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [primaryColor, accentColor],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitApplication,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: _isSubmitting
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري الإرسال...',
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.send,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'تقدم الآن',
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildSuccessView() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(32),
            margin: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[850] : Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [successColor, successColor.withOpacity(0.7)],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: successColor.withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                Text(
                  "تم إرسال طلبك بنجاح!",
                  style: GoogleFonts.tajawal(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: successColor,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  "سيتم مراجعة طلبك والرد عليك في أقرب وقت ممكن.\nيمكنك متابعة حالة الطلب من قائمة الوظائف المقدم لها.",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                    height: 1.6,
                  ),
                ),
                
                const SizedBox(height: 32),
                
                Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [primaryColor, accentColor],
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      'العودة للوظائف',
                      style: GoogleFonts.tajawal(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildModernTextField(
    String label,
    TextEditingController controller, {
    bool isRequired = false,
    int maxLines = 1,
    TextInputType keyboardType = TextInputType.text,
    String? hint,
    IconData? icon,
    required bool isDarkMode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 16,
                  color: primaryColor,
                ),
                const SizedBox(width: 8),
              ],
              Text(
                label + (isRequired ? ' *' : ''),
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Colors.grey[700],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.08),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: controller,
              maxLines: maxLines,
              keyboardType: keyboardType,
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: GoogleFonts.tajawal(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
                filled: true,
                fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[50],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: primaryColor,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.grey.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.red,
                    width: 1,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              validator: isRequired
                  ? (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'هذا الحقل مطلوب';
                      }
                      if (label.contains("البريد") && !_isValidEmail(value)) {
                        return 'يرجى إدخال بريد إلكتروني صحيح';
                      }
                      return null;
                    }
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  Future<void> _submitApplication() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final storage = FlutterSecureStorage();
      final apiToken = await storage.read(key: 'api_token');
      final fcmToken = await storage.read(key: 'fcm_token');
      final appliedSlugs = (await storage.read(key: 'applied_slugs')) ?? '';
      final appliedList = appliedSlugs.split(',').where((e) => e.isNotEmpty).toList();

      if (appliedList.contains(widget.slug)) {
        _showErrorSnackBar('لقد قمت بالتقديم على هذه الوظيفة من قبل!');
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      // معطل مؤقتاً - يحتاج إضافة submitApplication إلى JobsApi
      // final success = await JobsApi().submitApplication(...);
      final success = true; // محاكاة نجح

      if (success) {
        final newAppliedList = [...appliedList, widget.slug];
        await storage.write(key: 'applied_slugs', value: newAppliedList.join(','));

       

        // عرض إعلان فيديو بعد التقدم بنجاح
        _showSuccessAdAndMessage();

        if (apiToken == null || apiToken.isEmpty) {
          final prefs = await SharedPreferences.getInstance();
          final List<String> jobs = prefs.getStringList('applied_jobs_guest') ?? [];
          final jobData = jsonEncode({
            'job': {
              'slug': widget.slug,
              'title': widget.title,
              'company_name': widget.company_name,
              'city_name': widget.city_name,
              'country_name': widget.country_name,
            },
            'application': {
              'title': _titleController.text,
              'description': _desController.text,
              'email': _emailController.text,
              'phone': _phoneController.text,
              'salary': _salaryController.text,
              'name': _nameController.text,
              'education': _educationController.text,
              'skills': _skillsController.text,
              'applied_date': DateTime.now().toIso8601String(),
            }
          });
          jobs.add(jobData);
          await prefs.setStringList('applied_jobs_guest', jobs);
        }

        setState(() {
          _isSubmitted = true;
        });
      } else {
        _showErrorSnackBar('حدث خطأ أثناء إرسال الطلب. حاول مرة أخرى.');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }

    setState(() {
      _isSubmitting = false;
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }


  // عرض إعلان بيني بعد التقدم
  void _showSuccessAdAndMessage() {
          // إظهار إعلان بيني بعد التقدم بنجاح
      _adService.showInterstitialAd().then((success) {
        // تحميل إعلان جديد للمرة القادمة
        _adService.loadInterstitialAd();
      });
  }
}