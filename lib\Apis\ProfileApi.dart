import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:wzzff/core/constants/Constants.dart';
import 'package:wzzff/core/utils/app_messages.dart';
import 'package:wzzff/models/Encryption.dart';
import 'package:wzzff/models/SeekerModel.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/main.dart';
import 'package:wzzff/models/JobApplication.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// واجهة برمجة التطبيقات الخاصة بالملف الشخصي
///
/// هذه الفئة تحتوي على جميع الطرق المتعلقة بإدارة الملف الشخصي
/// مثل التحقق من مشاهدة الملف الشخصي، تحديث الملف الشخصي، الحصول على معلومات الملف الشخصي، إلخ
class ProfileApi {

  /// عرض رسالة خطأ للمستخدم
  ///
  /// يستخدم هذا المثود لعرض رسائل الخطأ للمستخدم عندما يحدث خطأ في أي من العمليات
  // تحديث دوال عرض الرسائل
  void _showErrorToast(String message) {
    AppMessages.showError(message);
  }

  /// التحقق من مشاهدة الملف الشخصي
  ///
  /// يرسل طلب إلى السيرفر للتحقق مما إذا كانت هناك شركة قد شاهدت الملف الشخصي
  /// يعيد `true` إذا كانت هناك مشاهدة، و `false` إذا لم تكن هناك مشاهدة
  Future<bool> checkProfileView(String apiToken) async {
    try {
      // إرسال طلب إلى السيرفر للتحقق من مشاهدة الملف الشخصي
      final url = Uri.parse('${Constants.url}/apiwzzff/check-profile-view');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'api_token': apiToken}),
      );

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);

          // التحقق من وجود مفتاح company_view في البيانات
          if (data.containsKey('company_view')) {
            // التحقق من قيمة company_view
            // القيمة ستكون إما "1" أو "0" كنص
            final companyView = data['company_view'];

            // إرجاع true إذا كانت القيمة "1"، وإلا إرجاع false
            return companyView == '1';
          } else {
            return false;
          }
        } catch (e) {
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      // إرجاع false في حالة حدوث أي خطأ
      return false;
    }
  }

  /// تحديث الملف الشخصي
  ///
  /// يرسل طلب إلى السيرفر لتحديث معلومات الملف الشخصي
  /// يعيد `true` إذا تم التحديث بنجاح، و `false` إذا فشل التحديث
  Future<bool> updateProfile(String apiToken, Map<String, dynamic> profileData) async {
    try {
      // إرسال طلب إلى السيرفر لتحديث الملف الشخصي
      final url = Uri.parse('${Constants.url}/apiwzzff/update-profile');

      // إضافة api_token إلى بيانات الملف الشخصي
      profileData['api_token'] = apiToken;

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(profileData),
      );

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);

          // التحقق من وجود مفتاح status في البيانات
          if (data.containsKey('status')) {
            // التحقق من قيمة status
            // القيمة ستكون إما 1 أو 0
            final status = data['status'];

            // إرجاع true إذا كانت القيمة 1، وإلا إرجاع false
            return status == 1;
          } else {
            return false;
          }
        } catch (e) {
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      // إرجاع false في حالة حدوث أي خطأ
      return false;
    }
  }

  /// الحصول على معلومات الملف الشخصي
  ///
  /// يرسل طلب إلى السيرفر للحصول على معلومات الملف الشخصي
  /// يعيد بيانات الملف الشخصي إذا نجح الطلب، وإلا يعيد null
  Future<Map<String, dynamic>?> getProfileInfo(String apiToken) async {
    try {
      // إرسال طلب إلى السيرفر للحصول على معلومات الملف الشخصي
      final url = Uri.parse('${Constants.url}/apiwzzff/get-profile-info');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'api_token': apiToken}),
      );

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);

          // التحقق من وجود مفتاح status في البيانات
          if (data.containsKey('status') && data['status'] == 1) {
            // إرجاع بيانات الملف الشخصي
            return data['data'];
          } else {
            return null;
          }
        } catch (e) {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      // إرجاع null في حالة حدوث أي خطأ
      return null;
    }
  }

  /// الحصول على معلومات الملف الشخصي للباحث عن عمل
  ///
  /// يرسل طلب إلى السيرفر للحصول على معلومات الملف الشخصي للباحث عن عمل
  /// يعيد نموذج الباحث عن عمل إذا نجح الطلب
  Future<SeekerModel> getMyProfileInformation() async {
    final storage = FlutterSecureStorage();
    String url = "${Constants.url}/apiwzzff/getMyProfileSeeker";
    String? apiToken = await storage.read(key: "api_token");
    Uri urlConvert = Uri.parse(url);
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": "Bearer " +
          Encryption.instance.encrypt(jsonEncode({"api_token": apiToken}))
    };
    http.Response response = await http.post(urlConvert, headers: headers);

    String decryptData = Encryption.instance.decrypt(
      response.body,
    );
    Map<String, dynamic> user = jsonDecode(decryptData);
    SeekerModel userModel = SeekerModel.fromJson(user["user"]);
    return userModel;
  }

  /// تحديث الملف الشخصي للباحث عن عمل
  ///
  /// يرسل طلب إلى السيرفر لتحديث معلومات الملف الشخصي للباحث عن عمل
  /// يعيد void
  Future<void> updateSeekerProfile(Map<String, dynamic> userData) async {
    final storage = FlutterSecureStorage();
    String? apiToken = await storage.read(key: "api_token");

    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": "Bearer " +
          Encryption.instance.encrypt(jsonEncode({"api_token": apiToken}))
    };

    String url = "${Constants.url}/apiwzzff/update_seeker_profile";
    Uri urlConvert = Uri.parse(url);
    userData["api_token"] = apiToken;
    String encryptedData = Encryption.instance.encrypt(jsonEncode(userData));

    http.Response response = await http.post(
      urlConvert,
      headers: headers,
      body: jsonEncode({"data": encryptedData}),
    );
    String decryptData = Encryption.instance.decrypt(response.body);
    Map<String, dynamic> data = jsonDecode(decryptData);

    if (data["status"] == 0) {
      throw Exception(data["message"] ?? "حدث خطأ أثناء تحديث الملف الشخصي");
    }
  }

  /// رفع صورة الملف الشخصي
  ///
  /// يرسل طلب إلى السيرفر لرفع صورة الملف الشخصي
  /// يعيد رابط الصورة إذا نجح الطلب، وإلا يعيد null
  Future<String?> uploadProfileImage(File imageFile) async {
    try {
      final storage = FlutterSecureStorage();
      String? apiToken = await storage.read(key: "api_token");

      if (apiToken == null) {
        return null;
      }

      Map<String, String> headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": "Bearer " +
            Encryption.instance.encrypt(jsonEncode({"api_token": apiToken}))
      };

      var request = http.MultipartRequest(
        'POST',
        Uri.parse("${Constants.url}/apiwzzff/upload_profile_image")
      );

      request.files.add(
        await http.MultipartFile.fromPath('image', imageFile.path)
      );

      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      String responseBody = await response.stream.bytesToString();
      Map<String, dynamic> data = jsonDecode(responseBody);

      if (response.statusCode == 200 && data["status"] == 1) {
        return data["image"];
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// تحديث رمز FCM في السيرفر
  ///
  /// يرسل طلب إلى السيرفر لتحديث رمز FCM
  /// يعيد `true` إذا تم التحديث بنجاح، و `false` إذا فشل التحديث
  Future<bool> updateFcmToken(String fcmToken) async {
    try {
      final storage = FlutterSecureStorage();
      String? apiToken = await storage.read(key: "api_token");

      if (apiToken == null) {
        return false;
      }

      Map<String, String> headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": "Bearer " +
            Encryption.instance.encrypt(jsonEncode({"api_token": apiToken}))
      };

      String url = "${Constants.url}/apiwzzff/update_fcm_token";
      Uri urlConvert = Uri.parse(url);

      String encryptedData = Encryption.instance.encrypt(jsonEncode({
        "fcm_token": fcmToken,
        "device_type": Platform.isAndroid ? "android" : "ios"
      }));

      http.Response response = await http.post(
        urlConvert,
        headers: headers,
        body: jsonEncode({"data": encryptedData}),
      );

      String decryptData = Encryption.instance.decrypt(response.body);
      Map<String, dynamic> data = jsonDecode(decryptData);

      if (data["status"] == 1) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// الحصول على السيرة الذاتية
  ///
  /// يرسل طلب إلى السيرفر للحصول على السيرة الذاتية
  /// يعيد رابط السيرة الذاتية إذا نجح الطلب، وإلا يعيد null
  Future<String?> getMyCv() async {
    final storage = FlutterSecureStorage();
    String? apiToken = await storage.read(key: "api_token");
    if (apiToken != null) {
      Map<String, String> headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": "Bearer " +
            Encryption.instance.encrypt(jsonEncode({"api_token": apiToken}))
      };
      String url = "${Constants.url}/apiwzzff/getMycv";
      Uri urlConvert = Uri.parse(url);
      http.Response response = await http.post(
        urlConvert,
        headers: headers,
      );
      Map<String, dynamic> data = jsonDecode(response.body);
      if (data["status"] == 0) {
        _showErrorToast("تحتاج أولاً لإضافة سيرة ذاتية لعرضها");
        return null;
      }
      return data["cv"].toString();
    } else {
      return null;
    }
  }

  /// الحصول على رمز الدولة
  ///
  /// يعيد رمز الدولة بناءً على اسم الدولة
  String getCountryCode(String country) {
    switch (country) {
      case "السعودية": return "sa";
      case "مصر": return "eg";
      case "الامارات": return "ae";
      case "الأردن": return "jo";
      case "البحرين": return "bh";
      case "الكويت": return "kw";
      case "قطر": return "qa";
      case "عمان": return "om";
      case "العراق": return "iq";
      case "الجزائر": return "dz";
      case "المغرب": return "ma";
      case "تونس": return "tn";
      case "لبنان": return "lb";
      case "سوريا": return "sy";
      case "السودان": return "sd";
      case "ليبيا": return "ly";
      case "فلسطين": return "ps";
      case "اليمن": return "ye";
      default: return country.toLowerCase();
    }
  }

  Future<List<JobApplication>> getAppliedJobs() async {
    try {
      final storage = FlutterSecureStorage();
      final apiToken = await storage.read(key: 'api_token');
      
      if (apiToken == null || apiToken.isEmpty) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      final response = await http.post(
        Uri.parse('${Constants.url}/apiwzzff/get_applay_to_jobs'),
        headers: {
          'Authorization': 'Bearer $apiToken',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'success') {
          final List<dynamic> jobsData = data['data'];
          return jobsData.map((jobData) {
            final job = jobData['job'];
            final application = job['application'];
            
            return JobApplication(
              id: job['id'] ?? 0,
              slug: job['slug'] ?? '',
              title: job['title'] ?? '',
              company_name: job['company_name'] ?? '',
              city_name: job['city_name'] ?? '',
              country_name: job['country_name'] ?? '',
              applied_date: application['applied_date'],
              status: _getApplicationStatus(application['status']),
              interview_date: application['interview_date'],
              application_title: application['title'],
              application_email: application['email'],
              application_phone: application['phone'],
              application_salary: application['salary'],
              application_description: application['description'],
            );
          }).toList();
        } else {
          throw Exception(data['message'] ?? 'حدث خطأ أثناء جلب البيانات');
        }
      } else {
        throw Exception('فشل الاتصال بالسيرفر');
      }
    } catch (e) {
      throw Exception('حدث خطأ: $e');
    }
  }

  ApplicationStatus _getApplicationStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ApplicationStatus.pending;
      case 'viewed':
        return ApplicationStatus.viewed;
      case 'accepted':
        return ApplicationStatus.accepted;
      case 'rejected':
        return ApplicationStatus.rejected;
      default:
        return ApplicationStatus.pending;
    }
  }

  Future<bool> sendGuestAppliedJobsToServer(String apiToken) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> jobs = prefs.getStringList('applied_jobs_guest') ?? [];
      
      if (jobs.isEmpty) {
        return true;
      }

      final response = await http.post(
        Uri.parse('${Constants.url}/apiwzzff/add_my_applad_jobs_after_login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $apiToken',
        },
        body: jsonEncode({
          'jobs': jobs,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 1) {
          await prefs.remove('applied_jobs_guest');
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// رفع السيرة الذاتية
  ///
  /// يرسل طلب إلى السيرفر لرفع السيرة الذاتية
  /// يعيد true إذا نجح الرفع، وإلا يعيد false
  Future<bool> uploadCv(File cvFile) async {
    try {
      final storage = FlutterSecureStorage();
      String? apiToken = await storage.read(key: "api_token");
      
      if (apiToken == null) {
        return false;
      }

      String url = "${Constants.url}/apiwzzff/upload_cv";
      Uri urlConvert = Uri.parse(url);

      var request = http.MultipartRequest('POST', urlConvert);
      request.headers.addAll({
        'Authorization': 'Bearer ${Encryption.instance.encrypt(jsonEncode({"api_token": apiToken}))}'
      });
      
      request.files.add(
        await http.MultipartFile.fromPath('cv_file', cvFile.path)
      );

      var response = await request.send();
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}