import 'package:flutter/material.dart';
import 'package:wzzff/models/SpinnerDataModel.dart';

class JobFormData {
  // الحقول الأساسية
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descController = TextEditingController();
  final TextEditingController reqController = TextEditingController();
  final TextEditingController salaryController = TextEditingController();
  
  // الحقول الجديدة الاختيارية
  final TextEditingController companyEmailController = TextEditingController();
  final TextEditingController companyPhoneController = TextEditingController();
  
  // القوائم المنسدلة
  SpinnerDataModel? selectedCountry;
  SpinnerDataModel? selectedCity;
  String? selectedJobType;
  String? selectedCategory;
  String? selectedEducation;
  String? selectedExperience;
  String? selectedGender;
  String? selectedCurrency;
  DateTime? endDate;

  // بيانات السيرفر
  List<SpinnerDataModel> countries = [];
  List<SpinnerDataModel> cities = [];
  List<String> jobTypes = [];
  List<String> categories = [];
  List<String> educationLevels = [];
  List<String> experienceLevels = [];
  List<String> genders = [];
  List<String> currencies = [];

  // حالة التحميل
  bool isCitiesLoading = false;

  void dispose() {
    titleController.dispose();
    descController.dispose();
    reqController.dispose();
    salaryController.dispose();
    companyEmailController.dispose();
    companyPhoneController.dispose();
  }

  Map<String, dynamic> toJson() {
    return {
      'title': titleController.text.trim(),
      'description': descController.text.trim(),
      'requirements': reqController.text.trim(),
      'job_type': selectedJobType,
      'category': selectedCategory,
      'country': selectedCountry?.name,
      'city': selectedCity?.name,
      'education_level': selectedEducation,
      'experience_level': selectedExperience,
      'gender_requirement': selectedGender,
      'salary': salaryController.text.trim(),
      'salary_currency': selectedCurrency,
      'end_date': endDate?.toIso8601String(),
      'company_email': companyEmailController.text.trim(),
      'company_phone': companyPhoneController.text.trim(),
    };
  }

  // التحقق من صحة الخطوة
  ValidationResult validateStep(int step) {
    switch (step) {
      case 0:
        if (titleController.text.isEmpty) {
          return ValidationResult(false, 'يرجى إدخال عنوان الوظيفة');
        }
        if (selectedJobType == null) {
          return ValidationResult(false, 'يرجى اختيار نوع الوظيفة');
        }
        if (selectedCategory == null) {
          return ValidationResult(false, 'يرجى اختيار تصنيف الوظيفة');
        }
        if (selectedCountry == null) {
          return ValidationResult(false, 'يرجى اختيار الدولة');
        }
        if (selectedCity == null) {
          return ValidationResult(false, 'يرجى اختيار المدينة');
        }
        return ValidationResult(true, '');
        
      case 1:
        if (descController.text.isEmpty) {
          return ValidationResult(false, 'يرجى إدخال وصف الوظيفة');
        }
        if (selectedEducation == null) {
          return ValidationResult(false, 'يرجى اختيار المستوى التعليمي');
        }
        if (selectedExperience == null) {
          return ValidationResult(false, 'يرجى اختيار مستوى الخبرة');
        }
        if (selectedGender == null) {
          return ValidationResult(false, 'يرجى اختيار الجنس المطلوب');
        }
        return ValidationResult(true, '');
        
      case 2:
        if (reqController.text.isEmpty) {
          return ValidationResult(false, 'يرجى إدخال متطلبات الوظيفة');
        }
        if (salaryController.text.isEmpty) {
          return ValidationResult(false, 'يرجى إدخال الراتب');
        }
        if (selectedCurrency == null) {
          return ValidationResult(false, 'يرجى اختيار العملة');
        }
        if (endDate == null) {
          return ValidationResult(false, 'يرجى اختيار تاريخ انتهاء التقديم');
        }
        return ValidationResult(true, '');
        
      case 3:
      case 4:
        return ValidationResult(true, '');
        
      default:
        return ValidationResult(false, 'خطوة غير صحيحة');
    }
  }
}

class ValidationResult {
  final bool isValid;
  final String errorMessage;
  
  ValidationResult(this.isValid, this.errorMessage);
} 