import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ProfileStepHeader extends StatelessWidget {
  final int currentStep;

  const ProfileStepHeader({
    super.key,
    required this.currentStep,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStepCircle(0, "البيانات\nالشخصية"),
          _buildStepConnector(currentStep >= 1),
          _buildStepCircle(1, "معلومات\nالاتصال"),
          _buildStepConnector(currentStep >= 2),
          _buildStepCircle(2, "المؤهلات\nوالخبرات"),
          _buildStepConnector(currentStep >= 3),
          _buildStepCircle(3, "تفضيلات\nالعمل"),
        ],
      ),
    );
  }

  Widget _buildStepCircle(int step, String label) {
    return Builder(
      builder: (context) {
        bool isActive = currentStep >= step;
        bool isCurrent = currentStep == step;
        final primaryColor = Theme.of(context).colorScheme.primary;

        return Column(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isActive ? Colors.white : Colors.white.withOpacity(0.5),
                border: Border.all(
                  color: isCurrent ? Colors.white : Colors.transparent,
                  width: 2,
                ),
              ),
              child: Center(
                child: Text(
                  "${step + 1}",
                  style: GoogleFonts.tajawal(
                    color: isActive ? primaryColor : Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 6),
            Text(
              label,
              textAlign: TextAlign.center,
              style: GoogleFonts.tajawal(
                color: Colors.white,
                fontSize: 10,
                fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        );
      }
    );
  }

  Widget _buildStepConnector(bool isActive) {
    return Container(
      width: 20,
      height: 2,
      color: isActive ? Colors.white : Colors.white.withOpacity(0.5),
    );
  }
}