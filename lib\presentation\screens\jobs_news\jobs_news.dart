import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wzzff/models/JobNewsModel.dart';
import 'package:wzzff/presentation/screens/jobs_news/job_news_show.dart';
import 'package:wzzff/presentation/components/ListShimmerNews.dart';
import 'package:wzzff/presentation/widgets/ad_job_card.dart';
import 'dart:io';
import 'package:wzzff/Apis/job_news_api.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:wzzff/services/google_ad_service.dart';

/// شاشة أخبار الوظائف مع نظام التتبع الذكي المتقدم
/// 
/// المميزات المضافة:
/// 🎯 تتبع زيارة قسم الأخبار
/// 📰 تتبع مشاهدة الأخبار الفردية
/// 🔍 تحليل محتوى الأخبار واستخراج كلمات مفتاحية
/// 👆 تتبع النقر على "قراءة المزيد"
/// 🔄 تتبع تحديث الأخبار (Pull to Refresh)
/// 📊 تحليل اهتمامات المستخدم بناءً على الأخبار المقروءة
/// 
/// هذا النظام يساعد في:
/// - فهم اهتمامات المستخدم من خلال نوع الأخبار التي يقرأها
/// - تحسين اقتراحات الوظائف بناءً على الاهتمامات المستنتجة
/// - تحليل سلوك التصفح والتفاعل

class JobsNewsScreen extends StatefulWidget {
  const JobsNewsScreen({super.key});

  @override
  State<JobsNewsScreen> createState() => _JobsNewsScreenState();
}

class _JobsNewsScreenState extends State<JobsNewsScreen> {
  // خدمة الإعلانات المركزية
  final GoogleAdService _adService = GoogleAdService();

  // متغير للتحكم في ظهور الهيدر
  bool _showHeader = true;
  final ScrollController _scrollController = ScrollController();

  List<JobNewsModel> _newsList = [];
  bool _isLoading = true;
  int _offset = 0;
  bool _hasMore = true;
  int _adInsertCounter = 0;
  int _interstitialCounter = 0;
  int _interstitialLoadCount = 0; // عداد تحميل المزيد للإعلان البيني
  List<JobNewsModel> _cachedNews = [];
  bool _isCacheLoaded = false;

  // دالة للتحقق من عمر الكاش
  Future<bool> _isCacheExpired() async {
    final prefs = await SharedPreferences.getInstance();
    final lastUpdateTime = prefs.getInt('news_cache_time');
    if (lastUpdateTime == null) return true;
    
    final now = DateTime.now().millisecondsSinceEpoch;
    final cacheAge = now - lastUpdateTime;
    // 24 ساعة = 24 * 60 * 60 * 1000 مللي ثانية
    return cacheAge > 24 * 60 * 60 * 1000;
  }

  Future<void> _fetchNews({bool refresh = false}) async {
    if (refresh) {
      _offset = 0;
      _newsList.clear();
      _hasMore = true;
      _interstitialLoadCount = 0;
    } else {
      _interstitialLoadCount++;
      if (_interstitialLoadCount >= 3) {
        // تم حذف استدعاء الإعلان البيني لعدم وجود _adService بعد تنظيف الأنظمة القديمة
        // await _adService.showInterstitialAd();
        _interstitialLoadCount = 0;
      }
    }

    if (mounted) {
      setState(() => _isLoading = true);
    }
    try {
      final newItems = await JobNewsApi.fetchPaginated(offset: _offset);
      
      // التحقق من وجود بيانات جديدة
      bool hasNewData = false;
      if (newItems.isNotEmpty) {
        if (_offset == 0) {
          // إذا كان هذا هو التحميل الأول، قارن مع الكاش
          if (_cachedNews.isEmpty || 
              newItems.first.id != _cachedNews.first.id) {
            hasNewData = true;
          }
        } else {
          // إذا كان تحميل المزيد، تحقق من عدم وجود تكرار
          for (var item in newItems) {
            if (!_newsList.any((existing) => existing.id == item.id)) {
              hasNewData = true;
              break;
            }
          }
        }
      }

      if (hasNewData && mounted) {
        setState(() {
          if (refresh) {
            _newsList = newItems;
          } else {
            _newsList.addAll(newItems);
          }
          _isLoading = false;
          _hasMore = newItems.isNotEmpty;
          _offset += newItems.length;
        });

        // تحديث الكاش فقط إذا كانت هناك بيانات جديدة
        if (_offset == 0 || refresh) {
          await _cacheNews(_newsList);
        }
      } else if (mounted) {
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadNewsFromCache() async {
    final prefs = await SharedPreferences.getInstance();
    final cached = prefs.getString('news_cache');
    if (cached != null) {
      final List<dynamic> decoded = json.decode(cached);
      setState(() {
        _cachedNews = decoded.map((e) => JobNewsModel.fromJson(e)).toList();
        _isCacheLoaded = true;
        _newsList = _cachedNews;
      });
      // تحميل البيانات في الخلفية بعد عرض الكاش
      _fetchNews();
    } else {
      // إذا لم يكن هناك كاش، قم بتحميل البيانات مباشرة
      _fetchNews();
    }
  }

  Future<void> _cacheNews(List<JobNewsModel> news) async {
    final prefs = await SharedPreferences.getInstance();
    final encoded = json.encode(news.map((e) => e.toJson()).toList());
    await prefs.setString('news_cache', encoded);
    // حفظ وقت التحديث
    await prefs.setInt('news_cache_time', DateTime.now().millisecondsSinceEpoch);
  }

  Future<void> _clearCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('news_cache');
    await prefs.remove('news_cache_time');
  }

  @override
  void initState() {
    super.initState();
    _loadNewsFromCache();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.grey[50],
      body: Column(
        children: [
          // رأس الصفحة - مع خاصية الإخفاء عند التمرير
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _showHeader ? null : 0,
            child: AnimatedOpacity(
              opacity: _showHeader ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 200),
              child: _buildCompactHeader(),
            ),
          ),

                      // إعلان البانر
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              alignment: Alignment.center,
           //   child: _adService.createBannerAdWidget(),
            ),

          // قائمة أخبار الوظائف
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(isDarkMode ? 51 : 13),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                child: RefreshIndicator(
                  onRefresh: () async {
                    await _loadNewsFromCache();
                  },
                  color: primaryColor,
                  child: _isLoading && _newsList.isEmpty
                      ? const ListShimmerNews()
                      : NotificationListener<ScrollNotification>(
                          onNotification: (scrollInfo) {
                            if (_hasMore && !_isLoading && scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
                              _fetchNews();
                            }
                            return false;
                          },
                          child: ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: _newsList.length + (_hasMore ? 1 : 0) + (_newsList.isEmpty ? 0 : (_newsList.length ~/ 5)),
                            itemBuilder: (context, index) {
                              // منطق إدراج إعلان بانر وسط القائمة كل 5 عناصر
                              int itemIndex = index - (index ~/ 6); // كل سادس عنصر إعلان
                              if (index > 0 && index % 10 == 0) {
                                return const AdJobCard();
                              }
                              if (itemIndex < _newsList.length) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: JobNewsCard(
                                    news: _newsList[itemIndex],
                                    onTap: () async {
                                      if (!mounted) return;
                                      
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => JobNewsShowScreen(
                                            news: _newsList[itemIndex],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              } else if (_hasMore && !_isLoading && index == _newsList.length + (_newsList.isEmpty ? 0 : (_newsList.length ~/ 5))) {
                                // مؤشر تحميل واحد فقط في نهاية القائمة
                                return const Padding(
                                  padding: EdgeInsets.symmetric(vertical: 16),
                                  child: Center(child: CircularProgressIndicator()),
                                );
                              } else {
                                return const SizedBox.shrink();
                              }
                            },
                          ),
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // رأس مبسط للعرض - تصميم محسن
  Widget _buildCompactHeader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDarkMode
              ? [
                  primaryColor,
                  primaryColor.withAlpha(220),
                ]
              : [
                  primaryColor,
                  primaryColor.withAlpha(200),
                ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(isDarkMode ? 51 : 26), // 0.2 = 51, 0.1 = 26
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة محسنة
          Container(
            height: 42,
            width: 42,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(40),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.newspaper_rounded,
              size: 22,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 14),

          // نص محسن
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "أخبار الوظائف",
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  "آخر أخبار الوظائف والفرص المهنية",
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.white.withAlpha(230),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // مراقبة التمرير لإخفاء/إظهار الهيدر
  void _scrollListener() {
    if (_scrollController.offset > 10 && _showHeader) {
      setState(() {
        _showHeader = false;
      });
    } else if (_scrollController.offset <= 10 && !_showHeader) {
      setState(() {
        _showHeader = true;
      });
    }
  }
}

class JobNewsCard extends StatelessWidget {
  final JobNewsModel news;
  final VoidCallback onTap;

  const JobNewsCard({
    super.key,
    required this.news,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: isDarkMode
                ? Theme.of(context).cardTheme.color
                : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withAlpha(51) // 0.2 = 51
                    : Colors.black.withAlpha(13), // 0.05 = 13
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          clipBehavior: Clip.antiAliasWithSaveLayer,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة الخبر من السيرفر
              Stack(
                children: [
                  // خلفية تدرج لوني بدلاً من صورة الخبر
                  Container(
                    height: 160,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: isDarkMode
                            ? [
                                const Color(0xFF232526),
                                const Color(0xFF414345),
                              ]
                            : [
                                const Color(0xFFe0eafc),
                                const Color(0xFFcfdef3),
                              ],
                      ),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.newspaper_rounded,
                        size: 60,
                        color: isDarkMode ? Colors.white.withOpacity(0.18) : Colors.black.withOpacity(0.10),
                      ),
                    ),
                  ),
                  // اللوجو مع فلتر تغميق خلفه
                  if (news.sourceImage.isNotEmpty)
                    Positioned(
                      bottom: 12,
                      left: 12,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: isDarkMode
                                  ? Colors.black.withOpacity(0.5)
                                  : Colors.black.withOpacity(0.18),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 24,
                          backgroundColor: Colors.white,
                          backgroundImage: news.sourceImage.isNotEmpty
                              ? CachedNetworkImageProvider(news.sourceImage)
                              : null,
                          onBackgroundImageError: (error, stackTrace) {},
                        ),
                      ),
                    ),
                  // شريط المصدر
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(230),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(30),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.newspaper_rounded,
                            color: primaryColor,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            "أخبار الوظائف",
                            style: GoogleFonts.tajawal(
                              color: primaryColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // شريط التاريخ والمصدر
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withAlpha(180),
                          ],
                        ),
                      ),
                      child: Row(
                        children: [
                          // التاريخ
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(30),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.access_time,
                                  color: Colors.white,
                                  size: 12,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  news.date,
                                  style: GoogleFonts.tajawal(
                                    color: Colors.white,
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          // المصدر (حل مشكلة overflow)
                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(30),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.business,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 4),
                                  Flexible(
                                    child: Text(
                                      news.source,
                                      style: GoogleFonts.tajawal(
                                        color: Colors.white,
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // عنوان الخبر على الصورة
                  Positioned(
                    bottom: 45,
                    left: 12,
                    right: 12,
                    child: Text(
                      news.title,
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode
                            ? Colors.white
                            : const Color(0xFF333C6D),
                        height: 1.3,
                        shadows: isDarkMode
                            ? [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 3.0,
                                  color: Colors.black.withOpacity(0.7),
                                ),
                              ]
                            : [],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              // محتوى الخبر
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // وصف مختصر للخبر
                    Text(
                      news.description.split('\n').first,
                      style: GoogleFonts.tajawal(
                        fontSize: 13,
                        color: isDarkMode
                            ? Colors.grey[100]
                            : Colors.grey[900],
                        fontWeight: FontWeight.w500,
                        height: 1.4,
                        shadows: [
                          Shadow(
                            offset: const Offset(0, 1),
                            blurRadius: 2.0,
                            color: Colors.black.withOpacity(isDarkMode ? 0.5 : 0.15),
                          ),
                        ],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 12),

                    // زر القراءة
                    Align(
                      alignment: Alignment.centerLeft,
                      child: TextButton.icon(
                        onPressed: onTap,
                        style: TextButton.styleFrom(
                          foregroundColor: primaryColor,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                            side: BorderSide(
                              color: primaryColor.withAlpha(100),
                              width: 1,
                            ),
                          ),
                          backgroundColor: primaryColor.withAlpha(isDarkMode ? 30 : 20),
                        ),
                        icon: const Icon(
                          Icons.remove_red_eye_outlined,
                          size: 16,
                        ),
                        label: Text(
                          "قراءة المزيد",
                          style: GoogleFonts.tajawal(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
