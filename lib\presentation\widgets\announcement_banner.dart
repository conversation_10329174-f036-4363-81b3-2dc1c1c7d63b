﻿import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/core/utils/app_messages.dart' as utils;
import 'package:wzzff/models/app_state_model.dart';

/// مكون لعرض شريط الإعلان
class AnnouncementBanner extends StatelessWidget {
  const AnnouncementBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appStateProvider = Provider.of<AppStateProvider>(context);

    // إضافة debug information
    if (appStateProvider.appState != null) {
    }

    // التحقق من وجود إعلان
    if (!appStateProvider.hasAnnouncement()) {
      return const SizedBox.shrink();
    }

    final announcement = appStateProvider.getAnnouncement();
    if (announcement == null) {
      return const SizedBox.shrink();
    }


    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDarkMode
              ? [
                  Theme.of(context).colorScheme.primary.withOpacity(0.7),
                  Theme.of(context).colorScheme.primary.withOpacity(0.5),
                ]
              : [
                  Theme.of(context).colorScheme.primary.withOpacity(0.8),
                  Theme.of(context).colorScheme.primary.withOpacity(0.6),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _launchAnnouncementLink(context, announcement),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.campaign,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      announcement.title,
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  announcement.body,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                if (announcement.link.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        'اضغط للمزيد',
                        style: GoogleFonts.tajawal(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                      const SizedBox(width: 4),
                      const Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 12,
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// فتح رابط الإعلان
  Future<void> _launchAnnouncementLink(BuildContext context, AppAnnouncement announcement) async {
    if (announcement.link.isEmpty) {
      return;
    }

    final Uri uri = Uri.parse(announcement.link);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        utils.AppMessages.showError('لا يمكن فتح الرابط');
      }
    } catch (e) {
      utils.AppMessages.showError('حدث خطأ أثناء فتح الرابط: $e');
    }
  }
}
