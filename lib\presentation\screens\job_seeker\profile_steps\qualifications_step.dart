import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/SeekerModel.dart';
import 'common_widgets.dart';

class QualificationsStep extends StatefulWidget {
  final SeekerModel user;
  final Function(Map<String, dynamic>) onDataChanged;

  const QualificationsStep({
    super.key,
    required this.user,
    required this.onDataChanged,
  });

  @override
  State<QualificationsStep> createState() => _QualificationsStepState();
}

class _QualificationsStepState extends State<QualificationsStep> {
  final _educationController = TextEditingController();
  final _universityController = TextEditingController();
  final _experienceController = TextEditingController();
  final _skillsController = TextEditingController();
  String? selectedValueEducationLevel;

  List<String> educationLevels = [
    'دكتوراه',
    'ماجستير',
    'بكالوريوس',
    'دبلوم',
    'ثانوية عامة',
    'أخرى'
  ];

  @override
  void initState() {
    super.initState();
    _fillUserData();
  }

  @override
  void dispose() {
    _educationController.dispose();
    _universityController.dispose();
    _experienceController.dispose();
    _skillsController.dispose();
    super.dispose();
  }

  void _fillUserData() {
    if (_educationController.text.isEmpty) {
      _educationController.text = widget.user.education ?? '';
    }
    if (_universityController.text.isEmpty) {
      _universityController.text = widget.user.university ?? '';
    }
    if (_experienceController.text.isEmpty) {
      _experienceController.text = widget.user.experience ?? '';
    }
    if (_skillsController.text.isEmpty) {
      _skillsController.text = widget.user.skills ?? '';
    }
    if (selectedValueEducationLevel == null) {
      selectedValueEducationLevel = widget.user.education_level;
    }
    _updateData();
  }

  void _updateData() {
    widget.onDataChanged({
      "education": _educationController.text,
      "university": _universityController.text,
      "experience": _experienceController.text,
      "skills": _skillsController.text,
      "education_level": selectedValueEducationLevel,
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSectionTitle("المؤهلات والخبرات", Icons.school),
          const SizedBox(height: 24),
          
          buildDropdownField(
            label: "المستوى التعليمي",
            value: selectedValueEducationLevel,
            items: educationLevels,
            onChanged: (value) {
              setState(() {
                selectedValueEducationLevel = value;
                _updateData();
              });
            },
            icon: Icons.school_outlined,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء اختيار المستوى التعليمي";
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          buildInputField(
            controller: _educationController,
            label: "التخصص",
            icon: Icons.book_outlined,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء إدخال التخصص";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),
          
          buildInputField(
            controller: _universityController,
            label: "الجامعة / المؤسسة التعليمية",
            icon: Icons.account_balance_outlined,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "الرجاء إدخال اسم الجامعة";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),
          
          buildInputField(
            controller: _experienceController,
            label: "الخبرات السابقة",
            icon: Icons.work_outline,
            maxLines: 3,
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),
          
          buildInputField(
            controller: _skillsController,
            label: "المهارات",
            icon: Icons.psychology_outlined,
            maxLines: 3,
            helperText: "أدخل المهارات مفصولة بفواصل",
            onChanged: (value) => _updateData(),
          ),
        ],
      ),
    );
  }
}