import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../utils/Constants.dart';
import '../jobs/LoadScreen.dart';
import '../../widgets/job_card.dart';

class FivSeekerJobs extends StatelessWidget {
  const FivSeekerJobs({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text("  الوظائف المفضلة "),
          centerTitle: true,
          backgroundColor: const Color.fromARGB(255, 6, 193, 244),
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(Icons.arrow_forward, color: Colors.white),
              onPressed: () => Navigator.pop(context),
              tooltip: 'رجوع',
            ),
          ],
        ),
        backgroundColor: const Color(0xffF2F2FF),
        body: Directionality(
          textDirection: TextDirection.rtl,
          child: Stack(
            children: [
              Align(
                alignment: Alignment.topCenter,
                child: Container(
                  color: Colors.grey,
                  width: double.infinity,
                  height: 30,
                  child: Center(
                    child: Text(" الوظائف المفضلة ",
                        style: GoogleFonts.tajawal(color: Colors.white)),
                  ),
                ),
              ),
              Positioned(
                top: 30,
                right: 0,
                left: 0,
                bottom: 0,
                child: LoadJobScreen(
                  paddingTop: 0,
                  where: "getmyfavjobs",
                  extraData: const {},
                  showCity: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
