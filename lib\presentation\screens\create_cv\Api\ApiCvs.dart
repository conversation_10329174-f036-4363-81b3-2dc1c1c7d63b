import 'dart:convert';

import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModelSimple.dart';
import 'package:wzzff/presentation/screens/create_cv/Screens/ShowCVScreen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as Http;

import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class ApiCvs {
  Future<List<CvModelSimple>> allCvs({required ltf}) async {
    String url = "https://wassta.net/cvs-api";
    Uri urlConvert = Uri.parse(url);
    Http.Response response =
        await Http.post(urlConvert, body: {"ltf": ltf.toString()});
    Map<String, dynamic> apiData = jsonDecode(response.body);
    List<CvModelSimple> listCvModelSimple = [];

    for (int i = 0; i < apiData["data"].length; i++) {
      if (apiData["data"][i] != null) {
        listCvModelSimple.add(CvModelSimple.fromJson(apiData["data"][i]));
      }
    }
    return listCvModelSimple;
  }

  Future<CvModel> getFields(id) async {
    String url = "https://wassta.net/cvs-api/" + id.toString();
    Uri urlConvert = Uri.parse(url);
    Http.Response response =
        await Http.post(urlConvert, body: {"id": id.toString()});
    Map<String, dynamic> apiDataCv = jsonDecode(response.body);
    return CvModel.fromJson(apiDataCv["data"]);
  }

  Future createCv(Map dataSendMap, context) async {
    dataSendMap.removeWhere((key, item) => item == null);

    //print(dataSendMap);

    String url = "https://wassta.net/cvs-api/create/cv";
    Uri urlConvert = Uri.parse(url);
    Http.Response response = await Http.post(urlConvert, body: dataSendMap);
   // print(response.body);
    if (response.body.contains("DOCTYPE")) {
      Fluttertoast.showToast(
        msg: 'Whoops! There was an error Try Later',
        toastLength: Toast.LENGTH_SHORT,
        fontSize: 18.0,
        gravity: ToastGravity.CENTER,
        backgroundColor: Color.fromARGB(255, 199, 62, 52).withOpacity(0.8),
      );
    } else {
      Map<String, dynamic> reponseData = jsonDecode(response.body);
      if (reponseData["status"] == 0) {
        Fluttertoast.showToast(
          msg: reponseData["message"],
          toastLength: Toast.LENGTH_SHORT,
          fontSize: 18.0,
          gravity: ToastGravity.CENTER,
          backgroundColor: Color.fromARGB(255, 199, 62, 52).withOpacity(0.8),
        );
      }
      Navigator.push(context, MaterialPageRoute(builder: ((context) {
        return ShowCVScreen(file: reponseData["data"]);
      })));
    }
  }
}
