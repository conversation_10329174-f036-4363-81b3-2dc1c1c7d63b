import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class ShowCvFile extends StatefulWidget {
  final String fileCv;
  
  const ShowCvFile({Key? key, required this.fileCv}) : super(key: key);

  @override
  State<ShowCvFile> createState() => _ShowCvFileState();
}

class _ShowCvFileState extends State<ShowCvFile> {
  double countDataReceived = 0;
  bool _isDownloading = false;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _downloadFile() async {
    if (_isDownloading) return;
    
    setState(() {
      _isDownloading = true;
      countDataReceived = 0;
    });
    
    try {
      // إظهار رسالة بدء التحميل مع شريط التقدم
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: _isDownloading 
            ? _buildDownloadProgressIndicator() 
            : const Text('جاري تحميل السيرة الذاتية...'),
          backgroundColor: const Color(0xff2daae2),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 60),
        ),
      );
      
      // تحديد مسار الحفظ
      Directory? directory;
      if (Platform.isAndroid) {
        directory = await getExternalStorageDirectory();
      } else {
        directory = await getApplicationDocumentsDirectory();
      }
      
      // إنشاء اسم الملف
      final fileName = 'cv_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = '${directory!.path}/$fileName';
      
      // تحميل الملف
      await Dio().download(
        widget.fileCv,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              countDataReceived = (received / total * 100);
            });
            
            // تحديث رسالة التحميل
            if (_isDownloading) {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: _buildDownloadProgressIndicator(),
                  backgroundColor: const Color(0xff2daae2),
                  behavior: SnackBarBehavior.floating,
                  duration: const Duration(seconds: 60),
                ),
              );
            }
          }
        }
      );
      
      // إخفاء شريط التقدم الحالي
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      
      // إظهار رسالة نجاح التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تحميل السيرة الذاتية بنجاح إلى: $filePath'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
        ),
      );
    } catch (e) {
      // إخفاء شريط التقدم الحالي
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      
      // إظهار رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تحميل الملف: ${e.toString()}'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }
  
  // بناء مؤشر التقدم الخطي
  Widget _buildDownloadProgressIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'جاري تحميل السيرة الذاتية...',
          style: GoogleFonts.tajawal(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        LinearPercentIndicator(
          lineHeight: 10.0,
          percent: countDataReceived / 100,
          backgroundColor: Colors.white.withOpacity(0.3),
          progressColor: Colors.white,
          barRadius: const Radius.circular(5),
          padding: EdgeInsets.zero,
          animation: true,
          animateFromLastPercent: true,
          center: Text(
            "${countDataReceived.toStringAsFixed(0)}%",
            style: GoogleFonts.tajawal(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xff2daae2),
        elevation: 0,
        title: Text(
          'السيرة الذاتية',
          style: GoogleFonts.tajawal(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.arrow_forward, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
            tooltip: 'رجوع',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xff2daae2).withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: PDF(
          enableSwipe: true,
          swipeHorizontal: false,
          autoSpacing: true,
          pageFling: true,
        ).fromUrl(
          widget.fileCv,
          placeholder: (progress) => Center(
            child: Container(
              padding: const EdgeInsets.all(25),
              width: MediaQuery.of(context).size.width * 0.8,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 15,
                    spreadRadius: 2,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.picture_as_pdf_rounded,
                    size: 50,
                    color: const Color(0xff2daae2),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    "جاري تحميل السيرة الذاتية",
                    style: GoogleFonts.tajawal(
                      color: const Color(0xff2daae2),
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 25),
                  LinearPercentIndicator(
                    lineHeight: 12.0,
                    width: MediaQuery.of(context).size.width * 0.7,
                    percent: progress == null ? 0 : progress / 100,
                    backgroundColor: Colors.grey.withOpacity(0.2),
                    progressColor: const Color(0xff2daae2),
                    barRadius: const Radius.circular(10),
                    padding: EdgeInsets.zero,
                    animation: true,
                    animateFromLastPercent: true,
                    center: Text(
                      progress == null ? "0%" : "${progress.toStringAsFixed(0)}%",
                      style: GoogleFonts.tajawal(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          errorWidget: (error) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 50),
                const SizedBox(height: 10),
                Text(
                  'حدث خطأ أثناء تحميل الملف: $error',
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color(0xff2daae2),
        child: const Icon(Icons.refresh, color: Colors.white),
        onPressed: () {
          setState(() {});
        },
      ),
    );
  }
}
