import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:list_load_more/list_load_more/list_load_more.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/presentation/components/ListShimmer.dart';
import 'package:wzzff/presentation/widgets/job_card.dart';
import 'LoadScreen.dart';

class JobsByCountries extends StatefulWidget {
  final String country;

  const JobsByCountries({super.key, required this.country});

  @override
  State<JobsByCountries> createState() => _JobsByCountriesState();
}

class _JobsByCountriesState extends State<JobsByCountries> {
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: isDarkMode
                  ? [primaryColor.withOpacity(0.85), const Color(0xFF232B3E)]
                  : [primaryColor, const Color(0xFF2196F3)],
            ),
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
            ),
            onPressed: () => Navigator.pop(context),
            tooltip: 'رجوع',
          ),
        ],
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: isDarkMode
                      ? [primaryColor.withOpacity(0.13), Colors.transparent]
                      : [primaryColor.withOpacity(0.10), Colors.white],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "استكشف أحدث الوظائف المتاحة في هذه ${widget.country}",
                    style: GoogleFonts.tajawal(
                      fontSize: 14,
                      color: isDarkMode
                          ? Colors.white.withOpacity(0.85)
                          : primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.transparent : Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: LoadJobScreen(
                  paddingTop: 0,
                  where: "countryByName",
                  extraData: {"country": widget.country},
                  showCity: true,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
