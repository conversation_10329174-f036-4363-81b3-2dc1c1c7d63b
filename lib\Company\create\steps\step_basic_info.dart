import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/job_seeker/profile_steps/common_widgets.dart';
import 'package:wzzff/models/SpinnerDataModel.dart';
import 'job_form_data.dart';

class StepBasicInfo extends StatefulWidget {
  final JobFormData formData;
  final Function(String) onCountryChanged;

  const StepBasicInfo({
    Key? key,
    required this.formData,
    required this.onCountryChanged,
  }) : super(key: key);

  @override
  _StepBasicInfoState createState() => _StepBasicInfoState();
}

class _StepBasicInfoState extends State<StepBasicInfo> {
  // Theme helpers
  bool get _isDarkMode => Theme.of(context).brightness == Brightness.dark;
  Color get _primaryColor => Theme.of(context).colorScheme.primary;
  Color get _surfaceColor => _isDarkMode ? Colors.grey[800]! : Colors.grey[50]!;
  Color get _cardColor => _isDarkMode ? Colors.grey[850]! : Colors.white;
  Color get _textColor => _isDarkMode ? Colors.white : Colors.black87;
  Color get _secondaryTextColor => _isDarkMode ? Colors.grey[300]! : Colors.grey[600]!;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _isDarkMode ? Colors.black.withOpacity(0.3) : Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: _primaryColor, size: 18),
              const SizedBox(width: 8),
              Text(
                'معلومات الوظيفة الأساسية',
                style: GoogleFonts.tajawal(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          buildInputField(
            controller: widget.formData.titleController,
            label: 'عنوان الوظيفة',
            icon: Icons.work,
            isRequired: true,
            validator: (v) => v == null || v.isEmpty ? 'يرجى إدخال عنوان الوظيفة' : null,
          ),
          const SizedBox(height: 12),
          buildDropdownField(
            label: 'نوع الوظيفة',
            value: widget.formData.selectedJobType,
            items: widget.formData.jobTypes,
            onChanged: (val) => setState(() => widget.formData.selectedJobType = val),
            icon: Icons.schedule,
            isRequired: true,
            validator: (v) => v == null ? 'يرجى اختيار نوع الوظيفة' : null,
          ),
          const SizedBox(height: 12),
          buildDropdownField(
            label: 'تصنيف الوظيفة',
            value: widget.formData.selectedCategory,
            items: widget.formData.categories,
            onChanged: (val) => setState(() => widget.formData.selectedCategory = val),
            icon: Icons.category,
            isRequired: true,
            validator: (v) => v == null ? 'يرجى اختيار تصنيف الوظيفة' : null,
          ),
          const SizedBox(height: 12),
          _buildCountryDropdown(),
          const SizedBox(height: 12),
          _buildCityDropdown(),
        ],
      ),
    );
  }

  Widget _buildCountryDropdown() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: _primaryColor.withOpacity(0.3)),
        color: _surfaceColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
            child: Row(
              children: [
                Icon(Icons.public, color: _primaryColor, size: 16),
                const SizedBox(width: 6),
                Text('الدولة *', style: GoogleFonts.tajawal(
                  fontSize: 12, 
                  fontWeight: FontWeight.w600,
                  color: _textColor,
                )),
              ],
            ),
          ),
          DropdownButtonHideUnderline(
            child: DropdownButton<SpinnerDataModel>(
              isExpanded: true,
              hint: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Text('اختر الدولة', style: GoogleFonts.tajawal(color: _secondaryTextColor)),
              ),
              value: widget.formData.selectedCountry,
              items: widget.formData.countries.map((country) {
                return DropdownMenuItem<SpinnerDataModel>(
                  value: country,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Text(country.name, style: GoogleFonts.tajawal(
                      fontSize: 12,
                      color: _textColor,
                    )),
                  ),
                );
              }).toList(),
              onChanged: (country) {
                if (country != null) {
                  setState(() {
                    widget.formData.selectedCountry = country;
                    widget.formData.selectedCity = null;
                  });
                  widget.onCountryChanged(country.name);
                }
              },
              icon: Padding(
                padding: const EdgeInsets.only(right: 12),
                child: Icon(Icons.arrow_drop_down, color: _primaryColor),
              ),
              dropdownColor: _cardColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCityDropdown() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: _primaryColor.withOpacity(0.3)),
        color: _surfaceColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 12, right: 12, top: 8),
            child: Row(
              children: [
                Icon(Icons.location_city, color: _primaryColor, size: 16),
                const SizedBox(width: 6),
                Text('المدينة *', style: GoogleFonts.tajawal(
                  fontSize: 12, 
                  fontWeight: FontWeight.w600,
                  color: _textColor,
                )),
              ],
            ),
          ),
          widget.formData.isCitiesLoading
              ? Container(
                  height: 40,
                  child: Center(child: CircularProgressIndicator(color: _primaryColor)),
                )
              : DropdownButtonHideUnderline(
                  child: DropdownButton<SpinnerDataModel>(
                    isExpanded: true,
                    hint: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text('اختر المدينة', style: GoogleFonts.tajawal(color: _secondaryTextColor)),
                    ),
                    value: widget.formData.selectedCity,
                    items: widget.formData.cities.map((city) {
                      return DropdownMenuItem<SpinnerDataModel>(
                        value: city,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Text(city.name, style: GoogleFonts.tajawal(
                            fontSize: 12,
                            color: _textColor,
                          )),
                        ),
                      );
                    }).toList(),
                    onChanged: (city) {
                      setState(() {
                        widget.formData.selectedCity = city;
                      });
                    },
                    icon: Padding(
                      padding: const EdgeInsets.only(right: 12),
                      child: Icon(Icons.arrow_drop_down, color: _primaryColor),
                    ),
                    dropdownColor: _cardColor,
                  ),
                ),
        ],
      ),
    );
  }
} 