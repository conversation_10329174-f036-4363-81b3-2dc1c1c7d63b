﻿import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:file_selector/file_selector.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import '../../../Apis/ProfileApi.dart';
import '../../../core/utils/app_messages.dart';
import 'package:wzzff/presentation/screens/job_seeker/ShowCvFile.dart';

class UploadSeekerCv extends StatefulWidget {
  const UploadSeekerCv({super.key});

  @override
  State<UploadSeekerCv> createState() => _UploadSeekerCvState();
}

class _UploadSeekerCvState extends State<UploadSeekerCv> {
  bool showLodingUploadCv = false;
  bool hasCv = false;
  String? cvFileName;
  DateTime? lastUploadDate;

  @override
  void initState() {
    super.initState();
    _checkExistingCv();
  }

  Future<void> _checkExistingCv() async {
    try {
      String? cvUrl = await ProfileApi().getMyCv();
      setState(() {
        hasCv = cvUrl != null;
        if (hasCv) {
          cvFileName = "السيرة_الذاتية.pdf";
          lastUploadDate = DateTime.now(); // يمكن استبداله بتاريخ حقيقي من الخادم
        }
      });
    } catch (e) {
      _showErrorToast("حدث خطأ أثناء التحقق من السيرة الذاتية");
    }
  }

  void _showErrorToast(String message) {
    AppMessages.showError(message);
  }

  void _showSuccessToast(String message) {
    AppMessages.showSuccess(message);
  }

  Future<void> _showLoadingDialog({
    required BuildContext context,
    required String message,
    required String loadingText,
    required String successText,
    required Future<void> Function() action,
  }) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 20),
              Text(
                loadingText,
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );

    try {
      await action();
      Navigator.pop(context); // Close loading dialog
      _showSuccessToast(successText);
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : Colors.white,
        appBar: AppBar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          centerTitle: true,
          elevation: 0,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(
                Icons.arrow_forward,
                color: Colors.white,
              ),
              onPressed: () => Navigator.pop(context),
              tooltip: 'رجوع',
            ),
          ],
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Theme.of(context).colorScheme.primary,
            statusBarIconBrightness: Brightness.light,
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withOpacity(0.8),
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
              ],
              stops: const [0.0, 0.1, 0.3],
            ),
          ),
          child: Column(
            children: [
              _buildHeaderSection(),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 20),
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).scaffoldBackgroundColor
                        : Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(
                            Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 30),
                        _buildCvTipsSection(),
                        const SizedBox(height: 30),
                        _buildCvStatusCard(),
                        const SizedBox(height: 30),
                        _buildUploadSection(),
                        const SizedBox(height: 20),
                        _buildViewSection(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      child: Column(
        children: [
          Text(
            hasCv ? "تم رفع السيرة الذاتية" : "لم يتم رفع السيرة الذاتية بعد",
            style: GoogleFonts.tajawal(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 5),
          Text(
            hasCv
                ? "يمكنك عرض أو تحديث السيرة الذاتية"
                : "قم برفع سيرتك الذاتية لزيادة فرص التوظيف",
            style: GoogleFonts.tajawal(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCvStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).cardTheme.color
            : Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(
                Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: hasCv ? const Color(0xFF4CAF50).withOpacity(0.3) : Colors.grey.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: hasCv ? const Color(0xFF4CAF50).withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  hasCv ? Icons.check_circle : Icons.info_outline,
                  color: hasCv ? const Color(0xFF4CAF50) : Colors.grey,
                  size: 30,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      hasCv ? "السيرة الذاتية جاهزة" : "لا توجد سيرة ذاتية",
                      style: GoogleFonts.tajawal(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: hasCv ? const Color(0xFF4CAF50) : Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      hasCv
                          ? "تم رفع السيرة الذاتية بنجاح"
                          : "قم برفع سيرتك الذاتية لزيادة فرص التوظيف",
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyMedium?.color
                            : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (hasCv) ...[
            const SizedBox(height: 15),
            const Divider(),
            const SizedBox(height: 10),
            Row(
              children: [
                Icon(
                  Icons.insert_drive_file,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 10),
                Text(
                  cvFileName ?? "السيرة_الذاتية.pdf",
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).textTheme.bodyMedium?.color
                        : Colors.grey[700],
                  ),
                ),
                const Spacer(),
                Text(
                  "تم التحديث: ${_formatDate(lastUploadDate)}",
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).textTheme.bodySmall?.color
                        : Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return "غير معروف";
    return "${date.day}/${date.month}/${date.year}";
  }

  Widget _buildUploadSection() {
    return GestureDetector(
      onTap: () async {
        if (showLodingUploadCv) return;

        try {
          // تحديد نوع الملف المطلوب (PDF)
          const XTypeGroup typeGroup = XTypeGroup(
            label: 'PDFs',
            extensions: <String>['pdf'],
          );

          // فتح نافذة اختيار الملف
          final XFile? file = await openFile(
            acceptedTypeGroups: <XTypeGroup>[typeGroup],
          );

          if (file != null) {
            // تحويل XFile إلى File
            final File pdfFile = File(file.path);
            final String fileName = file.name;

            _showLoadingDialog(
              context: context,
              message: "جاري رفع السيرة الذاتية",
              loadingText: "جاري رفع الملف...",
              successText: "تم رفع ملف السيرة الذاتية بنجاح",
              action: () async {
                bool success = await LoginAndCheckAndRegi.uploadCv(pdfFile);
                
                if (success) {
                  setState(() {
                    hasCv = true;
                    cvFileName = fileName;
                    lastUploadDate = DateTime.now();
                  });
                } else {
                  throw Exception("فشل رفع الملف");
                }
              },
            );
          } else {
            AppMessages.showWarning("لم تختر ملف سيرة ذاتية");
          }
        } catch (e) {
          AppMessages.showError("حدث خطأ أثناء اختيار الملف: ${e.toString()}");
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).cardTheme.color
              : Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(
                  Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: showLodingUploadCv
            ? Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 10),
                    Text(
                      "جاري رفع الملف...",
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyMedium?.color
                            : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(
                          Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.upload_file,
                      color: Theme.of(context).colorScheme.primary,
                      size: 30,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Text(
                    hasCv ? "تحديث السيرة الذاتية" : "رفع السيرة الذاتية",
                    style: GoogleFonts.tajawal(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    "اضغط هنا لاختيار ملف PDF",
                    style: GoogleFonts.tajawal(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).textTheme.bodyMedium?.color
                          : Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 15),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      "اختيار ملف",
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildViewSection() {
    return GestureDetector(
      onTap: () async {
        if (!hasCv) {
          _showErrorToast("لا توجد سيرة ذاتية لعرضها");
          return;
        }

        String? getMycvFromServerOrNull = await ProfileApi().getMyCv();
        if (getMycvFromServerOrNull != null) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: ((context) {
                return ShowCvFile(
                  fileCv: getMycvFromServerOrNull,
                );
              }),
            ),
          );
        } else {
          _showErrorToast("لا توجد سيرة ذاتية لعرضها");
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).cardTheme.color
              : Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(
                  Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey.withOpacity(0.3)
                : const Color(0xFF043955).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.blueGrey.withOpacity(0.2)
                    : const Color(0xFF043955).withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.visibility,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.blueGrey[200]
                    : const Color(0xFF043955),
                size: 24,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "عرض السيرة الذاتية",
                    style: GoogleFonts.tajawal(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).textTheme.titleMedium?.color
                          : const Color(0xFF043955),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    "اضغط هنا لعرض وتحميل السيرة الذاتية",
                    style: GoogleFonts.tajawal(
                      fontSize: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).textTheme.bodyMedium?.color
                          : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_back_ios,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).textTheme.bodyMedium?.color
                  : const Color(0xFF043955),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCvTipsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).cardTheme.color?.withOpacity(0.5)
            : const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb_outline,
                color: Color(0xFFFF9800),
                size: 24,
              ),
              const SizedBox(width: 10),
              Text(
                "نصائح لسيرة ذاتية مميزة",
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).textTheme.titleMedium?.color
                      : const Color(0xFF333333),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          _buildTipItem("استخدم تنسيق واضح ومقروء"),
          _buildTipItem("أبرز مهاراتك وإنجازاتك"),
          _buildTipItem("تأكد من خلو السيرة الذاتية من الأخطاء اللغوية"),
          _buildTipItem("قم بتحديث سيرتك الذاتية بشكل دوري"),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check_circle,
            color: Color(0xFF4CAF50),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
