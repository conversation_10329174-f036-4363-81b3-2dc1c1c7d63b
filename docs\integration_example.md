# مثال التطبيق العملي للبحث الذكي

## تحديث SearchScreen.dart

```dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/SpinnerDataModel.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/presentation/components/ListShimmer.dart';
import 'package:wzzff/presentation/widgets/custom_theme.dart';
import 'package:wzzff/presentation/widgets/announcement_banner.dart';
import 'package:wzzff/presentation/widgets/smart_search_widget.dart'; // جديد
import 'package:wzzff/presentation/screens/smart_results_screen.dart'; // جديد
import 'package:wzzff/models/search_suggestion_model.dart'; // جديد

class SearchScreen extends StatefulWidget {
  SearchScreen({super.key});
  List<SpinnerDataModel>? cites;
  List<SpinnerDataModel>? countries;
  int spinnerId = 0;
  String? countryValue;

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  bool isLoading = false;
  String searchKeyword = "";
  SpinnerDataModel? selectedCity;
  
  // إضافة متحكم للبحث الذكي
  final TextEditingController _smartSearchController = TextEditingController();
  bool _useSmartSearch = true; // تبديل بين البحث التقليدي والذكي

  @override
  void initState() {
    super.initState();
    getCountries();
    widget.countryValue = "السعودية";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: Theme.of(context).brightness == Brightness.dark
                ? [
                    Theme.of(context).scaffoldBackgroundColor,
                    Theme.of(context).scaffoldBackgroundColor,
                  ]
                : [
                    const Color(0xFFF5F7FA),
                    Colors.white,
                  ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              const AnnouncementBanner(),
              Expanded(
                child: _buildSearchForm(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // تبديل بين البحث التقليدي والذكي
          _buildSearchModeToggle(),
          const SizedBox(height: 20),
          
          // حقل البحث (ذكي أو تقليدي)
          _useSmartSearch ? _buildSmartSearchField() : _buildTraditionalSearchForm(),
          
          const SizedBox(height: 24),
          
          // نصائح البحث
          if (!_useSmartSearch) _buildSearchTips(),
        ],
      ),
    );
  }

  // تبديل نمط البحث
  Widget _buildSearchModeToggle() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF2D2D2D)
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _useSmartSearch = true),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _useSmartSearch 
                      ? Theme.of(context).colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      size: 18,
                      color: _useSmartSearch 
                          ? Colors.white 
                          : Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'بحث ذكي',
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: _useSmartSearch 
                            ? Colors.white 
                            : Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _useSmartSearch = false),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: !_useSmartSearch 
                      ? Theme.of(context).colorScheme.primary
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.search,
                      size: 18,
                      color: !_useSmartSearch 
                          ? Colors.white 
                          : Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'بحث تقليدي',
                      style: GoogleFonts.tajawal(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: !_useSmartSearch 
                            ? Colors.white 
                            : Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // حقل البحث الذكي الجديد
  Widget _buildSmartSearchField() {
    return Column(
      children: [
        // وصف المميزات
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.psychology,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'البحث الذكي المطور',
                      style: GoogleFonts.tajawal(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildFeatureItem('🎯', 'اقتراحات ذكية فورية'),
              _buildFeatureItem('🔍', 'بحث في المرادفات والمصطلحات المشابهة'),
              _buildFeatureItem('📊', 'ترتيب النتائج حسب درجة التطابق'),
              _buildFeatureItem('🧠', 'تعلم من سلوك البحث السابق'),
            ],
          ),
        ),
        const SizedBox(height: 20),
        
        // مكون البحث الذكي
        SmartSearchWidget(
          controller: _smartSearchController,
          hintText: 'ابحث بذكاء عن وظائف... (مثل: مطور Flutter، مهندس شبكات)',
          onSearch: _performSmartSearch,
          onSuggestionSelected: _onSmartSuggestionSelected,
          showSuggestions: true,
        ),
        
        const SizedBox(height: 16),
        
        // خيارات إضافية للبحث الذكي
        _buildSmartSearchOptions(),
      ],
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.tajawal(
                fontSize: 13,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : const Color(0xFF555555),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmartSearchOptions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF2D2D2D)
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'خيارات إضافية',
            style: GoogleFonts.tajawal(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // اختيار الدولة
          if (widget.countries != null) _buildCountryDropdown(),
          
          const SizedBox(height: 12),
          
          // اختيار المدينة
          if (widget.cites != null) _buildCityDropdown(),
        ],
      ),
    );
  }

  // البحث الذكي
  void _performSmartSearch(String query) {
    if (query.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى إدخال كلمة البحث',
            style: GoogleFonts.tajawal(),
            textAlign: TextAlign.center,
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SmartResultsScreen(
          initialQuery: query,
          country: widget.countryValue ?? 'السعودية',
          city: selectedCity?.name,
        ),
      ),
    );
  }

  // معالجة اختيار الاقتراح الذكي
  void _onSmartSuggestionSelected(SearchSuggestion suggestion) {
    // يمكن إضافة تحليلات هنا لتتبع الاقتراحات المختارة
    _performSmartSearch(suggestion.text);
  }

  // النموذج التقليدي (الحالي)
  Widget _buildTraditionalSearchForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF2D2D2D)
            : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'معلومات البحث',
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).textTheme.titleLarge?.color
                      : const Color(0xFF333333),
                ),
              ),
              const SizedBox(height: 16),
              _buildSearchField(),
              const SizedBox(height: 16),
              widget.countries != null
                  ? _buildCountryDropdown()
                  : _buildCountrySelector(),
              const SizedBox(height: 16),
              widget.cites != null
                  ? _buildCityDropdown()
                  : (isLoading && widget.cites == null
                      ? _buildLoadingCities()
                      : const SizedBox()),
              const SizedBox(height: 24),
              _buildTraditionalSearchButton(),
            ],
          ),
        ),
      ),
    );
  }

  // باقي الدوال المطلوبة...
  List<SpinnerDataModel>? getCountries() {
    widget.countries = [
      SpinnerDataModel(name: 'السعودية', id: 1),
      SpinnerDataModel(name: 'مصر', id: 2),
      SpinnerDataModel(name: 'الامارات', id: 3),
      SpinnerDataModel(name: 'الأردن', id: 4),
      SpinnerDataModel(name: 'البحرين', id: 5),
      SpinnerDataModel(name: 'الكويت', id: 6),
      SpinnerDataModel(name: 'قطر', id: 7),
      SpinnerDataModel(name: 'عمان', id: 8),
      SpinnerDataModel(name: 'العراق', id: 9),
      SpinnerDataModel(name: 'الجزائر', id: 10),
      SpinnerDataModel(name: 'المغرب', id: 11),
      SpinnerDataModel(name: 'تونس', id: 12),
      SpinnerDataModel(name: 'لبنان', id: 13),
      SpinnerDataModel(name: 'سوريا', id: 14),
      SpinnerDataModel(name: 'السودان', id: 15),
      SpinnerDataModel(name: 'ليبيا', id: 16),
      SpinnerDataModel(name: 'فلسطين', id: 17),
      SpinnerDataModel(name: 'اليمن', id: 18),
    ];
    return widget.countries;
  }

  // إضافة باقي الدوال المطلوبة من الكود الأصلي...
  Widget _buildSearchField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'كلمة البحث',
          style: GoogleFonts.tajawal(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).textTheme.titleMedium?.color
                : const Color(0xFF555555),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: searchKeyword,
          onChanged: (value) {
            setState(() {
              searchKeyword = value;
            });
          },
          style: GoogleFonts.tajawal(
            fontSize: 14,
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).textTheme.bodyMedium?.color
                : const Color(0xFF333333),
          ),
          decoration: InputDecoration(
            hintText: 'أدخل كلمة البحث (مثل: مهندس، محاسب، مطور)',
            hintTextDirection: TextDirection.rtl,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.withOpacity(0.3)
                    : const Color(0xFFE0E0E0),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.withOpacity(0.3)
                    : const Color(0xFFE0E0E0),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCountryDropdown() {
    // تطبيق من الكود الأصلي
    return Container(); // ملء هذا بالكود الأصلي
  }

  Widget _buildCityDropdown() {
    // تطبيق من الكود الأصلي
    return Container(); // ملء هذا بالكود الأصلي
  }

  Widget _buildCountrySelector() {
    // تطبيق من الكود الأصلي
    return Container(); // ملء هذا بالكود الأصلي
  }

  Widget _buildLoadingCities() {
    // تطبيق من الكود الأصلي
    return Container(); // ملء هذا بالكود الأصلي
  }

  Widget _buildTraditionalSearchButton() {
    // تطبيق من الكود الأصلي مع تعديل للانتقال للنتائج التقليدية
    return Container(); // ملء هذا بالكود الأصلي
  }

  Widget _buildSearchTips() {
    // تطبيق من الكود الأصلي
    return Container(); // ملء هذا بالكود الأصلي
  }
}
```

## إضافة البحث السريع في الصفحة الرئيسية

```dart
// في home_screen.dart أو main.dart
Widget _buildQuickSmartSearch() {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    child: SmartSearchWidget(
      hintText: 'بحث سريع ذكي...',
      showSuggestions: true,
      padding: EdgeInsets.zero,
      onSearch: (query) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SmartResultsScreen(
              initialQuery: query,
            ),
          ),
        );
      },
    ),
  );
}
```

## تحديث شريط التطبيق العلوي

```dart
// إضافة أيقونة البحث الذكي في AppBar
AppBar(
  title: Text('وظف'),
  actions: [
    IconButton(
      icon: Icon(Icons.auto_awesome),
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SearchScreen(), // مع البحث الذكي
          ),
        );
      },
      tooltip: 'البحث الذكي',
    ),
    // باقي الأيقونات...
  ],
)
```

## تحليلات الاستخدام

```dart
// إضافة تتبع استخدام البحث الذكي
class SearchAnalytics {
  static void trackSmartSearch(String query, int resultsCount) {
    // إرسال إحصائيات لـ Firebase Analytics أو أي نظام تحليلات
    print('Smart search: $query, Results: $resultsCount');
  }

  static void trackSuggestionSelected(SearchSuggestion suggestion) {
    // تتبع الاقتراحات المختارة
    print('Suggestion selected: ${suggestion.text}, Type: ${suggestion.type}');
  }

  static void trackMatchScoreDistribution(List<AIMatchResult> results) {
    // تحليل توزيع درجات التطابق
    final scores = results.map((r) => r.matchScore).toList();
    final averageScore = scores.reduce((a, b) => a + b) / scores.length;
    print('Average match score: $averageScore');
  }
}
``` 