import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/SpinnerDataModel.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/presentation/components/ListShimmer.dart';
import 'package:wzzff/presentation/widgets/custom_theme.dart';
import 'package:wzzff/presentation/screens/jobs/resultsScreen.dart';
import 'package:wzzff/presentation/widgets/announcement_banner.dart';

class SearchScreen extends StatefulWidget {
  SearchScreen({super.key});
  List<SpinnerDataModel>? cites;
  List<SpinnerDataModel>? countries;
  int spinnerId = 0;
  String? countryValue;

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  bool isLoading = false;
  String searchKeyword = "";
  SpinnerDataModel? selectedCity;

  @override
  void initState() {
    super.initState();
    getCountries();
    widget.countryValue = "السعودية";
  }

  List<SpinnerDataModel>? getCountries() {
    widget.countries = [
      SpinnerDataModel(name: 'السعودية', id: 1),
      SpinnerDataModel(name: 'مصر', id: 2),
      SpinnerDataModel(name: 'الامارات', id: 3),
      SpinnerDataModel(name: 'الأردن', id: 4),
      SpinnerDataModel(name: 'البحرين', id: 5),
      SpinnerDataModel(name: 'الكويت', id: 6),
      SpinnerDataModel(name: 'قطر', id: 7),
      SpinnerDataModel(name: 'عمان', id: 8),
      SpinnerDataModel(name: 'العراق', id: 9),
      SpinnerDataModel(name: 'الجزائر', id: 10),
      SpinnerDataModel(name: 'المغرب', id: 11),
      SpinnerDataModel(name: 'تونس', id: 12),
      SpinnerDataModel(name: 'لبنان', id: 13),
      SpinnerDataModel(name: 'سوريا', id: 14),
      SpinnerDataModel(name: 'السودان', id: 15),
      SpinnerDataModel(name: 'ليبيا', id: 16),
      SpinnerDataModel(name: 'فلسطين', id: 17),
      SpinnerDataModel(name: 'اليمن', id: 18),
    ];
    return widget.countries;
  }

  Future<List<SpinnerDataModel>?> getCitiesToFirstCountry() async {
    if (!mounted) return null;

    setState(() {
      isLoading = true;
    });

    widget.cites = await JobsApi().getCitesApi(country: "السعودية").whenComplete(() {
      if (mounted) {
        setState(() {
          widget.cites = widget.cites;
          isLoading = false;
        });
      }
    });

    return widget.cites;
  }

  Future<List<SpinnerDataModel>?> getCityByCountry(String? country) async {
    if (!mounted) return null;

    setState(() {
      isLoading = true;
    });

    widget.cites = await JobsApi().getCitesApi(country: country.toString()).whenComplete(() {
      if (mounted) {
        setState(() {
          widget.cites = widget.cites;
          isLoading = false;
        });
      }
    });
    return widget.cites;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: Theme.of(context).brightness == Brightness.dark
                ? [
                    Theme.of(context).scaffoldBackgroundColor,
                    Theme.of(context).scaffoldBackgroundColor,
                  ]
                : [
                    const Color(0xFFF5F7FA),
                    Colors.white,
                  ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              const AnnouncementBanner(),
              Expanded(
                child: _buildSearchForm(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        children: [
          _buildSearchTips(),
          const SizedBox(height: 24),
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).cardTheme.color
                  : Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withOpacity(0.2)
                      : Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Directionality(
              textDirection: TextDirection.rtl,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات البحث',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.titleLarge?.color
                            : const Color(0xFF333333),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildSearchField(),
                    const SizedBox(height: 16),
                    widget.countries != null
                        ? _buildCountryDropdown()
                        : _buildCountrySelector(),
                    const SizedBox(height: 16),
                    widget.cites != null
                        ? _buildCityDropdown()
                        : (isLoading && widget.cites == null
                            ? _buildLoadingCities()
                            : const SizedBox()),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          _buildSearchButton(),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'كلمة البحث',
          style: GoogleFonts.tajawal(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          onChanged: (value) {
            setState(() {
              searchKeyword = value;
            });
          },
          style: GoogleFonts.tajawal(
            fontSize: 14,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.black87,
          ),
          decoration: InputDecoration(
            hintText: 'ابحث عن وظيفة أو مجال عمل...',
            hintStyle: GoogleFonts.tajawal(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            filled: true,
            fillColor: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[800]
                : Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            prefixIcon: Icon(
              Icons.search,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCountryDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الدولة',
          style: GoogleFonts.tajawal(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: widget.countryValue,
          decoration: InputDecoration(
            filled: true,
            fillColor: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[800]
                : Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          items: widget.countries!.map((country) {
            return DropdownMenuItem<String>(
              value: country.name,
              child: Text(
                country.name,
                style: GoogleFonts.tajawal(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: (value) async {
            setState(() {
              widget.countryValue = value;
              selectedCity = null;
            });
            await getCityByCountry(value);
          },
        ),
      ],
    );
  }

  Widget _buildCountrySelector() {
    return ElevatedButton(
      onPressed: () async {
        await getCitiesToFirstCountry();
      },
      child: Text(
        'عرض المدن',
        style: GoogleFonts.tajawal(),
      ),
    );
  }

  Widget _buildCityDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المدينة (اختياري)',
          style: GoogleFonts.tajawal(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white
                : Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<SpinnerDataModel>(
          value: selectedCity,
          decoration: InputDecoration(
            hintText: 'اختر المدينة',
            hintStyle: GoogleFonts.tajawal(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            filled: true,
            fillColor: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[800]
                : Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          items: widget.cites!.map((city) {
            return DropdownMenuItem<SpinnerDataModel>(
              value: city,
              child: Text(
                city.name,
                style: GoogleFonts.tajawal(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              selectedCity = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildLoadingCities() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[800]
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildSearchButton() {
    final primaryColor = Theme.of(context).colorScheme.primary;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        onPressed: () async {
          if (searchKeyword.isEmpty) {
            if (!mounted) return;

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'يرجى إدخال كلمة البحث',
                  style: GoogleFonts.tajawal(),
                  textAlign: TextAlign.center,
                ),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }

          if (widget.countryValue == null) {
            if (!mounted) return;

            setState(() {
              widget.countryValue = 'السعودية';
            });
          }

          if (!mounted) return;

          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResultsScreen(
                word: searchKeyword,
                country: widget.countryValue!,
                city: selectedCity?.name,
              ),
            ),
          );

          if (mounted) {
            setState(() {
              // يمكن إضافة أي تحديثات ضرورية هنا
            });
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.search_rounded, size: 24),
            const SizedBox(width: 12),
            Text(
              'بحث عن وظائف',
              style: GoogleFonts.tajawal(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchTips() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            textDirection: TextDirection.rtl,
            children: [
              const Icon(
                Icons.lightbulb_outline,
                color: Color(0xFFFFA000),
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'نصائح البحث',
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode
                      ? Theme.of(context).textTheme.titleMedium?.color
                      : const Color(0xFF333333),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTipItem('استخدم كلمات مفتاحية محددة مثل "مهندس برمجيات" أو "محاسب"'),
          _buildTipItem('حدد الدولة والمدينة للحصول على نتائج أكثر دقة'),
          _buildTipItem('جرب البحث بمسميات وظيفية مختلفة للحصول على المزيد من الفرص'),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: TextDirection.rtl,
        children: [
          Text(
            '•',
            style: TextStyle(
              fontSize: 14,
              color: primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.tajawal(
                fontSize: 13,
                color: isDarkMode
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : const Color(0xFF555555),
              ),
              textDirection: TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }
} 