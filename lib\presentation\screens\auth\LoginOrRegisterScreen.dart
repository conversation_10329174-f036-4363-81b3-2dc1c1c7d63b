import 'package:flutter/material.dart';
// import 'package:flutter/services.dart'; // غير مستخدم
// import 'package:fluttertoast/fluttertoast.dart'; // غير مستخدم
import 'package:google_fonts/google_fonts.dart';
// import 'package:url_launcher/url_launcher.dart'; // غير مستخدم
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import 'package:wzzff/presentation/screens/auth/Registeration_page.dart';
import 'package:wzzff/presentation/screens/auth/LoginScreenPage.dart'; // معطل مؤقتاً
import '../job_seeker/SeekerScreenProfile.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/Company/Company/company_login_page.dart';
import 'package:wzzff/Company/Company/register_company_page.dart';

class LoginOrRegisterScreen extends StatefulWidget {
  const LoginOrRegisterScreen({super.key});

  @override
  State<LoginOrRegisterScreen> createState() => _LoginOrRegisterScreenState();
}

class _LoginOrRegisterScreenState extends State<LoginOrRegisterScreen> with SingleTickerProviderStateMixin {
  bool? company;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: Future.value(false), // معطل مؤقتاً
      builder: ((context, AsyncSnapshot<bool> snapshot) {
        if (snapshot.hasError) {
          Provider.of<AppStateProvider>(context, listen: false).setServerError(
            'حدث خطأ أثناء التحقق من المستخدم. سيتم إعادة المحاولة تلقائيًا.',
            () => setState(() {}),
          );
          return const SizedBox.shrink();
        }
        if (snapshot.hasData) {
          if (snapshot.data! == true) {
            return const SeekerScreenProfile();
          } else {
            return Scaffold(
              body: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF2daae2),
                      Colors.white,
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Directionality(
                    textDirection: TextDirection.rtl,
                    child: Column(
                      children: [
                        Expanded(
                          child: SingleChildScrollView(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 20),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                              
                                  const SizedBox(height: 40),
                                  Text(
                                    'مرحباً بك في وظف',
                                    style: GoogleFonts.tajawal(
                                      fontSize: 28,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  if (company == null) ...[
                                    const SizedBox(height: 10),
                                    Text(
                                      'اختر نوع الحساب للمتابعة',
                                      style: GoogleFonts.tajawal(
                                        fontSize: 16,
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                    ),
                                  ] else if (company == false) ...[
                                    const SizedBox(height: 10),
                                    Text(
                                      'منصة الباحثين عن عمل',
                                      style: GoogleFonts.tajawal(
                                        fontSize: 16,
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                    ),
                                    Text(
                                      'ابدأ رحلتك المهنية معنا',
                                      style: GoogleFonts.tajawal(
                                        fontSize: 14,
                                        color: Colors.white.withOpacity(0.7),
                                      ),
                                    ),
                                  ] else ...[
                                    const SizedBox(height: 10),
                                    Text(
                                      'بوابة أصحاب الشركات',
                                      style: GoogleFonts.tajawal(
                                        fontSize: 16,
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                    ),
                                    Text(
                                      'اعثر على أفضل المواهب لشركتك',
                                      style: GoogleFonts.tajawal(
                                        fontSize: 14,
                                        color: Colors.white.withOpacity(0.7),
                                      ),
                                    ),
                                  ],
                                  const SizedBox(height: 10),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: Column(
                                      children: [
                                        if (company == null) ...[
                                          _buildMainOption(
                                            'الباحثين عن عمل',
                                            'assets/profile_seeker.png',
                                            'تسجيل الدخول أو إنشاء حساب جديد للباحثين عن عمل',
                                            () {
                                              setState(() {
                                                company = false;
                                              });
                                            },
                                          ),
                                          const SizedBox(height: 20),
                                          _buildMainOption(
                                            'أصحاب الشركات',
                                            'assets/company-ava.png',
                                            'تسجيل الدخول أو إنشاء حساب جديد للشركات',
                                            () {
                                              setState(() {
                                                company = true;
                                              });
                                            },
                                          ),
                                        ] else if (company == false) ...[
                                          _buildSubOption(
                                            'تسجيل الدخول',
                                            Icons.login_outlined,
                                            () {
                                               Navigator.push(context, MaterialPageRoute(
                                                 builder: (context) => LoginScreenPage(),
                                               ));
                                            },
                                          ),
                                          const SizedBox(height: 16),
                                          _buildSubOption(
                                            'إنشاء حساب جديد',
                                            Icons.person_add_outlined,
                                            () {
                                               Navigator.push(context, MaterialPageRoute(
                                                 builder: (context) => Registeration_Page(messsase: null),
                                               ));
                                            },
                                          ),
                                          const SizedBox(height: 40),
                                          _buildBackButton(),
                                        ] else ...[
                                          _buildSubOption(
                                            'تسجيل دخول الشركات',
                                            Icons.business_center_outlined,
                                            () async {
                                              await Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) => CompanyLoginPage(),
                                                ),
                                              );
                                              
                                              // إشعار الأب بالتحديث
                                              if (mounted) {
                                                setState(() {});
                                              }
                                            },
                                          ),
                                          const SizedBox(height: 16),
                                          _buildSubOption(
                                            'تسجيل شركة جديدة',
                                            Icons.business_outlined,
                                            () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) => RegisterCompanyPage(),
                                                ),
                                              );
                                            },
                                          ),
                                          const SizedBox(height: 40),
                                          _buildBackButton(),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }
        } else {
          return const Center(
            child: CircularProgressIndicator(
              color: Color(0xFF2daae2),
            ),
          );
        }
      }),
    );
  }

  Widget _buildMainOption(String title, String imagePath, String description, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(15),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2daae2).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Image.asset(
                  imagePath,
                  height: 60,
                  width: 60,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                title,
                style: GoogleFonts.tajawal(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF1A1A1A),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                description,
                textAlign: TextAlign.center,
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubOption(String title, IconData icon, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF2daae2).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: const Color(0xFF2daae2),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                title,
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1A1A1A),
                ),
              ),
              const Spacer(),
              Builder(
                builder: (context) {
                  final isRtl = Directionality.of(context) == TextDirection.rtl;
                  return Icon(
                    isRtl ? Icons.arrow_forward_ios : Icons.arrow_back_ios_new,
                    color: const Color(0xFF2daae2),
                    size: 16,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return TextButton.icon(
      onPressed: () {
        setState(() {
          company = null;
        });
      },
      icon: Builder(
        builder: (context) {
          final isRtl = Directionality.of(context) == TextDirection.rtl;
          return Icon(
            isRtl ? Icons.arrow_forward_ios : Icons.arrow_back_ios_new,
            size: 16,
            color: const Color.fromARGB(255, 14, 14, 14),
          );
        },
      ),
      label: Text(
        'رجوع',
        style: GoogleFonts.tajawal(
          fontSize: 16,
          color: const Color.fromARGB(255, 17, 17, 17),
          fontWeight: FontWeight.w500,
        ),
      ),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        backgroundColor: const Color(0xFF2daae2).withOpacity(0.2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
