import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/models/NewsModel.dart';
import 'package:wzzff/models/SpinnerDataModel.dart';
import 'package:wzzff/core/constants/Constants.dart';

class JobsApi {
    static String baseUrl = "https://wzzff.com/apiwzzff/";
  // جلب الوظائف
  static Future<List<JobModel>?> getJobs({
    int page = 1,
    int limit = 10,
    String? country,
    String? category,
    String? search,
  }) async {
    try {
      String url = "${Constants.url}/jobs?page=$page&limit=$limit";
      
      if (country != null && country.isNotEmpty) {
        url += "&country=$country";
      }
      
      if (category != null && category.isNotEmpty) {
        url += "&category=$category";
      }
      
      if (search != null && search.isNotEmpty) {
        url += "&search=$search";
      }
      
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      http.Response response = await http.get(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final List<dynamic> apiDataList = responseData['data'] ?? [];
        
        List<JobModel> jobsmodelList = [];
        for (int i = 0; i < apiDataList.length; i++) {
          if (apiDataList[i] != null) {
            jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
          }
        }
        return jobsmodelList;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // جلب تفاصيل وظيفة محددة
  static Future<JobModel?> getJobDetails({
    required String jobId,
  }) async {
    try {
      final String url = "${Constants.url}/jobs/$jobId";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      http.Response response = await http.get(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return JobModel.fromJson(responseData['data']);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // التقدم لوظيفة
  static Future<Map<String, dynamic>?> applyForJob({
    required String jobId,
    required String token,
    String? coverLetter,
    String? cvUrl,
  }) async {
    try {
      final String url = "${Constants.url}/jobs/$jobId/apply";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, dynamic> applicationData = {
        if (coverLetter != null) 'cover_letter': coverLetter,
        if (cvUrl != null) 'cv_url': cvUrl,
      };
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.post(
        urlConvert, 
        headers: headers,
        body: jsonEncode(applicationData),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // جلب الوظائف المحفوظة
  static Future<List<JobModel>?> getSavedJobs({
    required String token,
  }) async {
    try {
      final String url = "${Constants.url}/saved-jobs";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.get(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final List<dynamic> apiDataList = responseData['data'] ?? [];
        
        List<JobModel> jobsmodelList = [];
        for (int i = 0; i < apiDataList.length; i++) {
          if (apiDataList[i] != null) {
            jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
          }
        }
        return jobsmodelList;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // حفظ وظيفة
  static Future<bool> saveJob({
    required String jobId,
    required String token,
  }) async {
    try {
      final String url = "${Constants.url}/jobs/$jobId/save";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.post(urlConvert, headers: headers);

      return response.statusCode == 200 || response.statusCode == 201;
    } catch (e) {
      return false;
    }
  }

  // إلغاء حفظ وظيفة
  static Future<bool> unsaveJob({
    required String jobId,
    required String token,
  }) async {
    try {
      final String url = "${Constants.url}/jobs/$jobId/unsave";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.delete(urlConvert, headers: headers);

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // جلب الوظائفتقدم إليها
  static Future<List<JobModel>?> getAppliedJobs({
    required String token,
  }) async {
    try {
      final String url = "${Constants.url}/applied-jobs";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.get(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final List<dynamic> apiDataList = responseData['data'] ?? [];
        
        List<JobModel> jobsmodelList = [];
        for (int i = 0; i < apiDataList.length; i++) {
          if (apiDataList[i] != null) {
            jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
          }
        }
        return jobsmodelList;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // الإبلاغ عن وظيفة
  static Future<bool> reportJob({
    required String jobId,
    required String reason,
    required String token,
  }) async {
    try {
      final String url = "${Constants.url}/jobs/$jobId/report";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, dynamic> reportData = {
        'reason': reason,
      };
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
      };

      http.Response response = await http.post(
        urlConvert, 
        headers: headers,
        body: jsonEncode(reportData),
      );

      return response.statusCode == 200 || response.statusCode == 201;
    } catch (e) {
      return false;
    }
  }
  Future<List<JobModel>> jobsByCountryName(
      {required int? offset, required Map data}) async {
    String url = "${baseUrl}countryJobs";
    Uri urlConvert = Uri.parse(url);
    http.Response response = await http.post(urlConvert, body: {
      "offset": offset.toString(),
      "country": data["country"].toString(),
    });
    List apiDataList = jsonDecode(response.body);
    List<JobModel> jobsmodelList = [];
    for (int i = 0; i < apiDataList.length; i++) {
      if (apiDataList[i] != null) {
        jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
      }
    }
    return jobsmodelList;
  }
  Future<List<JobModel>> getmyfavjobs(
      {required int? offset, required Map data}) async {
    String url = "${baseUrl}getmyfavjobs";
    const storage = FlutterSecureStorage();
    String? apiToken = await storage.read(key: "api_token");
    Uri urlConvert = Uri.parse(url);
    http.Response response = await http.post(urlConvert, body: {
      "offset": offset.toString(),
      "api_token": apiToken.toString(),
    });
    List apiDataList = jsonDecode(response.body);
    List<JobModel> jobsmodelList = [];
    for (int i = 0; i < apiDataList.length; i++) {
      if (apiDataList[i] != null) {
        jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
      }
    }
    return jobsmodelList;
  }

    Future<List<JobModel>> getYesterdayJobs() async {
    String url = "${baseUrl}yesterday_jobs";
    Uri urlConvert = Uri.parse(url);
    http.Response response =
        await http.post(urlConvert, body: {});
    List apiDataList = jsonDecode(response.body);
    List<JobModel> jobsmodelList = [];
    for (int i = 0; i < apiDataList.length; i++) {
      if (apiDataList[i] != null) {
        jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
      }
    }
    return jobsmodelList;
  }

  Future<List<JobModel>> getTodayJobs() async {
    String url = "${baseUrl}today_jobs";
    Uri urlConvert = Uri.parse(url);
    http.Response response =
        await http.post(urlConvert, body: {});
    List apiDataList = jsonDecode(response.body);
    List<JobModel> jobsmodelList = [];
    for (int i = 0; i < apiDataList.length; i++) {
      if (apiDataList[i] != null) {
        jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
      }
    }
    return jobsmodelList;
  }
  Future<List<NewsModel>> getNews() async {
    String url = "${baseUrl}blogapi";
    Uri urlConvert = Uri.parse(url);
    http.Response response = await http.post(urlConvert);
    List apiDataList = jsonDecode(response.body);
    List<NewsModel> newsModelList = [];
    for (int i = 0; i < apiDataList.length; i++) {
      if (apiDataList[i] != null) {
        newsModelList.add(NewsModel.fromJson(apiDataList[i]));
      }
    }
    return newsModelList;
  }

  Future<List<JobModel>> getJobsMainIndex({
    required int? offset,
  }) async {
    String url = "${baseUrl}jobsMainApi";
    Uri urlConvert = Uri.parse(url);
    http.Response response =
        await http.post(urlConvert, body: {"offset": offset.toString()});
    List apiDataList = jsonDecode(response.body);
    List<JobModel> jobsmodelList = [];
    for (int i = 0; i < apiDataList.length; i++) {
      if (apiDataList[i] != null) {
        jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
      }
    }
    return jobsmodelList;
  }
  Future<List<JobModel>> searchApi(
      {required int? offset, required Map data}) async {
    String url = "${baseUrl}searchapi";
    Uri urlConvert = Uri.parse(url);
    http.Response response = await http.post(urlConvert, body: {
      "offset": offset.toString(),
      "word": data["word"].toString(),
      "country": data["country"].toString(),
      "city": data["city"] == null ? "" : data["city"].toString()
    });
    List apiDataList = jsonDecode(response.body);
    List<JobModel> jobsmodelList = [];
    for (int i = 0; i < apiDataList.length; i++) {
      if (apiDataList[i] != null) {
        jobsmodelList.add(JobModel.fromJson(apiDataList[i]));
      }
    }
    return jobsmodelList;
  }
    // جلب وظيفة محددة بواسطة المعرف
  Future<JobModel?> getJobById(int jobId) async {
    try {
      // محاولة البحث عن الوظيفة بواسطة المعرف
      String url = "${baseUrl}job_details";
      Uri urlConvert = Uri.parse(url);
      http.Response response = await http.post(urlConvert, body: {
        "id": jobId.toString(),
      });

      Map<String, dynamic> apiData = jsonDecode(response.body);

      // التحقق من وجود بيانات الوظيفة
      if (apiData.containsKey("job") && apiData["job"] != null) {
        return JobModel.fromJson(apiData["job"]);
      }

      return null;
    } catch (e) {
      //debugPrint("Error fetching job by ID: $e");
      return null;
    }
  }


  // جلب المدن بناءً على الدولة
  Future<List<SpinnerDataModel>> getCitesApi({required String country}) async {
    try {
      final String url = "${Constants.url}/cities?country=$country";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      http.Response response = await http.get(urlConvert, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final List<dynamic> citiesData = responseData['data'] ?? [];
        
        List<SpinnerDataModel> cities = [];
        for (var cityData in citiesData) {
          if (cityData != null) {
            cities.add(SpinnerDataModel.fromJson(cityData));
          }
        }
        return cities;
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  /// الإبلاغ عن مشكلة في الوظيفة
  Future<bool> reportJobProblem({
    required String jobTitle,
    required String jobSlug,
    required String problem,
    required String email,
    required String phone,
    required String fcmToken,
  }) async {
    try {
      final String url = "${Constants.url}/apiwzzff/iHaveProblemWithThisAd";
      final Uri urlConvert = Uri.parse(url);
      
      final Map<String, dynamic> reportData = {
        'job_title': jobTitle,
        'job_slug': jobSlug,
        'problem': problem,
        'email': email,
        'phone': phone,
        'fcm_token': fcmToken,
      };
      
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      http.Response response = await http.post(
        urlConvert, 
        headers: headers,
        body: jsonEncode(reportData),
      );

      return response.statusCode == 200 || response.statusCode == 201;
    } catch (e) {
      return false;
    }
  }
}
