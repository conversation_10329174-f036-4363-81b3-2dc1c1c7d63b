import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/job_seeker/profile_steps/common_widgets.dart';
import 'job_form_data.dart';

class StepRequirements extends StatefulWidget {
  final JobFormData formData;

  const StepRequirements({
    Key? key,
    required this.formData,
  }) : super(key: key);

  @override
  _StepRequirementsState createState() => _StepRequirementsState();
}

class _StepRequirementsState extends State<StepRequirements> {
  // Theme helpers
  bool get _isDarkMode => Theme.of(context).brightness == Brightness.dark;
  Color get _primaryColor => Theme.of(context).colorScheme.primary;
  Color get _cardColor => _isDarkMode ? Colors.grey[850]! : Colors.white;
  Color get _surfaceColor => _isDarkMode ? Colors.grey[800]! : Colors.grey[50]!;
  Color get _textColor => _isDarkMode ? Colors.white : Colors.black87;
  Color get _secondaryTextColor => _isDarkMode ? Colors.grey[300]! : Colors.grey[600]!;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _isDarkMode ? Colors.black.withOpacity(0.3) : Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.list_alt, color: _primaryColor, size: 18),
              const SizedBox(width: 8),
              Text(
                'متطلبات الوظيفة والراتب',
                style: GoogleFonts.tajawal(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          buildInputField(
            controller: widget.formData.reqController,
            label: 'متطلبات الوظيفة',
            icon: Icons.list_alt,
            isRequired: true,
            maxLines: 3,
            validator: (v) => v == null || v.isEmpty ? 'يرجى إدخال متطلبات الوظيفة' : null,
          ),
          const SizedBox(height: 16),
          // قسم الراتب
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _primaryColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.monetization_on, color: _primaryColor, size: 16),
                    const SizedBox(width: 6),
                    Text(
                      'معلومات الراتب',
                      style: GoogleFonts.tajawal(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'حدد قيمة الراتب والعملة المستخدمة',
                  style: GoogleFonts.tajawal(
                    fontSize: 10,
                    color: _primaryColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          buildInputField(
            controller: widget.formData.salaryController,
            label: 'الراتب',
            icon: Icons.monetization_on,
            isRequired: true,
            keyboardType: TextInputType.number,
            validator: (v) => v == null || v.isEmpty ? 'يرجى إدخال الراتب' : null,
          ),
          const SizedBox(height: 12),
          buildDropdownField(
            label: 'العملة',
            value: widget.formData.selectedCurrency,
            items: widget.formData.currencies,
            onChanged: (val) => setState(() => widget.formData.selectedCurrency = val),
            icon: Icons.currency_exchange,
            isRequired: true,
            validator: (v) => v == null ? 'يرجى اختيار العملة' : null,
          ),
          const SizedBox(height: 12),
          _buildDatePicker(),
        ],
      ),
    );
  }

  Widget _buildDatePicker() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: _primaryColor.withOpacity(0.3)),
        color: _surfaceColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.event, color: _primaryColor, size: 16),
              const SizedBox(width: 6),
              Text('تاريخ انتهاء التقديم *', style: GoogleFonts.tajawal(
                fontSize: 12, 
                fontWeight: FontWeight.w600,
                color: _textColor,
              )),
            ],
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: DateTime.now().add(const Duration(days: 30)),
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 365)),
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      colorScheme: Theme.of(context).colorScheme.copyWith(
                        primary: _primaryColor,
                        surface: _cardColor,
                      ),
                    ),
                    child: child!,
                  );
                },
              );
              if (date != null) {
                setState(() => widget.formData.endDate = date);
              }
            },
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: _isDarkMode ? Colors.grey[600]! : Colors.grey.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(6),
                color: _cardColor,
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, color: _primaryColor, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    widget.formData.endDate != null 
                        ? '${widget.formData.endDate!.day}/${widget.formData.endDate!.month}/${widget.formData.endDate!.year}'
                        : 'اختر تاريخ انتهاء التقديم',
                    style: GoogleFonts.tajawal(
                      color: widget.formData.endDate != null ? _textColor : _secondaryTextColor,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 