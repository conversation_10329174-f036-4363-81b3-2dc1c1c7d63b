﻿import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/components/CardNews.dart';
import 'package:wzzff/presentation/components/ListShimmerNews.dart';
import 'package:wzzff/models/NewsModel.dart';
import 'package:wzzff/core/constants/Constants.dart';
import '../../../Apis/JobsApi.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/presentation/widgets/announcement_banner.dart';

class NewsPage extends StatefulWidget {
  const NewsPage({super.key});

  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> {
  bool _showHeader = true;
  final ScrollController _scrollController = ScrollController();

  void _scrollListener() {
    if (_scrollController.offset > 10 && _showHeader) {
      setState(() {
        _showHeader = false;
      });
    } else if (_scrollController.offset <= 10 && !_showHeader) {
      setState(() {
        _showHeader = true;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.grey[50],
      body: Directionality(
        textDirection: Constants().getOurDir(),
        child: Column(
          children: [
            const AnnouncementBanner(),
            
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _showHeader ? null : 0,
              child: AnimatedOpacity(
                opacity: _showHeader ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: _NewsAnimatedHeader(),
              ),
            ),
            Expanded(
              child: NewsListView(scrollController: _scrollController),
            ),
          ],
        ),
      ),
    );
  }
}

class _NewsAnimatedHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDarkMode
              ? [primaryColor, primaryColor.withAlpha(220)]
              : [primaryColor, primaryColor.withAlpha(200)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(isDarkMode ? 51 : 26),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            height: 42,
            width: 42,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(40),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.article_rounded,
              size: 22,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 14),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "أحدث المقالات والنصائح",
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  "اكتشف أحدث النصائح والمقالات المهنية",
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.white.withAlpha(230),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class NewsListView extends StatefulWidget {
  final ScrollController? scrollController;
  const NewsListView({super.key, this.scrollController});

  @override
  State<NewsListView> createState() => _NewsListViewState();
}

class _NewsListViewState extends State<NewsListView> {
  List<NewsModel>? news;
  bool isLoading = true;
  bool isRefreshing = false;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<List<NewsModel>?> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedNews = prefs.getString('cached_news');
      if (cachedNews != null) {
        final List<dynamic> decodedNews = json.decode(cachedNews);
        return decodedNews.map((item) => NewsModel.fromJson(item)).toList();
      }
    } catch (e) {
    }
    return null;
  }

  Future<void> _saveToCache(List<NewsModel> newsList) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encodedNews = json.encode(newsList.map((news) => news.toJson()).toList());
      await prefs.setString('cached_news', encodedNews);
    } catch (e) {
    }
  }

  Future<void> _loadData() async {
    if (!isRefreshing) {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });
    }

    try {
      final cachedNews = await _loadFromCache();
      if (cachedNews != null && !isRefreshing) {
        setState(() {
          news = cachedNews;
          isLoading = false;
        });
      }

      // تحميل البيانات الجديدة من الخادم
      final jobsApi = JobsApi();
       final fetchedNews = await jobsApi.getNews();
      //final fetchedNews = <NewsModel>[];

              // مقارنة البيانات الجديدة مع المخزنة
      if (cachedNews == null || _hasNewNews(cachedNews, fetchedNews)) {
        setState(() {
          news = fetchedNews;
          isLoading = false;
          isRefreshing = false;
        });
        // حفظ البيانات الجديدة في الكاش
        await _saveToCache(fetchedNews);
      } else {
        setState(() {
          isLoading = false;
          isRefreshing = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'حدث خطأ أثناء تحميل البيانات';
        isLoading = false;
        isRefreshing = false;
      });
    }
  }

  bool _hasNewNews(List<NewsModel> cachedNews, List<NewsModel> newNews) {
    if (cachedNews.length != newNews.length) return true;
    
    for (int i = 0; i < cachedNews.length; i++) {
      if (cachedNews[i].id != newNews[i].id) return true;
    }
    return false;
  }

  Future<void> _onRefresh() async {
    setState(() {
      isRefreshing = true;
    });
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const ListShimmerNews();
    }

    if (errorMessage.isNotEmpty) {
      Provider.of<AppStateProvider>(context, listen: false).setServerError(
        errorMessage.isNotEmpty ? errorMessage : 'حدث خطأ أثناء تحميل الأخبار. سيتم إعادة المحاولة تلقائياً.',
        _loadData,
      );
      return const SizedBox.shrink();
    }

    if (news == null || news!.isEmpty) {
      return const EmptyNewsView();
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: Theme.of(context).colorScheme.primary,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        controller: widget.scrollController,
        itemCount: news!.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: CardNews(data: news![index]),
          );
        },
      ),
    );
  }
}

class ErrorView extends StatelessWidget {
  final String errorMessage;
  final VoidCallback onRetry;

  const ErrorView({
    super.key,
    required this.errorMessage,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).cardTheme.color
              : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(
                  Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.red[900]?.withOpacity(0.2)
                    : Colors.red[50],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.red[300]
                    : Colors.red[400],
                size: 48,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              errorMessage,
              style: GoogleFonts.tajawal(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : const Color(0xFF555555),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              ),
              child: Text(
                'إعادة المحاولة',
                style: GoogleFonts.tajawal(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class EmptyNewsView extends StatelessWidget {
  const EmptyNewsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).cardTheme.color
              : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(
                  Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(
                    Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.article_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد أخبار متاحة حالياً',
              style: GoogleFonts.tajawal(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.titleMedium?.color
                    : const Color(0xFF555555),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى التحقق مرة أخرى لاحقاً',
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Repository Pattern
class NewsRepository {
  Future<List<NewsModel>> getNews() async {
    // استخدام API الحالي
    // return await JobsApi().getNews();
    return [];
  }
}
