/// نموذج اقتراحات البحث الذكي
class SearchSuggestion {
  final String text;
  final SuggestionType type;
  final double confidence;
  final String icon;
  final String? description;
  final Map<String, dynamic>? metadata;

  const SearchSuggestion({
    required this.text,
    required this.type,
    required this.confidence,
    required this.icon,
    this.description,
    this.metadata,
  });

  factory SearchSuggestion.fromJson(Map<String, dynamic> json) {
    return SearchSuggestion(
      text: json['text'] ?? '',
      type: SuggestionType.values.firstWhere(
        (e) => e.toString() == 'SuggestionType.${json['type']}',
        orElse: () => SuggestionType.general,
      ),
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      icon: json['icon'] ?? '🔍',
      description: json['description'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'type': type.toString().split('.').last,
      'confidence': confidence,
      'icon': icon,
      'description': description,
      'metadata': metadata,
    };
  }

  SearchSuggestion copyWith({
    String? text,
    SuggestionType? type,
    double? confidence,
    String? icon,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    return SearchSuggestion(
      text: text ?? this.text,
      type: type ?? this.type,
      confidence: confidence ?? this.confidence,
      icon: icon ?? this.icon,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchSuggestion &&
        other.text == text &&
        other.type == type;
  }

  @override
  int get hashCode => text.hashCode ^ type.hashCode;

  @override
  String toString() {
    return 'SearchSuggestion(text: $text, type: $type, confidence: $confidence, icon: $icon)';
  }
}

/// أنواع اقتراحات البحث
enum SuggestionType {
  // اقتراحات عامة
  general,
  
  // اقتراحات من المرادفات
  synonym,
  
  // اقتراحات من عناوين الوظائف الشائعة
  jobTitle,
  
  // اقتراحات من تاريخ البحث
  history,
  
  // اقتراحات ذكية متقدمة
  smart,
  
  // اقتراحات من الشركات
  company,
  
  // اقتراحات من المواقع
  location,
  
  // اقتراحات من المهارات
  skill,
  
  // اقتراحات من الخبرة
  experience,
}

/// امتداد لتسهيل العمل مع أنواع الاقتراحات
extension SuggestionTypeExtension on SuggestionType {
  /// الحصول على الوصف باللغة العربية
  String get arabicName {
    switch (this) {
      case SuggestionType.general:
        return 'عام';
      case SuggestionType.synonym:
        return 'مرادف';
      case SuggestionType.jobTitle:
        return 'مسمى وظيفي';
      case SuggestionType.history:
        return 'تاريخ البحث';
      case SuggestionType.smart:
        return 'اقتراح ذكي';
      case SuggestionType.company:
        return 'شركة';
      case SuggestionType.location:
        return 'موقع';
      case SuggestionType.skill:
        return 'مهارة';
      case SuggestionType.experience:
        return 'خبرة';
    }
  }

  /// الحصول على الأيقونة الافتراضية
  String get defaultIcon {
    switch (this) {
      case SuggestionType.general:
        return '🔍';
      case SuggestionType.synonym:
        return '🔄';
      case SuggestionType.jobTitle:
        return '💼';
      case SuggestionType.history:
        return '🕐';
      case SuggestionType.smart:
        return '🧠';
      case SuggestionType.company:
        return '🏢';
      case SuggestionType.location:
        return '📍';
      case SuggestionType.skill:
        return '⚡';
      case SuggestionType.experience:
        return '🎯';
    }
  }

  /// ترتيب الأولوية (الأعلى أولاً)
  int get priority {
    switch (this) {
      case SuggestionType.history:
        return 5;
      case SuggestionType.smart:
        return 4;
      case SuggestionType.jobTitle:
        return 3;
      case SuggestionType.synonym:
        return 2;
      case SuggestionType.company:
        return 2;
      case SuggestionType.skill:
        return 1;
      case SuggestionType.location:
        return 1;
      case SuggestionType.experience:
        return 1;
      case SuggestionType.general:
        return 0;
    }
  }
} 