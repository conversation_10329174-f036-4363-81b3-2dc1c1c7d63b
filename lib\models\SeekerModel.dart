class SeekerModel {
  String first_name;
  String middle_name;
  String last_name;
  String name;
  String email;
  int gender_id;
  int country_id;
  int num_profile_views;

  // إضافة الخصائص المفقودة
  String? phone;
  String? address;
  String? city;
  String? education;
  String? university;
  String? experience;
  String? skills;
  String? education_level;
  String? job_title;
  int? expected_salary;
  int? current_salary;
  String? job_type;
  String? work_location;
  bool? is_remote_work;

  // إضافة الخصائص الجديدة
  String? firstName;
  String? middleName;
  String? lastName;
  String? gender;
  String? country;
  String? profile_image;

  SeekerModel({
    required this.first_name,
    required this.middle_name,
    required this.last_name,
    required this.name,
    required this.email,
    required this.gender_id,
    required this.country_id,
    required this.num_profile_views,
    this.phone,
    this.address,
    this.city,
    this.education,
    this.university,
    this.experience,
    this.skills,
    this.education_level,
    this.job_title,
    this.expected_salary,
    this.current_salary,
    this.job_type,
    this.work_location,
    this.is_remote_work,
    this.firstName,
    this.middleName,
    this.lastName,
    this.gender,
    this.country,
    this.profile_image,
  });

  factory SeekerModel.fromJson(Map u) {
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) return int.tryParse(value);
      return null;
    }
    return SeekerModel(
        first_name: u["first_name"]?.toString() ?? '',
        middle_name: u["middle_name"]?.toString() ?? '',
        last_name: u["last_name"]?.toString() ?? '',
        name: u["name"]?.toString() ?? '',
        email: u["email"]?.toString() ?? '',
        gender_id: parseInt(u["gender_id"]) ?? 0,
        country_id: parseInt(u["country_id"]) ?? 0,
        num_profile_views: parseInt(u["num_profile_views"]) ?? 0,
        phone: u["phone"]?.toString(),
        address: u["street_address"]?.toString(),
        city: u["city"]?.toString(),
        education: u["education"]?.toString(),
        university: u["university"]?.toString(),
        experience: u["experience"]?.toString(),
        skills: u["skills"]?.toString(),
        education_level: u["education_level"]?.toString(),
        job_title: u["job_title"]?.toString(),
        expected_salary: parseInt(u["expected_salary"]),
        current_salary: parseInt(u["current_salary"]),
        job_type: u["job_type"]?.toString(),
        work_location: u["work_location"]?.toString(),
        is_remote_work: u["is_remote_work"] == true || u["is_remote_work"] == "true",
        firstName: u["firstName"]?.toString(),
        middleName: u["middleName"]?.toString(),
        lastName: u["lastName"]?.toString(),
        gender: u["gender"]?.toString(),
        country: u["country"]?.toString(),
        profile_image: u["image"]?.toString() ?? u["profile_image"]?.toString(),
    );
  }

  SeekerModel copyWithMap(Map<String, dynamic> map) {
    return SeekerModel(
      first_name: map['first_name'] ?? this.first_name,
      middle_name: map['middle_name'] ?? this.middle_name,
      last_name: map['last_name'] ?? this.last_name,
      name: map['name'] ?? this.name,
      email: map['email'] ?? this.email,
      gender_id: map['gender_id'] ?? this.gender_id,
      country_id: map['country_id'] ?? this.country_id,
      num_profile_views: map['num_profile_views'] ?? this.num_profile_views,
      phone: map['phone'] ?? this.phone,
      address: map['address'] ?? this.address,
      city: map['city'] ?? this.city,
      education: map['education'] ?? this.education,
      university: map['university'] ?? this.university,
      experience: map['experience'] ?? this.experience,
      skills: map['skills'] ?? this.skills,
      education_level: map['education_level'] ?? this.education_level,
      job_title: map['job_title'] ?? this.job_title,
      expected_salary: map['expected_salary'] ?? this.expected_salary,
      current_salary: map['current_salary'] ?? this.current_salary,
      job_type: map['job_type'] ?? this.job_type,
      work_location: map['work_location'] ?? this.work_location,
      is_remote_work: map['is_remote_work'] ?? this.is_remote_work,
      firstName: map['firstName'] ?? this.firstName,
      middleName: map['middleName'] ?? this.middleName,
      lastName: map['lastName'] ?? this.lastName,
      gender: map['gender'] ?? this.gender,
      country: map['country'] ?? this.country,
      profile_image: map['profile_image'] ?? this.profile_image,
    );
  }
}
