import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/Apis/CompanyApi.dart';

class EditCompanyFormPage extends StatefulWidget {
  final Map<String, dynamic> companyData;
  final Function(Map<String, dynamic>) onDataUpdated;

  const EditCompanyFormPage({
    Key? key,
    required this.companyData,
    required this.onDataUpdated,
  }) : super(key: key);

  @override
  _EditCompanyFormPageState createState() => _EditCompanyFormPageState();
}

class _EditCompanyFormPageState extends State<EditCompanyFormPage> 
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isSaving = false;

  // Animation Controllers
  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);
  Color get successColor => const Color(0xff4caf50);

  // Controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _websiteController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _ceoController = TextEditingController();
  final TextEditingController _establishedController = TextEditingController();
  final TextEditingController _employeesController = TextEditingController();
  final TextEditingController _officesController = TextEditingController();
  final TextEditingController _faxController = TextEditingController();
  final TextEditingController _facebookController = TextEditingController();
  final TextEditingController _twitterController = TextEditingController();
  final TextEditingController _linkedinController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadData();
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _websiteController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _ceoController.dispose();
    _establishedController.dispose();
    _employeesController.dispose();
    _officesController.dispose();
    _faxController.dispose();
    _facebookController.dispose();
    _twitterController.dispose();
    _linkedinController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimationController.forward();
  }

  void _loadData() {
    _nameController.text = widget.companyData['name'] ?? '';
    _emailController.text = widget.companyData['email'] ?? '';
    _phoneController.text = widget.companyData['phone'] ?? '';
    _websiteController.text = widget.companyData['website'] ?? '';
    _locationController.text = widget.companyData['location'] ?? '';
    _descriptionController.text = widget.companyData['description'] ?? '';
    _ceoController.text = widget.companyData['ceo'] ?? '';
    _establishedController.text = widget.companyData['established_in'] ?? '';
    _employeesController.text = widget.companyData['no_of_employees']?.toString() ?? '';
    _officesController.text = widget.companyData['no_of_offices']?.toString() ?? '';
    _faxController.text = widget.companyData['fax'] ?? '';
    _facebookController.text = widget.companyData['facebook'] ?? '';
    _twitterController.text = widget.companyData['twitter'] ?? '';
    _linkedinController.text = widget.companyData['linkedin'] ?? '';
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = Theme.of(context).scaffoldBackgroundColor;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: Stack(
          children: [
            // خلفية متدرجة
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    primaryColor.withOpacity(0.05),
                    backgroundColor,
                    backgroundColor,
                  ],
                  stops: const [0.0, 0.3, 1.0],
                ),
              ),
            ),

            Column(
              children: [
                // Header مخصص
                _buildCustomHeader(isDarkMode),

                // محتوى الصفحة
                Expanded(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // المعلومات الأساسية
                            _buildBasicInfoSection(isDarkMode),
                            
                            const SizedBox(height: 20),
                            
                            // معلومات الاتصال
                            _buildContactSection(isDarkMode),
                            
                            const SizedBox(height: 20),
                            
                            // تفاصيل الشركة
                            _buildDetailsSection(isDarkMode),
                            
                            const SizedBox(height: 20),
                            
                            // روابط التواصل الاجتماعي
                            _buildSocialSection(isDarkMode),
                            
                            const SizedBox(height: 30),
                            
                            // أزرار الحفظ والإلغاء
                            _buildActionButtons(isDarkMode),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomHeader(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 8,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة التعديل
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [primaryColor, accentColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Icon(
              Icons.edit,
              color: Colors.white,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // العنوان
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تعديل بيانات الشركة',
                  style: GoogleFonts.tajawal(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'تحديث معلومات الشركة',
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // زر الرجوع
          Container(
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                Icons.arrow_forward_ios,
                color: primaryColor,
                size: 20,
              ),
              onPressed: () => Navigator.pop(context),
              tooltip: 'رجوع',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(bool isDarkMode) {
    return _buildSection(
      'المعلومات الأساسية',
      Icons.business,
      primaryColor,
      isDarkMode,
      [
        _buildTextField(
          'اسم الشركة',
          _nameController,
          Icons.business,
          isRequired: true,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'نبذة عن الشركة',
          _descriptionController,
          Icons.description,
          maxLines: 4,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'الموقع',
          _locationController,
          Icons.location_on,
          isDarkMode: isDarkMode,
        ),
      ],
    );
  }

  Widget _buildContactSection(bool isDarkMode) {
    return _buildSection(
      'معلومات الاتصال',
      Icons.contact_phone,
      accentColor,
      isDarkMode,
      [
        _buildTextField(
          'البريد الإلكتروني',
          _emailController,
          Icons.email,
          isRequired: true,
          keyboardType: TextInputType.emailAddress,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'رقم الهاتف',
          _phoneController,
          Icons.phone,
          isRequired: true,
          keyboardType: TextInputType.phone,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'الفاكس',
          _faxController,
          Icons.fax,
          keyboardType: TextInputType.phone,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'الموقع الإلكتروني',
          _websiteController,
          Icons.language,
          keyboardType: TextInputType.url,
          isDarkMode: isDarkMode,
        ),
      ],
    );
  }

  Widget _buildDetailsSection(bool isDarkMode) {
    return _buildSection(
      'تفاصيل الشركة',
      Icons.info_outline,
      successColor,
      isDarkMode,
      [
        _buildTextField(
          'المدير التنفيذي',
          _ceoController,
          Icons.person,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'سنة التأسيس',
          _establishedController,
          Icons.calendar_today,
          keyboardType: TextInputType.number,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'عدد الموظفين',
          _employeesController,
          Icons.people,
          keyboardType: TextInputType.number,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'عدد المكاتب',
          _officesController,
          Icons.business_center,
          keyboardType: TextInputType.number,
          isDarkMode: isDarkMode,
        ),
      ],
    );
  }

  Widget _buildSocialSection(bool isDarkMode) {
    return _buildSection(
      'روابط التواصل الاجتماعي',
      Icons.share,
      Colors.blue,
      isDarkMode,
      [
        _buildTextField(
          'Facebook',
          _facebookController,
          Icons.facebook,
          keyboardType: TextInputType.url,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'Twitter',
          _twitterController,
          Icons.alternate_email,
          keyboardType: TextInputType.url,
          isDarkMode: isDarkMode,
        ),
        _buildTextField(
          'LinkedIn',
          _linkedinController,
          Icons.business_center,
          keyboardType: TextInputType.url,
          isDarkMode: isDarkMode,
        ),
      ],
    );
  }

  Widget _buildSection(String title, IconData icon, Color color, bool isDarkMode, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller,
    IconData icon, {
    bool isRequired = false,
    int maxLines = 1,
    TextInputType keyboardType = TextInputType.text,
    required bool isDarkMode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                label + (isRequired ? ' *' : ''),
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.08),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextFormField(
              controller: controller,
              maxLines: maxLines,
              keyboardType: keyboardType,
              style: GoogleFonts.tajawal(
                fontSize: 14,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
              decoration: InputDecoration(
                filled: true,
                fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[50],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: primaryColor,
                    width: 2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.grey.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              validator: isRequired
                  ? (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'هذا الحقل مطلوب';
                      }
                      if (label.contains("البريد") && !_isValidEmail(value)) {
                        return 'يرجى إدخال بريد إلكتروني صحيح';
                      }
                      return null;
                    }
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(bool isDarkMode) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
              borderRadius: BorderRadius.circular(16),
            ),
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Text(
                'إلغاء',
                style: GoogleFonts.tajawal(
                  color: isDarkMode ? Colors.white : Colors.grey[700],
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [primaryColor, accentColor],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isSaving ? null : _saveChanges,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: _isSaving
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'جاري الحفظ...',
                          style: GoogleFonts.tajawal(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.save,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'حفظ التعديلات',
                          style: GoogleFonts.tajawal(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ],
    );
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final updatedData = {
        'name': _nameController.text.trim(),
        'email': _emailController.text.trim(),
        'phone': _phoneController.text.trim(),
        'website': _websiteController.text.trim(),
        'location': _locationController.text.trim(),
        'description': _descriptionController.text.trim(),
        'ceo': _ceoController.text.trim(),
        'established_in': _establishedController.text.trim(),
        'no_of_employees': _employeesController.text.trim().isNotEmpty 
            ? int.tryParse(_employeesController.text.trim()) 
            : null,
        'no_of_offices': _officesController.text.trim().isNotEmpty 
            ? int.tryParse(_officesController.text.trim()) 
            : null,
        'fax': _faxController.text.trim(),
        'facebook': _facebookController.text.trim(),
        'twitter': _twitterController.text.trim(),
        'linkedin': _linkedinController.text.trim(),
      };

      final response = await CompanyApi().updateCompanyData(updatedData);

      if (response['success'] == true) {
        final completeUpdatedData = {
          ...widget.companyData,
          ...updatedData,
        };

        widget.onDataUpdated(completeUpdatedData);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'تم حفظ التعديلات بنجاح',
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: successColor,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            duration: const Duration(seconds: 3),
          ),
        );

        Navigator.pop(context);
      } else {
        _showErrorSnackBar(response['message'] ?? 'حدث خطأ أثناء حفظ التعديلات');
      }
    } catch (e) {
      _showErrorSnackBar('تعذر الاتصال بالخادم، يرجى المحاولة لاحقاً');
    }

    setState(() {
      _isSaving = false;
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.tajawal(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }
} 