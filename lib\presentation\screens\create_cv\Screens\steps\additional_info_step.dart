import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';
import 'package:wzzff/presentation/screens/create_cv/Widgets/modern_step_components.dart';

class AdditionalInfoStep extends StatefulWidget {
  final CvModel cvData;
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic> currentData;

  const AdditionalInfoStep({
    Key? key,
    required this.cvData,
    required this.onDataChanged,
    required this.currentData,
  }) : super(key: key);

  @override
  State<AdditionalInfoStep> createState() => _AdditionalInfoStepState();
}

class _AdditionalInfoStepState extends State<AdditionalInfoStep> 
    with TickerProviderStateMixin {
  late TextEditingController _facebookController;
  late TextEditingController _twitterController;
  late TextEditingController _linkedInController;
  late TextEditingController _behanceController;
  late TextEditingController _summaryController;

  String? _age;
  String? _socialStatus;

  // Animation Controllers
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);

  final List<String> socialStatusOptions = ['اعزب', 'متزوج', 'مطلق'];
  final List<String> ageOptions = [
    for (int i = 18; i <= 60; i++) " سنة ${i.toString()}"
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeControllers();
    _startAnimations();
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
      }
    });
  }

  void _initializeControllers() {
    _facebookController = TextEditingController(text: widget.currentData['face']);
    _twitterController = TextEditingController(text: widget.currentData['twt']);
    _linkedInController = TextEditingController(text: widget.currentData['linked_in']);
    _behanceController = TextEditingController(text: widget.currentData['behance']);
    _summaryController = TextEditingController(text: widget.currentData['summary']);

    _age = widget.currentData['age'];
    _socialStatus = widget.currentData['social_status'];

    // إضافة المستمعين للتحديث التلقائي
    _facebookController.addListener(_updateData);
    _twitterController.addListener(_updateData);
    _linkedInController.addListener(_updateData);
    _behanceController.addListener(_updateData);
    _summaryController.addListener(_updateData);
  }

  void _updateData() {
    widget.onDataChanged({
      'face': _facebookController.text,
      'twt': _twitterController.text,
      'linked_in': _linkedInController.text,
      'behance': _behanceController.text,
      'summary': _summaryController.text,
      'age': _age,
      'social_status': _socialStatus,
    });
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _facebookController.dispose();
    _twitterController.dispose();
    _linkedInController.dispose();
    _behanceController.dispose();
    _summaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Directionality(
      textDirection: widget.cvData.ltf == "0" ? TextDirection.rtl : TextDirection.ltr,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(12),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم المعلومات الشخصية
                if (widget.cvData.age == 1 || widget.cvData.social_status == 1)
                  _buildPersonalInfoSection(isDarkMode),
                
                if (widget.cvData.age == 1 || widget.cvData.social_status == 1)
                  const SizedBox(height: 14),
                
                // قسم وسائل التواصل الاجتماعي
                if (_hasSocialMediaFields())
                  _buildSocialMediaSection(isDarkMode),
                
                if (_hasSocialMediaFields())
                  const SizedBox(height: 14),
                
                // قسم الملخص الشخصي
                if (widget.cvData.summary == 1)
                  _buildSummarySection(isDarkMode),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _hasSocialMediaFields() {
    return widget.cvData.face == 1 || 
           widget.cvData.twt == 1 || 
           widget.cvData.linked_in == 1 || 
           widget.cvData.behance == 1;
  }

  Widget _buildPersonalInfoSection(bool isDarkMode) {
    return ModernStepComponents.buildModernCard(
      context: context,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ModernStepComponents.buildSectionHeader(
            title: widget.cvData.ltf == "0" ? 'المعلومات الشخصية' : 'Personal Information',
            icon: Icons.person_outline,
            context: context,
          ),
          
          const SizedBox(height: 16),
          
          // العمر والحالة الاجتماعية
          Row(
            children: [
              if (widget.cvData.age == 1)
                Expanded(
                  child: ModernStepComponents.buildModernDropdown<String>(
                    label: Lang().getWord("age", widget.cvData.ltf),
                    icon: Icons.cake,
                    value: _age,
                    items: ageOptions,
                    itemToString: (item) => item,
                    onChanged: (value) {
                      setState(() {
                        _age = value;
                      });
                      _updateData();
                    },
                    hint: Lang().getWord("age_hint", widget.cvData.ltf),
                    isRequired: true,
                    context: context,
                  ),
                ),
              
              if (widget.cvData.age == 1 && widget.cvData.social_status == 1)
                const SizedBox(width: 12),
              
              if (widget.cvData.social_status == 1)
                Expanded(
                  child: ModernStepComponents.buildModernDropdown<String>(
                    label: Lang().getWord("social_status", widget.cvData.ltf),
                    icon: Icons.family_restroom,
                    value: _socialStatus,
                    items: socialStatusOptions,
                    itemToString: (item) => item,
                    onChanged: (value) {
                      setState(() {
                        _socialStatus = value;
                      });
                      _updateData();
                    },
                    hint: Lang().getWord("social_status_hint", widget.cvData.ltf),
                    isRequired: true,
                    context: context,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSocialMediaSection(bool isDarkMode) {
    return ModernStepComponents.buildModernCard(
      context: context,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ModernStepComponents.buildSectionHeader(
            title: widget.cvData.ltf == "0" ? 'وسائل التواصل الاجتماعي' : 'Social Media',
            icon: Icons.share,
            subtitle: widget.cvData.ltf == "0" ? 'اختياري - يمكنك تركها فارغة' : 'Optional - you can leave empty',
            context: context,
          ),
          
          const SizedBox(height: 16),
          
          // حقول وسائل التواصل
          if (widget.cvData.face == 1)
            Column(
              children: [
                ModernStepComponents.buildModernTextField(
                  controller: _facebookController,
                  label: Lang().getWord("face", widget.cvData.ltf),
                  hint: 'https://facebook.com/username',
                  icon: Icons.facebook,
                  keyboardType: TextInputType.url,
                  context: context,
                ),
                const SizedBox(height: 16),
              ],
            ),
          
          if (widget.cvData.twt == 1)
            Column(
              children: [
                ModernStepComponents.buildModernTextField(
                  controller: _twitterController,
                  label: Lang().getWord("twt", widget.cvData.ltf),
                  hint: 'https://twitter.com/username',
                  icon: Icons.alternate_email,
                  keyboardType: TextInputType.url,
                  context: context,
                ),
                const SizedBox(height: 16),
              ],
            ),
          
          if (widget.cvData.linked_in == 1)
            Column(
              children: [
                ModernStepComponents.buildModernTextField(
                  controller: _linkedInController,
                  label: Lang().getWord("linked_in", widget.cvData.ltf),
                  hint: 'https://linkedin.com/in/username',
                  icon: Icons.work,
                  keyboardType: TextInputType.url,
                  context: context,
                ),
                const SizedBox(height: 16),
              ],
            ),
          
          if (widget.cvData.behance == 1)
            ModernStepComponents.buildModernTextField(
              controller: _behanceController,
              label: Lang().getWord("behance", widget.cvData.ltf),
              hint: 'https://behance.net/username',
              icon: Icons.palette,
              keyboardType: TextInputType.url,
              context: context,
            ),
        ],
      ),
    );
  }

  Widget _buildSummarySection(bool isDarkMode) {
    return ModernStepComponents.buildModernCard(
      context: context,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ModernStepComponents.buildSectionHeader(
            title: Lang().getWord("summary", widget.cvData.ltf),
            icon: Icons.description,
            subtitle: widget.cvData.ltf == "0" ? 'اكتب نبذة مختصرة عن نفسك ومهاراتك' : 'Write a brief summary about yourself and skills',
            context: context,
          ),
          
          const SizedBox(height: 16),
          
          ModernStepComponents.buildModernTextField(
            controller: _summaryController,
            label: Lang().getWord("summary", widget.cvData.ltf),
            hint: widget.cvData.ltf == "0" 
                ? 'اكتب نبذة مختصرة عن خبراتك ومهاراتك وأهدافك المهنية...'
                : 'Write a brief summary of your experience, skills and career goals...',
            icon: Icons.edit_note,
            maxLines: 4,
            isRequired: true,
            context: context,
          ),
        ],
      ),
    );
  }
}
