import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:wzzff/job/JobApplicationForm.dart';
import 'package:wzzff/presentation/widgets/HtmlWidget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../core/utils/app_messages.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wzzff/services/google_ad_service.dart';
import 'package:wzzff/presentation/screens/jobs/report_job_screen.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/models/JobModel.dart';

// إضافة كلاس WaveClipper لإنشاء تموج جمالي
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height - 40);

    var firstControlPoint = Offset(size.width / 4, size.height);
    var firstEndPoint = Offset(size.width / 2, size.height - 20);
    path.quadraticBezierTo(firstControlPoint.dx, firstControlPoint.dy,
        firstEndPoint.dx, firstEndPoint.dy);

    var secondControlPoint = Offset(size.width - (size.width / 4), size.height - 40);
    var secondEndPoint = Offset(size.width, size.height - 10);
    path.quadraticBezierTo(secondControlPoint.dx, secondControlPoint.dy,
        secondEndPoint.dx, secondEndPoint.dy);

    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class DetailJobScreen extends StatefulWidget {
  String title;

  String des;

  String code_address;

  String? email;

  String? number;

  String salary_currency;

  String? salary;

  String cat;

  String gender;

  String state_name;

  String country_name;

  String job_type_name;

  String city_name;

  String company_name;

  String edu;

  String exp;

  String end_at;

  String slug;

  String created_at_date;

  String time;

  DetailJobScreen({
    Key? key,
    required String this.title,
    required String this.des,
    required String this.code_address,
    required String? this.email,
    required String? this.number,
    required String this.salary_currency,
    required String? this.salary,
    required String this.cat,
    required String this.gender,
    required String this.state_name,
    required String this.country_name,
    required String this.job_type_name,
    required String this.city_name,
    required String this.company_name,
    required String this.edu,
    required String this.exp,
    required String this.end_at,
    required String this.time,
    required String this.created_at_date,
    required String this.slug,
  }) : super(key: key);

  @override
  State<DetailJobScreen> createState() => _DetailJobScreenState();
}

class _DetailJobScreenState extends State<DetailJobScreen> {
  bool isFavorite = false;
  
  // متغيرات الإعلانات
  BannerAd? _topBannerAd;
  BannerAd? _middleBannerAd;
  BannerAd? _bottomBannerAd;
  bool _isTopBannerAdLoaded = false;
  bool _isMiddleBannerAdLoaded = false;
  bool _isBottomBannerAdLoaded = false;

  // متغيرات إعلان الفيديو
  RewardedInterstitialAd? _rewardedInterstitialAd;
  bool _isRewardedInterstitialAdLoaded = false;
  String? _pendingAction; // لتخزين الإجراء المعلق بعد مشاهدة الإعلان
  bool _showAdLoadingOverlay = false;
  bool _adWatchedThisSession = false;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    
    _checkIfFavorite();
    _loadTopBannerAd();
    _loadMiddleBannerAd();
    _loadBottomBannerAd();
    _loadRewardedInterstitialAd();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _hideLoadingOverlay();
    _topBannerAd?.dispose();
    _middleBannerAd?.dispose();
    _bottomBannerAd?.dispose();
    _rewardedInterstitialAd?.dispose();
    super.dispose();
  }

  // تحميل إعلان البانر العلوي
  void _loadTopBannerAd() async {
   // _topBannerAd = await GoogleAdService().loadBannerAd();
    setState(() {
      _isTopBannerAdLoaded = true;
    });
  }

  // تحميل إعلان البانر المتوسط
  void _loadMiddleBannerAd() async {
  //  _middleBannerAd = await GoogleAdService().loadBannerAd(size: AdSize.mediumRectangle);
    setState(() {
      _isMiddleBannerAdLoaded = true;
    });
  }

  // تحميل إعلان البانر السفلي
  void _loadBottomBannerAd() async {
   // _bottomBannerAd = await GoogleAdService().loadBannerAd();
    setState(() {
      _isBottomBannerAdLoaded = true;
    });
  }

  // التحقق مما إذا كانت الوظيفة مفضلة
  Future<void> _checkIfFavorite() async {
    // تحقق من حفظ الوظيفة في المفضلة
    final prefs = await SharedPreferences.getInstance();
    final favoriteJobs = prefs.getStringList('favorite_jobs') ?? [];
    setState(() {
      isFavorite = favoriteJobs.contains(widget.slug);
    });
  }

  // حفظ أو إزالة الوظيفة من المفضلة
  Future<void> _toggleFavorite() async {
    print("_toggleFavorite called");
    final prefs = await SharedPreferences.getInstance();
    final savedJobs = prefs.getStringList('savedJobs') ?? [];
    final savedJobsData = prefs.getString('savedJobsData') ?? '{}';
    Map<String, dynamic> jobsData = json.decode(savedJobsData);

    setState(() {
      isFavorite = !isFavorite;
    });

    print("isFavorite: $isFavorite");

    if (isFavorite) {
      // إضافة الوظيفة إلى المفضلة
      if (!savedJobs.contains(widget.slug)) {
        savedJobs.add(widget.slug);
        await prefs.setStringList('savedJobs', savedJobs);

        // حفظ بيانات الوظيفة
        jobsData[widget.slug] = {
          'title': widget.title,
          'company_name': widget.company_name,
          'city_name': widget.city_name,
          'country_name': widget.country_name,
          'job_type_name': widget.job_type_name,
          'time': widget.time,
          'salary': widget.salary,
          'salary_currency': widget.salary_currency,
          'des': widget.des,
          'email': widget.email,
          'number': widget.number,
          'code_address': widget.code_address,
          'gender': widget.gender,
          'cat': widget.cat,
          'state_name': widget.state_name,
          'edu': widget.edu,
          'end_at': widget.end_at,
          'exp': widget.exp,
          'created_at_date': widget.created_at_date,
          'slug': widget.slug,
        };
        await prefs.setString('savedJobsData', json.encode(jobsData));

        // استخدام Fluttertoast مباشرة
        Fluttertoast.showToast(
          msg: 'تمت إضافة الوظيفة إلى المفضلة',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 3,
          backgroundColor: const Color(0xFF43A047),
          textColor: Colors.white,
          fontSize: 16.0,
        );
      }
    } else {
      // إزالة الوظيفة من المفضلة
      savedJobs.remove(widget.slug);
      await prefs.setStringList('savedJobs', savedJobs);

      // إزالة بيانات الوظيفة
      jobsData.remove(widget.slug);
      await prefs.setString('savedJobsData', json.encode(jobsData));


      // استخدام Fluttertoast مباشرة
      Fluttertoast.showToast(
        msg: 'تمت إزالة الوظيفة من المفضلة',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 3,
        backgroundColor: const Color(0xFF2daae2),
        textColor: Colors.white,
        fontSize: 16.0,
      );
    }
  }

  void _share(String title, String slug) {
    // استخدام Share.share من حزمة share_plus
    share_plus.Share.share('وظيفة: $title\nرابط: https://wzzff.com/job/$slug');
  }

  void _openJobOnSite(String slug) {
    launchUrlString('https://wzzff.com/job/$slug');
  }

  // عرض دائرة تحميل جميلة
  void _showLoadingOverlay() {
    if (!_showAdLoadingOverlay) {
      _showAdLoadingOverlay = true;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          final isDark = Theme.of(context).brightness == Brightness.dark;
          return Center(
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isDark ? Colors.black.withOpacity(0.7) : Colors.white.withOpacity(0.85),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                ),
              ),
            ),
          );
        },
      );
    }
  }

  void _hideLoadingOverlay() {
    if (_showAdLoadingOverlay) {
      _showAdLoadingOverlay = false;
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  void _loadRewardedInterstitialAd() {
    _isRewardedInterstitialAdLoaded = false;
    RewardedInterstitialAd.load(
      adUnitId: GoogleAdService().rewardedInterstitialAdUnitId,
      request: const AdRequest(),
      rewardedInterstitialAdLoadCallback: RewardedInterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedInterstitialAd = ad;
          _isRewardedInterstitialAdLoaded = true;
        },
        onAdFailedToLoad: (error) {
          _rewardedInterstitialAd = null;
          _isRewardedInterstitialAdLoaded = false;
        },
      ),
    );
  }

  void _showEmailWithAd() {
    if (widget.email == null || widget.email!.isEmpty) return;
    _pendingAction = 'email';
    if (_adWatchedThisSession) {
      _pendingAction = 'email';
      _executePostAdAction(userEarnedReward: true);
      return;
    }
    if (_isRewardedInterstitialAdLoaded && _rewardedInterstitialAd != null) {
      _showAdAndHandle('email');
    } else {
      _showLoadingOverlay();
      Future.doWhile(() async {
        if (_isDisposed) return false;
        await Future.delayed(const Duration(milliseconds: 300));
        if (_isRewardedInterstitialAdLoaded && _rewardedInterstitialAd != null) {
          _hideLoadingOverlay();
          _showAdAndHandle('email');
          return false;
        }
        return true;
      });
    }
  }

  void _showPhoneWithAd() {
    if (widget.number == null || widget.number!.isEmpty) return;
    _pendingAction = 'phone';
    if (_adWatchedThisSession) {
      _pendingAction = 'phone';
      _executePostAdAction(userEarnedReward: true);
      return;
    }
    if (_isRewardedInterstitialAdLoaded && _rewardedInterstitialAd != null) {
      _showAdAndHandle('phone');
    } else {
      _showLoadingOverlay();
      Future.doWhile(() async {
        if (_isDisposed) return false;
        await Future.delayed(const Duration(milliseconds: 300));
        if (_isRewardedInterstitialAdLoaded && _rewardedInterstitialAd != null) {
          _hideLoadingOverlay();
          _showAdAndHandle('phone');
          return false;
        }
        return true;
      });
    }
  }

  void _showAdAndHandle(String action) {
    if (_rewardedInterstitialAd == null) return;
    _adWatchedThisSession = true;
    _rewardedInterstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _rewardedInterstitialAd = null;
        _isRewardedInterstitialAdLoaded = false;
        _loadRewardedInterstitialAd();
        if (action == _pendingAction) {
          _executePostAdAction(userEarnedReward: false);
        }
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        ad.dispose();
        _rewardedInterstitialAd = null;
        _isRewardedInterstitialAdLoaded = false;
        _loadRewardedInterstitialAd();
        if (action == _pendingAction) {
          _executePostAdAction(userEarnedReward: false);
        }
      },
    );
    _rewardedInterstitialAd!.show(onUserEarnedReward: (ad, reward) {
      if (action == _pendingAction) {
        _executePostAdAction(userEarnedReward: true);
      }
    });
    _rewardedInterstitialAd = null;
    _isRewardedInterstitialAdLoaded = false;
  }

  // تنفيذ الإجراء المعلق بعد مشاهدة الإعلان
  void _executePostAdAction({bool userEarnedReward = false}) {
    if (_pendingAction == null) return;

    // تنفيذ الإجراء فقط إذا أكمل المستخدم مشاهدة الإعلان
    if (userEarnedReward) {
      if (_pendingAction == 'email' && widget.email != null && widget.email!.isNotEmpty) {
        _launchEmail(widget.title, widget.email!);
      } else if (_pendingAction == 'phone' && widget.number != null && widget.number!.isNotEmpty) {
        _launchPhone(widget.number!);
      }
    } else {
      // إذا لم يكمل المستخدم مشاهدة الإعلان، عرض رسالة توضيحية
      AppMessages.showInfo('يجب مشاهدة الإعلان كاملاً لعرض معلومات الاتصال');
    }

    // إعادة تعيين الإجراء المعلق
    _pendingAction = null;
  }

  // فتح تطبيق الهاتف
  void _launchPhone(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await launchUrl(phoneUri, mode: LaunchMode.externalApplication)) {
        // تم فتح تطبيق الهاتف بنجاح
      } else {
        // إذا لم يكن هناك تطبيق هاتف، عرض رسالة للمستخدم فقط
        if (mounted) {
          AppMessages.showError('لا يمكن الاتصال بالرقم: $phoneNumber');
        }
      }
    } catch (e) {
      // معالجة أي استثناءات أخرى
      if (mounted) {
        AppMessages.showError('حدث خطأ في فتح تطبيق الهاتف');
      }
    }
  }

  // ترميز معلمات الاستعلام للبريد الإلكتروني
  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  // فتح تطبيق البريد الإلكتروني
  void _launchEmail(String title, String email) async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: email,
      query: encodeQueryParameters(<String, String>{
        'subject': 'استفسار حول وظيفة: $title'
      }),
    );

    try {
      if (await launchUrl(emailLaunchUri, mode: LaunchMode.externalApplication)) {
        // تم فتح تطبيق البريد بنجاح
      } else {
        // إذا لم يكن هناك تطبيق بريد إلكتروني، عرض رسالة للمستخدم فقط
        if (mounted) {
          AppMessages.showError('لا يوجد تطبيق بريد إلكتروني مثبت. البريد الإلكتروني: $email');
        }
      }
    } catch (e) {
      // معالجة أي استثناءات أخرى
      if (mounted) {
        AppMessages.showError('حدث خطأ في فتح البريد الإلكتروني');
      }
    }
  }

  Widget _buildJobHeader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
        borderRadius: isDarkMode ? const BorderRadius.only(
          topLeft: Radius.circular(0),
          topRight: Radius.circular(0),
        ): const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: GoogleFonts.tajawal(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.business,
                size: 16,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  widget.company_name,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    color: isDarkMode ? Theme.of(context).textTheme.bodyMedium?.color : Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 16,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  "${widget.city_name}, ${widget.state_name}, ${widget.country_name}",
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: isDarkMode ? Theme.of(context).textTheme.bodySmall?.color : Colors.grey[700],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  "نشر ${widget.created_at_date} · ${widget.time}",
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: isDarkMode ? Theme.of(context).textTheme.bodySmall?.color : Colors.grey[700],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildJobHighlights() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "معلومات الوظيفة",
            style: GoogleFonts.tajawal(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHighlightItem(
                Icons.work_outline,
                "نوع العمل",
                widget.job_type_name,
              ),
              _buildHighlightItem(
                Icons.monetization_on,
                "الراتب",
                "${widget.salary ?? 'غير محدد'} ${widget.salary != null ? widget.salary_currency : ''}",
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              _buildHighlightItem(
                Icons.category_outlined,
                "القسم",
                widget.cat,
              ),
              _buildHighlightItem(
                Icons.person_outline,
                "الجنس",
                widget.gender,
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              _buildHighlightItem(
                Icons.school_outlined,
                "التعليم",
                widget.edu,
              ),
              _buildHighlightItem(
                Icons.workspace_premium_rounded,
                "الخبرة",
                widget.exp,
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              _buildHighlightItem(
                Icons.calendar_month,
                "تاريخ النشر",
                widget.created_at_date,
              ),
              _buildHighlightItem(
                Icons.timelapse,
                "تاريخ الانتهاء",
                widget.end_at,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHighlightItem(IconData icon, String label, String value) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 18,
            color: primaryColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Theme.of(context).textTheme.bodyMedium?.color : Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyInfo() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundImage: const AssetImage("assets/company-ava.png"),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.company_name,
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Theme.of(context).textTheme.titleLarge?.color : Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 14,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        "${widget.state_name}, ${widget.country_name}",
                        style: GoogleFonts.tajawal(
                          fontSize: 13,
                          color: isDarkMode ? Theme.of(context).textTheme.bodySmall?.color : Colors.grey[700],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.number != null && widget.number.toString().isNotEmpty && widget.number != "0")
                GestureDetector(
                  onTap: () {
                    _showPhoneWithAd();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: primaryColor.withAlpha(isDarkMode ? 51 : 26), // 0.2 = 51, 0.1 = 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.phone,
                      color: primaryColor,
                      size: 20,
                    ),
                  ),
                ),
              if (widget.number != null && widget.number.toString().isNotEmpty && widget.number != "0")
                const SizedBox(width: 8),
              if (widget.email != null && widget.email.toString().isNotEmpty && widget.email != "0")
                GestureDetector(
                  onTap: () {
                    _showEmailWithAd();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: primaryColor.withAlpha(isDarkMode ? 51 : 26), // 0.2 = 51, 0.1 = 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.email,
                      color: primaryColor,
                      size: 20,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildJobDescription() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "وصف الوظيفة",
            style: GoogleFonts.tajawal(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          HtmlWidget(
            widget.des,
            textStyle: GoogleFonts.tajawal(
              fontSize: 14,
              height: 1.5,
              color: isDarkMode ? Theme.of(context).textTheme.bodyMedium?.color : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          automaticallyImplyLeading: false,
          actions: [
         
            IconButton(
              icon: CircleAvatar(
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
                child: Icon(
                  Icons.report_gmailerrorred_outlined, // أيقونة تبليغ أوضح
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              tooltip: 'تبليغ عن الوظيفة',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ReportJobScreen(
                      jobId: widget.slug,
                    ),
                  ),
                );
              },
            ),
           IconButton(
              icon: CircleAvatar(
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
                child: Icon(
                  Icons.arrow_forward,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
          
          ],
          leading: Row(
            mainAxisSize: MainAxisSize.min, // تقليل حجم الصف إلى الحد الأدنى
            children: [
              IconButton(
                padding: EdgeInsets.zero, // إزالة الـ padding
                constraints: const BoxConstraints(), // إزالة القيود الافتراضية
                icon: CircleAvatar(
                  backgroundColor: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).cardTheme.color
                      : Colors.white,
                  child: Icon(
                    Icons.open_in_browser,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                onPressed: () => _openJobOnSite(widget.slug),
              ),
              IconButton(
                padding: EdgeInsets.zero, // إزالة الـ padding
                constraints: const BoxConstraints(), // إزالة القيود الافتراضية
                icon: CircleAvatar(
                  backgroundColor: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).cardTheme.color
                      : Colors.white,
                  child: Icon(
                    Icons.share,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                onPressed: () => _share(widget.title, widget.slug),
              ),
            ],
          ),
          leadingWidth: 100, // زيادة عرض منطقة leading لاستيعاب الأزرار
        ),
        extendBodyBehindAppBar: true,
        body: Column(
          children: [
            // Background Image with Wave Clipper (only in light mode)
            Theme.of(context).brightness == Brightness.dark
                ? // الوضع الليلي: صورة عادية بدون موجة
                Container(
                    height: 200,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        colorFilter: ColorFilter.mode(
                          Colors.black.withOpacity(0.5),
                          BlendMode.overlay,
                        ),
                        image: getCountryBg(widget.country_name),
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                : // الوضع العادي: صورة مع موجة
                ClipPath(
                    clipper: WaveClipper(),
                    child: Container(
                      height: 200,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          colorFilter: ColorFilter.mode(
                            Colors.black.withOpacity(0.2),
                            BlendMode.overlay,
                          ),
                          image: getCountryBg(widget.country_name),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),

            // Content
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).scaffoldBackgroundColor
                      : const Color(0xffF5F7FA),
                ),
                child: Directionality(
                  textDirection: TextDirection.rtl,
                  child: ListView(
                    padding: const EdgeInsets.only(top: 0, bottom: 16),
                    children: [
                      // إعلان البانر العلوي
                      if (_topBannerAd != null)
                        Container(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          alignment: Alignment.center,
                          width: _topBannerAd!.size.width.toDouble(),
                          height: _topBannerAd!.size.height.toDouble(),
                          decoration: BoxDecoration(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.grey[800]
                                : Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: AdWidget(ad: _topBannerAd!),
                        ),
                      _buildJobHeader(),
                      _buildJobHighlights(),

                      // إعلان البانر المتوسط
                      if (_middleBannerAd != null)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          alignment: Alignment.center,
                          width: _middleBannerAd!.size.width.toDouble(),
                          height: _middleBannerAd!.size.height.toDouble(),
                          decoration: BoxDecoration(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Theme.of(context).cardTheme.color
                                : Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(Theme.of(context).brightness == Brightness.dark ? 51 : 13), // 0.2 = 51, 0.05 = 13
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: AdWidget(ad: _middleBannerAd!),
                        ),

                      _buildCompanyInfo(),
                      _buildJobDescription(),

                      // إعلان البانر السفلي
                      if (_bottomBannerAd != null)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          alignment: Alignment.center,
                          width: _bottomBannerAd!.size.width.toDouble(),
                          height: _bottomBannerAd!.size.height.toDouble(),
                          decoration: BoxDecoration(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Theme.of(context).cardTheme.color
                                : Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(Theme.of(context).brightness == Brightness.dark ? 51 : 13), // 0.2 = 51, 0.05 = 13
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: AdWidget(ad: _bottomBannerAd!),
                        ),

                      const SizedBox(height: 70), // Space for bottom buttons
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        // Bottom Action Buttons
        bottomSheet: Directionality(
          textDirection: TextDirection.rtl,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).cardTheme.color
                  : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -3),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 4,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => JobApplicationForm(
                            slug: widget.slug,
                            title: widget.title,
                            des: widget.des,
                            code_address: widget.code_address,
                            email: widget.email,
                            number: widget.number,
                            salary_currency: widget.salary_currency,
                            salary: widget.salary,
                            cat: widget.cat,
                            gender: widget.gender,
                            state_name: widget.state_name,
                            country_name: widget.country_name,
                            job_type_name: widget.job_type_name,
                            city_name: widget.city_name,
                            company_name: widget.company_name,
                            edu: widget.edu,
                            exp: widget.exp,
                            end_at: widget.end_at,
                            created_at_date: widget.created_at_date,
                            time: widget.time,
                          ),
                        ),
                      );
                    },
                    child: Container(
                      height: 45,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          "تقدم الآن",
                          style: GoogleFonts.tajawal(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 1,
                  child: GestureDetector(
                    onTap: _toggleFavorite,
                    child: Container(
                      height: 45,
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).cardTheme.color
                            : Colors.white,
                        border: Border.all(color: Theme.of(context).colorScheme.primary),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Icon(
                          isFavorite ? Icons.bookmark : Icons.bookmark_border,
                          color: Theme.of(context).colorScheme.primary,
                          size: 22,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

ImageProvider<Object> getCountryBg(String name) {
  switch (name) {
    case "السعودية":
      return const AssetImage("assets/sa.jpg");
    case "مصر":
      return const AssetImage("assets/eg.jpg");
    case "الامارات":
      return const AssetImage("assets/em.jpg");
    case "فلسطين":
      return const AssetImage("assets/pl.jpg");
    case "الأردن":
      return const AssetImage("assets/or.jpg");
    case "البحرين":
      return const AssetImage("assets/bahr.jpg");
    case "الجزائر":
      return const AssetImage("assets/ja.jpg");
    case "العراق":
      return const AssetImage("assets/ir.jpg");
    case "الكويت":
      return const AssetImage("assets/ku.jpg");
    case "المغرب":
      return const AssetImage("assets/mo.jpg");
    case "اليمن":
      return const AssetImage("assets/yeman.jpg");
    case "تونس":
      return const AssetImage("assets/tu.jpg");
    case "قطر":
      return const AssetImage("assets/qa.jpg");
    case "عمان":
      return const AssetImage("assets/oman.jpg");
    case "السودان":
      return const AssetImage("assets/sudan.jpg");
    case "سوريا":
      return const AssetImage("assets/syria.jpg");
    case "لبنان":
      return const AssetImage("assets/lebanon.jpg");
    case "ليبيا":
      return const AssetImage("assets/libya.jpg");
    default:
      return const AssetImage("assets/bac.jpg");
  }
}


















