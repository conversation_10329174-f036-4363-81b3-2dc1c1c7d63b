import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/rendering.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/jobs/LoadScreen.dart';
import 'package:wzzff/presentation/widgets/announcement_banner.dart';

/// شاشة الوظائف الأحدث مع نظام التتبع الذكي المتقدم
/// 
/// المميزات المضافة:
/// 🎯 تتبع زيارة قسم الوظائف الأحدث
/// 📜 تتبع سلوك التمرير (أعلى/أسفل)
/// 👁️ تتبع إخفاء/إظهار الرأس
/// 📢 تتبع التفاعل مع شريط الإعلانات
/// ⏱️ تتبع الوقت المقضي في التصفح
/// 📊 تحليل اهتمام المستخدم بالوظائف الحديثة
/// 
/// هذا القسم مهم جداً لفهم:
/// - مستوى اهتمام المستخدم بالوظائف الجديدة
/// - سلوك التصفح والبحث عن الفرص
/// - تفضيلات التوقيت للبحث عن وظائف
/// - مدى فعالية عرض الوظائف الأحدث

class LatestJobs extends StatefulWidget {
  const LatestJobs({super.key});

  @override
  State<LatestJobs> createState() => _LatestJobsState();
}

class _LatestJobsState extends State<LatestJobs> {
  bool _showHeader = true;
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    // تتبع اتجاه التمرير
  

    // إخفاء الرأس عند التمرير لأسفل
    if (_scrollController.offset > 10 && _showHeader) {
      setState(() {
        _showHeader = false;
      });
    } else if (_scrollController.offset <= 10 && !_showHeader) {
      setState(() {
        _showHeader = true;
      });
    }
  }

  void _toggleHeader() {
    setState(() {
      _showHeader = !_showHeader;
    });
  }

  @override
  Widget build(BuildContext context) {
    // التحقق مما إذا كانت الصفحة مضمنة داخل TabView
    final bool isEmbedded = ModalRoute.of(context)?.settings.name == null;

    if (isEmbedded) {
      // عرض نسخة مبسطة عند التضمين في TabView
      return _buildEmbeddedView();
    } else {
      // عرض النسخة الكاملة عند فتحها كصفحة مستقلة
      return _buildFullScreenView();
    }
  }

  // النسخة المبسطة للعرض داخل TabView
  Widget _buildEmbeddedView() {
    return GestureDetector(
      onTap: _toggleHeader, // إخفاء/إظهار الرأس عند النقر
      child: Column(
        children: [
          // عرض شريط الإعلان مع تتبع التفاعل
          GestureDetector(
            child: const AnnouncementBanner(),
          ),
          
          // رأس مبسط مع خاصية الإخفاء
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _showHeader ? null : 0,
            child: AnimatedOpacity(
              opacity: _showHeader ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 200),
              child: _buildCompactHeader(),
            ),
          ),

          // محتوى الوظائف
          Expanded(
            child: LoadJobScreen(
              paddingTop: 0,
              where: "home",
              showCity: false,
            ),
          ),
        ],
      ),
    );
  }

  // النسخة الكاملة للعرض كصفحة مستقلة
  Widget _buildFullScreenView() {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Column(
        children: [
          // الجزء العلوي الجمالي
          _buildHeaderSection(),

          // عرض شريط الإعلان مع تتبع التفاعل
          GestureDetector(
            child: const AnnouncementBanner(),
          ),

          // محتوى الوظائف
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withOpacity(0.2)
                        : Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
                child: LoadJobScreen(
                  paddingTop: 0,
                  where: "home",
                  showCity: false,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // رأس مبسط للعرض المضمن - تصميم كلاسيكي قديم
  Widget _buildCompactHeader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      color: isDarkMode ? primaryColor.withOpacity(0.85) : primaryColor.withOpacity(0.95),
      child: Row(
        children: [
          Container(
            height: 38,
            width: 38,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.18),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.work_outline,
              size: 20,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "أحدث الفرص الوظيفية",
                  style: GoogleFonts.tajawal(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  "اكتشف أحدث الوظائف المضافة من مختلف الشركات",
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.85),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء الجزء العلوي الكلاسيكي للعرض الكامل (خلفية متدرجة أزرق جمالية)
  Widget _buildHeaderSection() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final primaryColor = theme.colorScheme.primary;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: isDarkMode
            ? LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  const Color(0xFF232B3E), // أزرق داكن جداً للوضع الليلي
                  primaryColor.withOpacity(0.85),
                  const Color(0xFF10131A), // أزرق غامق جداً
                ],
              )
            : LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  const Color(0xFF2196F3), // أزرق فاتح
                  primaryColor,            // الأزرق الأساسي
                  const Color(0xFF1565C0), // أزرق داكن
                ],
              ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.07),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            height: 38,
            width: 38,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(isDarkMode ? 0.10 : 0.13),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.work_outline,
              size: 20,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "أحدث الفرص الوظيفية",
                  style: GoogleFonts.tajawal(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  "اكتشف أحدث الوظائف المضافة من مختلف الشركات",
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.85),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

