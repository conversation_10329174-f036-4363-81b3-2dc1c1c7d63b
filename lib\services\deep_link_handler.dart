import 'package:flutter/material.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import '../Apis/NewsApi.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/models/JobNewsModel.dart';
import 'package:wzzff/models/NewsModel.dart';
import '../presentation/screens/articles/ArtcalPage.dart';
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';
import 'package:wzzff/presentation/screens/jobs_news/job_news_show.dart';
import 'package:wzzff/services/deep_link_service.dart';
import 'package:fluttertoast/fluttertoast.dart';

class DeepLinkHandler {
  final DeepLinkService _deepLinkService = DeepLinkService();
  final GlobalKey<NavigatorState> navigatorKey;

  String? _pendingDeepLink; // لتخزين الرابط مؤقتًا

  DeepLinkHandler({required this.navigatorKey});

  void initialize() {
    _deepLinkService.initialize();
    _listenToDeepLinks();
  }

  void _listenToDeepLinks() {
    _deepLinkService.deepLinkStream.listen((String link) {
      if (navigatorKey.currentState == null) {
        _pendingDeepLink = link;
      } else {
        _handleDeepLink(link);
      }
    });
  }

  void processPendingDeepLink() {
    if (_pendingDeepLink != null && navigatorKey.currentState != null) {
      _handleDeepLink(_pendingDeepLink!);
      _pendingDeepLink = null;
    }
  }

  Future<void> _handleDeepLink(String link) async {
    debugPrint('معالجة الرابط العميق: $link');

    try {
      // التحقق من نوع الرابط (وظيفة أو مقال أو خبر وظيفة)
      final int? jobId = _deepLinkService.parseJobId(link);
      final int? articleId = _deepLinkService.parseArticleId(link);
      final int? jobNewsId = _deepLinkService.parseJobNewsId(link);

      debugPrint('تم استخراج معرف الوظيفة: $jobId، معرف المقال: $articleId، معرف خبر الوظيفة: $jobNewsId');

      if (jobId != null) {
        debugPrint('التنقل إلى تفاصيل الوظيفة برقم: $jobId');
        await _navigateToJobDetails(jobId);
      } else if (articleId != null) {
        debugPrint('التنقل إلى تفاصيل المقال برقم: $articleId');
        await _navigateToArticleDetails(articleId);
      } else if (jobNewsId != null) {
        debugPrint('التنقل إلى تفاصيل خبر الوظيفة برقم: $jobNewsId');
        await _navigateToJobNewsDetails(jobNewsId);
      } else {
        debugPrint('رابط غير معروف: $link');
        Fluttertoast.showToast(
          msg: "رابط غير صالح",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e) {
      debugPrint('خطأ أثناء معالجة الرابط العميق: $e');
      Fluttertoast.showToast(
        msg: "حدث خطأ أثناء معالجة الرابط",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  Future<void> _navigateToJobDetails(int jobId) async {
    try {
      // تأكد من جاهزية navigatorKey
      int retries = 0;
      while (navigatorKey.currentState == null && retries < 5) {
        await Future.delayed(const Duration(milliseconds: 200));
        retries++;
      }

      _showLoadingDialog();

      final JobsApi jobsApi = JobsApi();
      final JobModel? job = await jobsApi.getJobById(jobId);

      _dismissLoadingDialog();

      if (job != null) {
        navigatorKey.currentState?.push(
          MaterialPageRoute(
            builder: (context) => DetailJobScreen(
              title: job.title,
              code_address: job.code_address,
              des: job.description,
              cat: job.cat,
              city_name: job.city_name,
              company_name: job.company_name,
              country_name: job.country_name,
              created_at_date: job.created_at_date,
              edu: job.edu,
              email: job.email,
              end_at: job.end_at,
              exp: job.exp,
              gender: job.gender,
              job_type_name: job.job_type_name,
              number: job.number,
              salary: job.salary,
              salary_currency: job.salary_currency,
              slug: job.slug,
              state_name: job.state_name,
              time: job.time,
            ),
          ),
        );
      } else {
        Fluttertoast.showToast(
          msg: "لم يتم العثور على الوظيفة",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e, stack) {
      _dismissLoadingDialog();
      debugPrint('خطأ في جلب بيانات الوظيفة: $e');
      debugPrint('$stack');
      Fluttertoast.showToast(
        msg: "حدث خطأ أثناء تحميل بيانات الوظيفة",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  Future<void> _navigateToArticleDetails(int articleId) async {
    try {
      // عرض مؤشر التحميل
      _showLoadingDialog();

      // جلب بيانات المقال من API
      final NewsApi newsApi = NewsApi();
      final NewsModel? article = await newsApi.getArticleById(articleId);

      // إغلاق مؤشر التحميل
      _dismissLoadingDialog();

      if (article != null) {
        // التنقل إلى صفحة تفاصيل المقال
        navigatorKey.currentState?.push(
          MaterialPageRoute(
            builder: (context) => ArtcalPage(data: article),
          ),
        );
      } else {
        Fluttertoast.showToast(
          msg: "لم يتم العثور على المقال",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e) {
      _dismissLoadingDialog();
      debugPrint('خطأ في جلب بيانات المقال: $e');
      Fluttertoast.showToast(
        msg: "حدث خطأ أثناء تحميل بيانات المقال",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  Future<void> _navigateToJobNewsDetails(int jobNewsId) async {
    try {
      // عرض مؤشر التحميل
      _showLoadingDialog();

      // البحث عن خبر الوظيفة في القائمة المؤقتة (يمكن استبدالها بطلب API في المستقبل)
      // نستخدم القائمة المؤقتة dummyJobNews من ملف JobNewsModel.dart
      final jobNews = dummyJobNews.firstWhere(
        (news) => news.id == jobNewsId,
        orElse: () => JobNewsModel(
          id: -1,
          title: "",
          description: "",
          image: "",
          source: "",
          sourceImage: "",
          date: "",
          url: "",
        ),
      );

      // إغلاق مؤشر التحميل
      _dismissLoadingDialog();

      if (jobNews.id != -1) {
        // التنقل إلى صفحة تفاصيل خبر الوظيفة
        navigatorKey.currentState?.push(
          MaterialPageRoute(
            builder: (context) => JobNewsShowScreen(news: jobNews),
          ),
        );
      } else {
        Fluttertoast.showToast(
          msg: "لم يتم العثور على خبر الوظيفة",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e) {
      _dismissLoadingDialog();
      debugPrint('خطأ في جلب بيانات خبر الوظيفة: $e');
      Fluttertoast.showToast(
        msg: "حدث خطأ أثناء تحميل بيانات خبر الوظيفة",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  void _showLoadingDialog() {
    showDialog(
      context: navigatorKey.currentContext!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }

  void _dismissLoadingDialog() {
    if (navigatorKey.currentContext != null) {
      Navigator.of(navigatorKey.currentContext!, rootNavigator: true).pop();
    }
  }
}
