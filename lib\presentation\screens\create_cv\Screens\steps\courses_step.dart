import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:wzzff/presentation/screens/create_cv/Components/FieldTxt.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';

class CoursesStep extends StatefulWidget {
  final CvModel cvData;
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic> currentData;
  final List<Map<String, dynamic>> coursesList;
  final Function(List<Map<String, dynamic>>) onCoursesListChanged;

  const CoursesStep({
    Key? key,
    required this.cvData,
    required this.onDataChanged,
    required this.currentData,
    required this.coursesList,
    required this.onCoursesListChanged,
  }) : super(key: key);

  @override
  State<CoursesStep> createState() => _CoursesStepState();
}

class _CoursesStepState extends State<CoursesStep> {
  late TextEditingController _courseTitleController;
  late TextEditingController _courseDesController;
  late TextEditingController _coursePlaceNameController;

  String? _courseFrom;
  String? _courseEnd;
  int? _editingIndex;

  final List<String> yearsOption = [for (int i = 1980; i <= 2025; i++) i.toString()];

  @override
  void initState() {
    super.initState();
    _courseTitleController = TextEditingController(text: widget.currentData['course_title']);
    _courseDesController = TextEditingController(text: widget.currentData['course_des']);
    _coursePlaceNameController = TextEditingController(text: widget.currentData['course_place_name']);

    _courseFrom = widget.currentData['course_from'];
    _courseEnd = widget.currentData['course_end'];
  }

  @override
  void dispose() {
    _courseTitleController.dispose();
    _courseDesController.dispose();
    _coursePlaceNameController.dispose();
    super.dispose();
  }

  void _updateData(String field, String? value) {
    final newData = Map<String, dynamic>.from(widget.currentData);
    newData[field] = value;
    widget.onDataChanged(newData);
  }

  void _addCourse() {
    if (_courseFrom == null || _courseEnd == null ||
        _courseTitleController.text.isEmpty ||
        _courseDesController.text.isEmpty ||
        _coursePlaceNameController.text.isEmpty) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(Lang().getWord("fill_all_fields", widget.cvData.ltf)),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final course = {
      "course_from": _courseFrom,
      "course_end": _courseEnd,
      "course_title": _courseTitleController.text,
      "course_des": _courseDesController.text,
      "course_place_name": _coursePlaceNameController.text,
    };

    final newList = List<Map<String, dynamic>>.from(widget.coursesList);

    if (_editingIndex != null) {
      newList[_editingIndex!] = course;
      _editingIndex = null;
    } else {
      newList.add(course);
    }

    widget.onCoursesListChanged(newList);

    // إعادة تعيين الحقول
    _courseTitleController.clear();
    _courseDesController.clear();
    _coursePlaceNameController.clear();
    setState(() {
      _courseFrom = null;
      _courseEnd = null;
    });
  }

  void _editCourse(int index) {
    final course = widget.coursesList[index];

    setState(() {
      _courseFrom = course["course_from"];
      _courseEnd = course["course_end"];
      _courseTitleController.text = course["course_title"];
      _courseDesController.text = course["course_des"];
      _coursePlaceNameController.text = course["course_place_name"];
      _editingIndex = index;
    });
  }

  void _deleteCourse(int index) {
    final newList = List<Map<String, dynamic>>.from(widget.coursesList);
    newList.removeAt(index);
    widget.onCoursesListChanged(newList);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // إذا تم العودة من الخطوة التالية، احذف آخر دورة من المصفوفة وأعد تعبئة الحقول
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map && args['action'] == 'back_step' && widget.coursesList.isNotEmpty) {
      final lastCourse = widget.coursesList.last;
      widget.onCoursesListChanged(List<Map<String, dynamic>>.from(widget.coursesList)..removeLast());
      setState(() {
        _courseFrom = lastCourse["course_from"];
        _courseEnd = lastCourse["course_end"];
        _courseTitleController.text = lastCourse["course_title"];
        _courseDesController.text = lastCourse["course_des"];
        _coursePlaceNameController.text = lastCourse["course_place_name"];
        _editingIndex = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final cardColor = Theme.of(context).cardColor;
    final textColor = Theme.of(context).textTheme.bodyLarge?.color;

    return Directionality(
      textDirection: widget.cvData.ltf == "0" ? TextDirection.rtl : TextDirection.ltr,
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [primaryColor, primaryColor.withOpacity(0.7)],
                ),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.10),
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const Icon(Icons.book, color: Colors.white, size: 22),
                  const SizedBox(width: 8),
                  Text(
                    Lang().getWord("courses", widget.cvData.ltf),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 14),

            // قائمة الدورات المضافة
            if (widget.coursesList.isNotEmpty) ...[
              Text(
                Lang().getWord("added_courses", widget.cvData.ltf),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
              const SizedBox(height: 10),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: widget.coursesList.length,
                itemBuilder: (context, index) {
                  final course = widget.coursesList[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 10),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      title: Text(
                        course["course_title"],
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(course["course_place_name"]),
                          Text("${course["course_from"]} - ${course["course_end"]}"),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editCourse(index),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _deleteCourse(index),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              const Divider(height: 30),
            ],

            // نموذج إضافة دورة جديدة
            Text(
              _editingIndex != null
                  ? Lang().getWord("edit_course", widget.cvData.ltf)
                  : Lang().getWord("add_new_course", widget.cvData.ltf),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 16),

            // سنة البداية
            Text(
              "${Lang().getWord("c_from", widget.cvData.ltf)}*",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 8),
            FormBuilderDropdown(
              name: 'course_from',
              initialValue: _courseFrom,
              decoration: InputDecoration(
                filled: true,
                fillColor: cardColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                prefixIcon: const Icon(Icons.calendar_today),
                hintText: Lang().getWord("choose", widget.cvData.ltf),
              ),
              items: yearsOption
                  .map((year) => DropdownMenuItem(
                        value: year,
                        child: Text(year),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _courseFrom = value;
                });
                _updateData('course_from', value);
              },
            ),
            const SizedBox(height: 16),

            // سنة النهاية
            Text(
              "${Lang().getWord("c_end", widget.cvData.ltf)}*",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 8),
            FormBuilderDropdown(
              name: 'course_end',
              initialValue: _courseEnd,
              decoration: InputDecoration(
                filled: true,
                fillColor: cardColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                prefixIcon: const Icon(Icons.calendar_today),
                hintText: Lang().getWord("choose", widget.cvData.ltf),
              ),
              items: yearsOption
                  .map((year) => DropdownMenuItem(
                        value: year,
                        child: Text(year),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _courseEnd = value;
                });
                _updateData('course_end', value);
              },
            ),
            const SizedBox(height: 16),

            // اسم الدورة
            FieldTxt(
              fieldLabel: Lang().getWord("c_title", widget.cvData.ltf),
              handlerInput: (value) => _updateData('course_title', value),
              fieldname: 'course_title',
              hint: "....",
              iconField: const Icon(Icons.school),
              inputRequired: widget.cvData.course_title == 1,
              textEditingController: _courseTitleController,
            ),
            const SizedBox(height: 16),

            // وصف الدورة
            Text(
              Lang().getWord("c_des", widget.cvData.ltf),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _courseDesController,
              maxLines: 3,
              style: Theme.of(context).textTheme.bodyLarge,
              decoration: InputDecoration(
                filled: isDarkMode,
                fillColor: isDarkMode ? Theme.of(context).inputDecorationTheme.fillColor : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: primaryColor, width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: isDarkMode ? Colors.red[300]! : Colors.red),
                ),
                hintText: Lang().getWord("c_des_hint", widget.cvData.ltf),
                hintStyle: TextStyle(
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  fontSize: 15,
                ),
                contentPadding: const EdgeInsets.all(16),
              ),
              onChanged: (value) => _updateData('course_des', value),
            ),
            const SizedBox(height: 16),

            // مكان الدورة
            FieldTxt(
              fieldLabel: Lang().getWord("c_place_name", widget.cvData.ltf),
              handlerInput: (value) => _updateData('course_place_name', value),
              fieldname: "course_place_name",
              hint: Lang().getWord("c_place_name_hint", widget.cvData.ltf),
              iconField: const Icon(Icons.location_city),
              inputRequired: true,
              textEditingController: _coursePlaceNameController,
            ),
            const SizedBox(height: 24),

            // أزرار الإضافة فقط (بدون زر التالي)
            Row(
              children: [
                // زر الإضافة
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _addCourse();
                      // إضافة رسالة تأكيد عند الإضافة بنجاح
                      if (_courseFrom != null && _courseEnd != null &&
                          _courseTitleController.text.isNotEmpty &&
                          _courseDesController.text.isNotEmpty &&
                          _coursePlaceNameController.text.isNotEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(_editingIndex != null
                                ? Lang().getWord("course_updated", widget.cvData.ltf)
                                : Lang().getWord("course_added", widget.cvData.ltf)),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                            margin: const EdgeInsets.all(16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        );
                      }
                    },
                    icon: Icon(_editingIndex != null ? Icons.save : Icons.add),
                    label: Text(
                      _editingIndex != null
                          ? Lang().getWord("save_changes", widget.cvData.ltf)
                          : Lang().getWord("add_new_course", widget.cvData.ltf),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
