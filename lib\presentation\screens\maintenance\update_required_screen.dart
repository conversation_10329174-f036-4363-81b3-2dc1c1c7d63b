import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/core/utils/app_messages.dart';

/// شاشة التحديث الإجباري
class UpdateRequiredScreen extends StatelessWidget {
  final bool isForceUpdate;

  const UpdateRequiredScreen({
    Key? key,
    required this.isForceUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appStateProvider = Provider.of<AppStateProvider>(context);
    final appState = appStateProvider.appState;
    
    if (appState == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final updateUrl = appStateProvider.getUpdateUrl();
    final updateMessage = isForceUpdate
        ? appState.messages.forceUpdateMessage
        : appState.messages.optionalUpdateMessage;

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // صورة متحركة للتحديث
                Lottie.asset(
                  'assets/update.json',
                  width: 250,
                  height: 250,
                  fit: BoxFit.contain,
                ),
                const SizedBox(height: 32),
                
                // عنوان التحديث
                Text(
                  isForceUpdate ? 'تحديث إجباري مطلوب' : 'تحديث جديد متاح',
                  style: GoogleFonts.tajawal(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                
                // رسالة التحديث
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.shade800
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: isDarkMode
                            ? Colors.black.withOpacity(0.2)
                            : Colors.grey.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Text(
                    updateMessage,
                    style: GoogleFonts.tajawal(
                      fontSize: 16,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),
                
                // زر التحديث
                ElevatedButton.icon(
                  onPressed: () {
                    _launchUpdateUrl(context, updateUrl);
                  },
                  icon: const Icon(Icons.system_update),
                  label: Text(
                    'تحديث التطبيق',
                    style: GoogleFonts.tajawal(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                
                // زر تخطي التحديث (للتحديث الاختياري فقط)
                if (!isForceUpdate) ...[
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'تخطي التحديث',
                      style: GoogleFonts.tajawal(
                        fontSize: 16,
                        color: isDarkMode ? Colors.grey : Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// فتح رابط التحديث
  Future<void> _launchUpdateUrl(BuildContext context, String url) async {
    if (url.isEmpty) {
      AppMessages.showError('رابط التحديث غير متوفر');
      return;
    }

    final Uri uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        AppMessages.showError('لا يمكن فتح رابط التحديث');
      }
    } catch (e) {
      AppMessages.showError('حدث خطأ أثناء فتح رابط التحديث: $e');
    }
  }
}
