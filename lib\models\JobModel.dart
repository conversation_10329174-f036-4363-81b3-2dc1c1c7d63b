import 'package:flutter/foundation.dart';

class JobModel {
  String title;

  String city_name;

  String time;

  String slug;

  String code_address;

  String description;

  String? email;

  String? number;

  String salary_currency;

  String? salary;

  String cat;

  String gender;

  String job_type_name;

  String country_name;

  String state_name;

  String company_name;

  String edu;

  String exp;

  String end_at;

  String created_at_date;

  JobModel({
    required this.title,
    this.email,
    this.number,
    required this.salary_currency,
    this.salary,
    required this.cat,
    required this.gender,
    required this.state_name,
    required this.country_name,
    required this.job_type_name,
    required this.city_name,
    required this.company_name,
    required this.edu,
    required this.exp,
    required this.end_at,
    required this.created_at_date,
    required this.slug,
    required this.time,
    required this.code_address,
    required this.description,
  });

  factory JobModel.fromJson(Map job) {
    return JobModel(
      title: job["title"]?.toString() ?? '',
      time: job["time"]?.toString() ?? job["begin_at"]?.toString() ?? '',
      city_name: job["city_name"]?.toString() ?? '',
      slug: job["slug"]?.toString() ?? '',
      code_address: job["code_address"]?.toString() ?? '',
      description: job["description"]?.toString() ?? '',
      cat: job["cat"]?.toString() ?? '',
      company_name: job["company_name"]?.toString() ?? '',
      country_name: job["country_name"]?.toString() ?? '',
      created_at_date: job["created_at_date"]?.toString() ?? '',
      edu: job["edu"]?.toString() ?? '',
      email: job["email"]?.toString(),
      end_at: job["end_at"]?.toString() ?? '',
      exp: job["exp"]?.toString() ?? '',
      gender: job["gender"]?.toString() ?? '',
      job_type_name: job["job_type_name"]?.toString() ?? '',
      number: job["number"]?.toString(),
      salary: job["salary"]?.toString(),
      salary_currency: job["salary_currency"]?.toString() ?? '',
      state_name: job["state_name"]?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "title": title,
      "time": time,
      "begin_at": time,
      "city_name": city_name,
      "slug": slug,
      "code_address": code_address,
      "description": description,
      "cat": cat,
      "company_name": company_name,
      "country_name": country_name,
      "created_at_date": created_at_date,
      "edu": edu,
      "email": email,
      "end_at": end_at,
      "exp": exp,
      "gender": gender,
      "job_type_name": job_type_name,
      "number": number,
      "salary": salary,
      "salary_currency": salary_currency,
      "state_name": state_name,
    };
  }
}
