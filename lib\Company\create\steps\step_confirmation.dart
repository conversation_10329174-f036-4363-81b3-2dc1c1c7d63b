import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class StepConfirmation extends StatelessWidget {
  const StepConfirmation({Key? key}) : super(key: key);

  // Theme helpers
  bool _isDarkMode(BuildContext context) => Theme.of(context).brightness == Brightness.dark;
  Color _primaryColor(BuildContext context) => Theme.of(context).colorScheme.primary;
  Color _cardColor(BuildContext context) => _isDarkMode(context) ? Colors.grey[850]! : Colors.white;
  Color _secondaryTextColor(BuildContext context) => _isDarkMode(context) ? Colors.grey[300]! : Colors.grey[600]!;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardColor(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _isDarkMode(context) ? Colors.black.withOpacity(0.3) : Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.withOpacity(0.1), Colors.green.withOpacity(0.05)],
              ),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check_circle, color: Colors.white, size: 24),
                ),
                const SizedBox(height: 12),
                Text(
                  'جميع البيانات جاهزة للنشر!',
                  style: GoogleFonts.tajawal(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'بالضغط على "نشر الوظيفة" سيتم إضافة الوظيفة وإتاحتها للمتقدمين.',
                  style: GoogleFonts.tajawal(fontSize: 12, color: _secondaryTextColor(context)),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 