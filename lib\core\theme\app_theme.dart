import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Common colors
  static const Color primaryColor = Color(0xFF2daae2);
  static const Color accentColor = Color(0xFF2daae2);

  // Light theme colors
  static const Color lightBackground = Colors.white;
  static const Color lightSurface = Colors.white;
  static const Color lightCardColor = Colors.white;
  static const Color lightTextPrimary = Colors.black87;
  static const Color lightTextSecondary = Colors.black54;
  static const Color lightDivider = Color(0xFFE0E0E0);

  // Dark theme colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkCardColor = Color(0xFF2A2A2A);
  static const Color darkTextPrimary = Colors.white;
  static const Color darkTextSecondary = Colors.white70;
  static const Color darkDivider = Color(0xFF3A3A3A);

  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: primaryColor,
    colorScheme: const ColorScheme.light(
      primary: primaryColor,
      secondary: accentColor,
      surface: lightSurface,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: lightTextPrimary,
    ),
    scaffoldBackgroundColor: lightBackground,
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      iconTheme: IconThemeData(color: Colors.white),
    ),
    cardTheme: CardTheme(
      color: lightCardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textTheme: TextTheme(
      displayLarge: GoogleFonts.tajawal(color: lightTextPrimary),
      displayMedium: GoogleFonts.tajawal(color: lightTextPrimary),
      displaySmall: GoogleFonts.tajawal(color: lightTextPrimary),
      headlineLarge: GoogleFonts.tajawal(color: lightTextPrimary),
      headlineMedium: GoogleFonts.tajawal(color: lightTextPrimary),
      headlineSmall: GoogleFonts.tajawal(color: lightTextPrimary),
      titleLarge: GoogleFonts.tajawal(color: lightTextPrimary),
      titleMedium: GoogleFonts.tajawal(color: lightTextPrimary),
      titleSmall: GoogleFonts.tajawal(color: lightTextPrimary),
      bodyLarge: GoogleFonts.tajawal(color: lightTextPrimary),
      bodyMedium: GoogleFonts.tajawal(color: lightTextPrimary),
      bodySmall: GoogleFonts.tajawal(color: lightTextSecondary),
      labelLarge: GoogleFonts.tajawal(color: lightTextPrimary),
      labelMedium: GoogleFonts.tajawal(color: lightTextPrimary),
      labelSmall: GoogleFonts.tajawal(color: lightTextSecondary),
    ),
    iconTheme: const IconThemeData(color: primaryColor),
    dividerTheme: const DividerThemeData(color: lightDivider),
    navigationBarTheme: NavigationBarThemeData(
      backgroundColor: lightBackground,
      indicatorColor: primaryColor.withOpacity(0.1),
      labelTextStyle: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return const TextStyle(color: primaryColor);
        }
        return const TextStyle(color: lightTextSecondary);
      }),
      iconTheme: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return const IconThemeData(color: primaryColor);
        }
        return const IconThemeData(color: lightTextSecondary);
      }),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: lightBackground,
      selectedItemColor: primaryColor,
      unselectedItemColor: lightTextSecondary,
    ),
    dialogTheme: DialogTheme(
      backgroundColor: lightBackground,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
    ),
    inputDecorationTheme: InputDecorationTheme(
      fillColor: lightBackground,
      filled: true,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: lightDivider),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: lightDivider),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: primaryColor),
      ),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: primaryColor,
    colorScheme: const ColorScheme.dark(
      primary: primaryColor,
      secondary: accentColor,
      surface: darkSurface,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: darkTextPrimary,
    ),
    scaffoldBackgroundColor: darkBackground,
    appBarTheme: const AppBarTheme(
      backgroundColor: darkSurface,
      foregroundColor: Colors.white,
      elevation: 0,
      iconTheme: IconThemeData(color: Colors.white),
    ),
    cardTheme: CardTheme(
      color: darkCardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textTheme: TextTheme(
      displayLarge: GoogleFonts.tajawal(color: darkTextPrimary),
      displayMedium: GoogleFonts.tajawal(color: darkTextPrimary),
      displaySmall: GoogleFonts.tajawal(color: darkTextPrimary),
      headlineLarge: GoogleFonts.tajawal(color: darkTextPrimary),
      headlineMedium: GoogleFonts.tajawal(color: darkTextPrimary),
      headlineSmall: GoogleFonts.tajawal(color: darkTextPrimary),
      titleLarge: GoogleFonts.tajawal(color: darkTextPrimary),
      titleMedium: GoogleFonts.tajawal(color: darkTextPrimary),
      titleSmall: GoogleFonts.tajawal(color: darkTextPrimary),
      bodyLarge: GoogleFonts.tajawal(color: darkTextPrimary),
      bodyMedium: GoogleFonts.tajawal(color: darkTextPrimary),
      bodySmall: GoogleFonts.tajawal(color: darkTextSecondary),
      labelLarge: GoogleFonts.tajawal(color: darkTextPrimary),
      labelMedium: GoogleFonts.tajawal(color: darkTextPrimary),
      labelSmall: GoogleFonts.tajawal(color: darkTextSecondary),
    ),
    iconTheme: const IconThemeData(color: primaryColor),
    dividerTheme: const DividerThemeData(color: darkDivider),
    navigationBarTheme: NavigationBarThemeData(
      backgroundColor: darkSurface,
      indicatorColor: primaryColor.withOpacity(0.2),
      labelTextStyle: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return const TextStyle(color: primaryColor);
        }
        return const TextStyle(color: darkTextSecondary);
      }),
      iconTheme: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return const IconThemeData(color: primaryColor);
        }
        return const IconThemeData(color: darkTextSecondary);
      }),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: darkSurface,
      selectedItemColor: primaryColor,
      unselectedItemColor: darkTextSecondary,
    ),
    dialogTheme: DialogTheme(
      backgroundColor: darkSurface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
    ),
    inputDecorationTheme: InputDecorationTheme(
      fillColor: darkCardColor,
      filled: true,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: darkDivider),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: darkDivider),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: primaryColor),
      ),
    ),
  );
}
