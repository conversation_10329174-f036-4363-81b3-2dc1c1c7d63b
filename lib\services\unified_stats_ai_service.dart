import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:wzzff/models/JobModel.dart';

/// نظام إحصائيات وذكاء صناعي موحد لجميع الشاشات
class UnifiedStatsAIService {
  static final UnifiedStatsAIService _instance = UnifiedStatsAIService._internal();
  factory UnifiedStatsAIService() => _instance;
  UnifiedStatsAIService._internal();

  /// جلب عدد الوظائف التي تمت مشاهدتها
  Future<int> getViewedJobsCount() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_viewed_jobs');
    if (data == null) return 0;
    final List<dynamic> jobs = json.decode(data);
    return jobs.length;
  }

  /// جلب عدد الوظائف التي تم التقديم لها
  Future<int> getAppliedJobsCount() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_applied_jobs');
    if (data == null) return 0;
    final List<dynamic> jobs = json.decode(data);
    return jobs.length;
  }

  /// جلب عدد عمليات البحث
  Future<int> getSearchesCount() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_searches');
    if (data == null) return 0;
    final List<dynamic> searches = json.decode(data);
    return searches.length;
  }

  /// جلب عدد الوظائف المحفوظة
  Future<int> getSavedJobsCount() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_saved_jobs');
    if (data == null) return 0;
    final List<dynamic> jobs = json.decode(data);
    return jobs.length;
  }

  /// تتبع مشاهدة وظيفة
  Future<void> trackJobView(JobModel job) async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_viewed_jobs');
    final List<dynamic> jobs = data == null ? [] : json.decode(data);
    jobs.add(job.toJson());
    await prefs.setString('unified_viewed_jobs', json.encode(jobs));
  }

  /// تتبع التقديم على وظيفة
  Future<void> trackJobApply(JobModel job) async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_applied_jobs');
    final List<dynamic> jobs = data == null ? [] : json.decode(data);
    jobs.add(job.toJson());
    await prefs.setString('unified_applied_jobs', json.encode(jobs));
  }

  /// تتبع عملية بحث
  Future<void> trackSearch(String term) async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_searches');
    final List<dynamic> searches = data == null ? [] : json.decode(data);
    searches.add({'term': term, 'searched_at': DateTime.now().toIso8601String()});
    await prefs.setString('unified_searches', json.encode(searches));
  }

  /// تتبع حفظ وظيفة
  Future<void> trackSaveJob(JobModel job) async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('unified_saved_jobs');
    final List<dynamic> jobs = data == null ? [] : json.decode(data);
    jobs.add(job.toJson());
    await prefs.setString('unified_saved_jobs', json.encode(jobs));
  }
} 