import 'package:wzzff/models/JobModel.dart';

/// نموذج نتيجة تطابق الذكاء الاصطناعي للوظائف
class AIMatchResult {
  final JobModel job;
  final double matchScore;
  final String explanation;
  final List<String> relevanceFactors;
  final MatchCategory category;
  final Map<String, double> scoreBreakdown;
  final List<String> recommendations;
  final DateTime analyzedAt;

  AIMatchResult({
    required this.job,
    required this.matchScore,
    required this.explanation,
    required this.relevanceFactors,
    this.category = MatchCategory.good,
    this.scoreBreakdown = const {},
    this.recommendations = const [],
    DateTime? analyzedAt,
  }) : analyzedAt = analyzedAt ?? DateTime.now();

  factory AIMatchResult.fromJson(Map<String, dynamic> json) {
    return AIMatchResult(
      job: JobModel.fromJson(json['job'] ?? {}),
      matchScore: (json['match_score'] ?? 0.0).toDouble(),
      explanation: json['explanation']?.toString() ?? '',
      relevanceFactors: List<String>.from((json['relevance_factors'] ?? []).map((e) => e?.toString() ?? '')),
      category: MatchCategory.values.firstWhere(
        (e) => e.toString() == 'MatchCategory.${json['category']}',
        orElse: () => MatchCategory.good,
      ),
      scoreBreakdown: Map<String, double>.from(
        (json['score_breakdown'] ?? {}).map(
          (key, value) => MapEntry(key?.toString() ?? '', (value ?? 0.0).toDouble()),
        ),
      ),
      recommendations: List<String>.from((json['recommendations'] ?? []).map((e) => e?.toString() ?? '')),
      analyzedAt: DateTime.tryParse(json['analyzed_at']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'job': job.toJson(),
      'match_score': matchScore,
      'explanation': explanation,
      'relevance_factors': relevanceFactors,
      'category': category.toString().split('.').last,
      'score_breakdown': scoreBreakdown,
      'recommendations': recommendations,
      'analyzed_at': analyzedAt.toIso8601String(),
    };
  }

  AIMatchResult copyWith({
    JobModel? job,
    double? matchScore,
    String? explanation,
    List<String>? relevanceFactors,
    MatchCategory? category,
    Map<String, double>? scoreBreakdown,
    List<String>? recommendations,
    DateTime? analyzedAt,
  }) {
    return AIMatchResult(
      job: job ?? this.job,
      matchScore: matchScore ?? this.matchScore,
      explanation: explanation ?? this.explanation,
      relevanceFactors: relevanceFactors ?? this.relevanceFactors,
      category: category ?? this.category,
      scoreBreakdown: scoreBreakdown ?? this.scoreBreakdown,
      recommendations: recommendations ?? this.recommendations,
      analyzedAt: analyzedAt ?? this.analyzedAt,
    );
  }

  /// الحصول على معرف الوظيفة الفريد
  String get jobId => job.slug;

  /// الحصول على اسم الشركة
  String? get company => job.company_name;

  /// الحصول على نسبة التطابق كنص
  String get matchPercentage => '${(matchScore * 100).round()}%';

  /// التحقق من كون التطابق عالياً
  bool get isHighMatch => matchScore >= 0.8;

  /// التحقق من كون التطابق متوسطاً
  bool get isMediumMatch => matchScore >= 0.5 && matchScore < 0.8;

  /// التحقق من كون التطابق ضعيفاً
  bool get isLowMatch => matchScore < 0.5;

  /// الحصول على اللون المناسب للدرجة
  String get matchColor {
    if (isHighMatch) return '#4CAF50'; // أخضر
    if (isMediumMatch) return '#FF9800'; // برتقالي
    return '#F44336'; // أحمر
  }

  /// الحصول على أيقونة التطابق
  String get matchIcon {
    if (isHighMatch) return '��';
    if (isMediumMatch) return '✅';
    return '📌';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AIMatchResult &&
        other.job.slug == job.slug &&
        other.matchScore == matchScore;
  }

  @override
  int get hashCode => job.slug.hashCode ^ matchScore.hashCode;

  @override
  String toString() {
    return 'AIMatchResult(jobSlug: ${job.slug}, matchScore: $matchScore, category: $category)';
  }
}

/// تصنيفات التطابق
enum MatchCategory {
  /// تطابق ممتاز (80% فأكثر)
  excellent,
  
  /// تطابق جيد جداً (70-79%)
  veryGood,
  
  /// تطابق جيد (60-69%)
  good,
  
  /// تطابق متوسط (40-59%)
  average,
  
  /// تطابق ضعيف (أقل من 40%)
  poor,
}

/// امتداد لتسهيل العمل مع تصنيفات التطابق
extension MatchCategoryExtension on MatchCategory {
  /// الحصول على الوصف باللغة العربية
  String get arabicName {
    switch (this) {
      case MatchCategory.excellent:
        return 'ممتاز';
      case MatchCategory.veryGood:
        return 'جيد جداً';
      case MatchCategory.good:
        return 'جيد';
      case MatchCategory.average:
        return 'متوسط';
      case MatchCategory.poor:
        return 'ضعيف';
    }
  }

  /// الحصول على اللون المناسب
  String get color {
    switch (this) {
      case MatchCategory.excellent:
        return '#4CAF50'; // أخضر غامق
      case MatchCategory.veryGood:
        return '#8BC34A'; // أخضر فاتح
      case MatchCategory.good:
        return '#CDDC39'; // أخضر ليموني
      case MatchCategory.average:
        return '#FF9800'; // برتقالي
      case MatchCategory.poor:
        return '#F44336'; // أحمر
    }
  }

  /// الحصول على الأيقونة المناسبة
  String get icon {
    switch (this) {
      case MatchCategory.excellent:
        return '🏆';
      case MatchCategory.veryGood:
        return '🎯';
      case MatchCategory.good:
        return '✅';
      case MatchCategory.average:
        return '📌';
      case MatchCategory.poor:
        return '❔';
    }
  }

  /// الحصول على التصنيف من الدرجة
  static MatchCategory fromScore(double score) {
    if (score >= 0.8) return MatchCategory.excellent;
    if (score >= 0.7) return MatchCategory.veryGood;
    if (score >= 0.6) return MatchCategory.good;
    if (score >= 0.4) return MatchCategory.average;
    return MatchCategory.poor;
  }

  /// الحصول على الحد الأدنى للدرجة
  double get minScore {
    switch (this) {
      case MatchCategory.excellent:
        return 0.8;
      case MatchCategory.veryGood:
        return 0.7;
      case MatchCategory.good:
        return 0.6;
      case MatchCategory.average:
        return 0.4;
      case MatchCategory.poor:
        return 0.0;
    }
  }

  /// الحصول على الحد الأقصى للدرجة
  double get maxScore {
    switch (this) {
      case MatchCategory.excellent:
        return 1.0;
      case MatchCategory.veryGood:
        return 0.799;
      case MatchCategory.good:
        return 0.699;
      case MatchCategory.average:
        return 0.599;
      case MatchCategory.poor:
        return 0.399;
    }
  }
}

/// نموذج تفصيل درجات التطابق
class MatchScoreBreakdown {
  final double titleMatch;
  final double descriptionMatch;
  final double skillsMatch;
  final double experienceMatch;
  final double locationMatch;
  final double salaryMatch;

  const MatchScoreBreakdown({
    this.titleMatch = 0.0,
    this.descriptionMatch = 0.0,
    this.skillsMatch = 0.0,
    this.experienceMatch = 0.0,
    this.locationMatch = 0.0,
    this.salaryMatch = 0.0,
  });

  factory MatchScoreBreakdown.fromJson(Map<String, dynamic> json) {
    return MatchScoreBreakdown(
      titleMatch: (json['title_match'] ?? 0.0).toDouble(),
      descriptionMatch: (json['description_match'] ?? 0.0).toDouble(),
      skillsMatch: (json['skills_match'] ?? 0.0).toDouble(),
      experienceMatch: (json['experience_match'] ?? 0.0).toDouble(),
      locationMatch: (json['location_match'] ?? 0.0).toDouble(),
      salaryMatch: (json['salary_match'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title_match': titleMatch,
      'description_match': descriptionMatch,
      'skills_match': skillsMatch,
      'experience_match': experienceMatch,
      'location_match': locationMatch,
      'salary_match': salaryMatch,
    };
  }

  /// الحصول على متوسط الدرجات
  double get averageScore {
    List<double> scores = [
      titleMatch,
      descriptionMatch,
      skillsMatch,
      experienceMatch,
      locationMatch,
      salaryMatch,
    ];
    
    // إزالة الدرجات الصفرية من الحساب
    scores = scores.where((score) => score > 0).toList();
    
    if (scores.isEmpty) return 0.0;
    return scores.reduce((a, b) => a + b) / scores.length;
  }

  /// الحصول على أعلى درجة تطابق
  double get highestScore {
    return [
      titleMatch,
      descriptionMatch,
      skillsMatch,
      experienceMatch,
      locationMatch,
      salaryMatch,
    ].reduce((a, b) => a > b ? a : b);
  }

  /// الحصول على أقل درجة تطابق (غير الصفر)
  double get lowestScore {
    List<double> scores = [
      titleMatch,
      descriptionMatch,
      skillsMatch,
      experienceMatch,
      locationMatch,
      salaryMatch,
    ].where((score) => score > 0).toList();
    
    if (scores.isEmpty) return 0.0;
    return scores.reduce((a, b) => a < b ? a : b);
  }
} 