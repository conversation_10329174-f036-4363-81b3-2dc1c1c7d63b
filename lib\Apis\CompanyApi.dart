import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class CompanyApi {
  static const String baseUrl = 'https://wzzff.com/apiwzzff';
  static const _storage = FlutterSecureStorage();
  
Future<Map<String, dynamic>> registerCompany(Map<String, dynamic> companyData) async {
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/company/register'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode(companyData),
    );

    // فك الاستجابة
    final responseBody = json.decode(response.body);

    if (response.statusCode == 200 || response.statusCode == 201) {
      // التحقق من نجاح العملية من الاستجابة
      if (responseBody['success'] == true) {
        // حفظ الـ API token إذا كان متوفراً
        if (responseBody['data'] != null && responseBody['data']['api_token'] != null) {
          await _storage.write(key: 'company_api_token', value: responseBody['data']['api_token']);
        }
        
        // حفظ بيانات الشركة الأساسية
        if (responseBody['data'] != null) {
          await _storage.write(key: 'company_id', value: responseBody['data']['company_id'].toString());
          await _storage.write(key: 'company_name', value: responseBody['data']['name']);
          await _storage.write(key: 'company_email', value: responseBody['data']['email']);
          await _storage.write(key: 'company_status', value: responseBody['data']['status']);
        }
        
        return {
          'success': true,
          'message': responseBody['message'] ?? 'تم تسجيل الشركة بنجاح',
          'data': responseBody['data'],
        };
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'حدث خطأ أثناء التسجيل',
        };
      }
    } else {
      return {
        'success': false,
        'message': responseBody['message'] ?? 'حدث خطأ أثناء التسجيل',
      };
    }

  } catch (e) {
    return {
      'success': false,
      'message': 'تعذر الاتصال بالخادم',
    };
  }
}

  // تسجيل دخول الشركة
  Future<Map<String, dynamic>> loginCompany(String email, String password) async {
    try {
      /*
      await Future.delayed(const Duration(seconds: 2)); // محاكاة التأخير
      
      // محاكاة التحقق من البيانات
      if (email == '<EMAIL>' && password == '123456') {
        return {
          'success': true,
          'message': 'تم تسجيل الدخول بنجاح',
          'data': {
            'company_id': 123,
            'name': 'شركة التقنية الحديثة',
            'email': email,
            'token': 'fake_token_123456789',
            'status': 'approved',
          }
        };
      } else {
        return {
          'success': false,
          'message': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        };
      }
      */
      // الكود الحقيقي للـ API
      
      final response = await http.post(
        Uri.parse('$baseUrl/company/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );
      
      // فك الاستجابة
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200) {
        // التحقق من نجاح العملية من الاستجابة
        if (responseBody['success'] == true) {
          // حفظ الـ API token (api_token من السيرفر كـ company_api_token محلياً)
          if (responseBody['data'] != null && responseBody['data']['api_token'] != null) {
            await _storage.write(key: 'company_api_token', value: responseBody['data']['api_token']);
        //    print('💾 CompanyApi: تم حفظ company_api_token: ${responseBody['data']['api_token']}');
          }
          
          // حفظ بيانات الشركة الأساسية
          if (responseBody['data'] != null) {
            await _storage.write(key: 'company_id', value: responseBody['data']['company_id'].toString());
            await _storage.write(key: 'company_name', value: responseBody['data']['name']);
            await _storage.write(key: 'company_email', value: responseBody['data']['email']);
            // حفظ الحالة كـ approved بما أن تسجيل الدخول نجح
            await _storage.write(key: 'company_status', value: 'approved');
           // print('💾 CompanyApi: تم حفظ بيانات الشركة: ${responseBody['data']['name']}');
          }
          
          return {
            'success': true,
            'message': responseBody['message'] ?? 'تم تسجيل الدخول بنجاح',
            'data': responseBody['data'],
          };
        } else {
          return {
            'success': false,
            'message': responseBody['message'] ?? 'فشل في تسجيل الدخول',
          };
        }
      } else {
        final responseBody = json.decode(response.body);
        return {
          'success': false,
          'message': responseBody['message'] ?? 'فشل في تسجيل الدخول',
        };
      }
      
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }
  
  // إعادة تعيين كلمة المرور
  Future<Map<String, dynamic>> resetPassword(String email) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      
      return {
        'success': true,
        'message': 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ أثناء إرسال الطلب',
      };
    }
  }
  
  // التحقق من حالة الشركة
  Future<Map<String, dynamic>> checkCompanyStatus(String email) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      
      return {
        'success': true,
        'data': {
          'status': 'approved', // approved, pending, rejected
          'message': 'تم الموافقة على حساب الشركة',
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر التحقق من حالة الحساب',
      };
    }
  }

  // إنشاء وظيفة جديدة
  Future<Map<String, dynamic>> createJob(Map<String, dynamic> jobData) async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      final response = await http.post(
        Uri.parse('$baseUrl/company/jobs/create'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(jobData),
      );
      
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'حدث خطأ أثناء إنشاء الوظيفة',
        };
      }
      
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }

  // تحديث بيانات الشركة
  Future<Map<String, dynamic>> updateCompanyData(Map<String, dynamic> companyData) async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      final response = await http.put(
        Uri.parse('$baseUrl/company/update'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(companyData),
      );
      
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'حدث خطأ أثناء تحديث البيانات',
        };
      }
      
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }

  // الحصول على بيانات الشركة من السيرفر
  Future<Map<String, dynamic>> getCompanyData() async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      final response = await http.get(
        Uri.parse('$baseUrl/company/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'فشل في جلب بيانات الشركة',
        };
      }
      
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }

  // الحصول على وظائف الشركة
  Future<Map<String, dynamic>> getCompanyJobs() async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      final response = await http.get(
        Uri.parse('$baseUrl/company/jobs'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'فشل في جلب الوظائف',
        };
      }
      
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }

  // الحصول على المتقدمين للوظائف
  Future<Map<String, dynamic>> getJobApplications({int? jobId}) async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      String url = '$baseUrl/company/applications';
      if (jobId != null) {
        url += '?job_id=$jobId';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'فشل في جلب طلبات التوظيف',
        };
      }
      
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }

  // الحصول على الـ API token المحفوظ
  Future<String?> getApiToken() async {
    return await _storage.read(key: 'company_api_token');
  }
  
  // التحقق من تسجيل دخول الشركة
  Future<bool> isCompanyLoggedIn() async {
    final token = await _storage.read(key: 'company_api_token');
    return token != null && token.isNotEmpty;
  }
  
  // تسجيل خروج الشركة
  Future<void> logoutCompany() async {
    await _storage.delete(key: 'company_api_token');
    await _storage.delete(key: 'company_id');
    await _storage.delete(key: 'company_name');
    await _storage.delete(key: 'company_email');
    await _storage.delete(key: 'company_status');
  }
  
  // الحصول على نوع المستخدم الحالي
  static Future<String?> getCurrentUserType() async {
    const storage = FlutterSecureStorage();
    
 //   print('🔍 CompanyApi: فحص نوع المستخدم...');
    
    // التحقق من وجود token للشركة
    final companyToken = await storage.read(key: 'company_api_token');
   // print('🏢 CompanyApi: company_api_token = $companyToken');
    if (companyToken != null && companyToken.isNotEmpty) {
   //   print('✅ CompanyApi: المستخدم من نوع company');
      return 'company';
    }
    
    // التحقق من وجود token للباحث عن عمل
    final seekerToken = await storage.read(key: 'api_token');
   // print('👤 CompanyApi: api_token = $seekerToken');
    if (seekerToken != null && seekerToken.isNotEmpty) {
    //  print('✅ CompanyApi: المستخدم من نوع seeker');
      return 'seeker';
    }
    
    // لا يوجد مستخدم مسجل دخول
   // print('❌ CompanyApi: لا يوجد مستخدم مسجل دخول');
    return null;
  }
  
  // الحصول على الـ token المناسب حسب نوع المستخدم
  static Future<String?> getCurrentUserToken() async {
    const storage = FlutterSecureStorage();
    final userType = await getCurrentUserType();
    
    switch (userType) {
      case 'company':
        return await storage.read(key: 'company_api_token');
      case 'seeker':
        return await storage.read(key: 'api_token');
      default:
        return null;
    }
  }
  
  // الحصول على بيانات الشركة المحفوظة
  Future<Map<String, String?>> getSavedCompanyData() async {
    return {
      'company_id': await _storage.read(key: 'company_id'),
      'company_name': await _storage.read(key: 'company_name'),
      'company_email': await _storage.read(key: 'company_email'),
      'company_status': await _storage.read(key: 'company_status'),
      'api_token': await _storage.read(key: 'company_api_token'),
    };
  }
  
  // تسجيل دخول تلقائي باستخدام البيانات المحفوظة
  Future<Map<String, dynamic>> autoLogin() async {
    try {
      final companyData = await getSavedCompanyData();
      
      if (companyData['api_token'] != null && companyData['company_email'] != null) {
        return {
          'success': true,
          'message': 'تم تسجيل الدخول بنجاح',
          'data': {
            'company_id': int.tryParse(companyData['company_id'] ?? '0') ?? 0,
            'name': companyData['company_name'],
            'email': companyData['company_email'],
            'api_token': companyData['api_token'],
          }
        };
      } else {
        return {
          'success': false,
          'message': 'لا توجد بيانات تسجيل دخول محفوظة',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ أثناء تسجيل الدخول التلقائي',
      };
    }
  }

  // الحصول على المتقدمين لوظيفة معينة
  Future<Map<String, dynamic>> getJobApplicants(int jobId) async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      final response = await http.get(
        Uri.parse('$baseUrl/company/jobs/$jobId/applicants'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200) {
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'حدث خطأ أثناء جلب بيانات المتقدمين',
        };
      }
      
    } catch (e) {
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }

  // تحديث حالة المتقدم
  Future<Map<String, dynamic>> updateApplicantStatus(int applicationId, String newStatus, {String? type}) async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      // إعداد البيانات المرسلة
      final requestData = {
        'status': newStatus,
      };
      
      // إضافة type إذا كان متوفراً
      if (type != null) {
        requestData['type'] = type;
      }
      
     // print('📤 إرسال بيانات تحديث الحالة: $requestData');

      final response = await http.post(
        Uri.parse('$baseUrl/company/applications/$applicationId/status'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestData),
      );
      
      final responseBody = json.decode(response.body);
     // print('📥 استجابة السيرفر: $responseBody');
      
      if (response.statusCode == 200) {
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'حدث خطأ أثناء تحديث حالة المتقدم',
        };
      }
      
    } catch (e) {
    //  print('❌ خطأ في updateApplicantStatus: $e');
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }

  // إرسال Firebase token للسيرفر
  Future<Map<String, dynamic>> sendFirebaseToken(String firebaseToken) async {
    try {
      // الحصول على الـ token
      final token = await getApiToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'يجب تسجيل الدخول أولاً',
        };
      }

      final response = await http.post(
        Uri.parse('$baseUrl/company/firebase-token'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'firebase_token': firebaseToken,
          'device_type': Platform.isIOS ? 'ios' : 'android',
        }),
      );
      
      final responseBody = json.decode(response.body);
      
      if (response.statusCode == 200) {
       // print('✅ تم إرسال Firebase token بنجاح للشركة');
        return responseBody;
      } else {
        return {
          'success': false,
          'message': responseBody['message'] ?? 'حدث خطأ أثناء إرسال Firebase token',
        };
      }
      
    } catch (e) {
     // print('❌ خطأ في إرسال Firebase token: $e');
      return {
        'success': false,
        'message': 'تعذر الاتصال بالخادم',
      };
    }
  }
} 