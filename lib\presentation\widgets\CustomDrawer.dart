﻿import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
// import 'package:wzzff/Apis/ProfileApi.dart';
import 'package:wzzff/core/providers/theme_provider.dart';
import 'package:wzzff/core/utils/app_messages.dart';
import 'package:wzzff/presentation/screens/job_seeker/AppliedJobsScreen.dart';
import 'package:wzzff/presentation/screens/auth/LoginOrRegisterScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/SavedJobs.dart';
import 'package:wzzff/presentation/screens/SearchScreen.dart';
import 'package:wzzff/presentation/screens/job_seeker/SeekerScreenProfile.dart';
import 'package:wzzff/presentation/screens/jobs/TodayJobsScreen.dart';
import 'package:wzzff/presentation/screens/jobs/YesterdayJobsScreen.dart';
import 'package:wzzff/presentation/screens/privacy_policy/privacy_policy.dart';
import 'package:wzzff/presentation/screens/privacy_policy/terms_OfUsing.dart';
import 'package:wzzff/presentation/screens/create_cv/cv_creator_main.dart';
import 'package:wzzff/services/deep_link_service.dart';
import 'package:wzzff/presentation/screens/privacy_policy/ContactUsScreen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wzzff/presentation/screens/guide/UserGuideScreen.dart';
import 'package:wzzff/Company/Company/edit_company_data_page.dart';
import 'package:wzzff/Company/Company/job_applications_page.dart';
import 'package:wzzff/Company/Company/create_job_page.dart';
import '../../core/services/user_service.dart';
import 'package:wzzff/Apis/CompanyApi.dart';

class CustomDrawer extends StatefulWidget {
  final Function(int) onItemSelected;
  final int selectedIndex;

  const CustomDrawer({
    Key? key,
    required this.onItemSelected,
    required this.selectedIndex,
  }) : super(key: key);

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  bool isLoggedIn = false;
  String? name;
  String? email;

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    try {
      // فحص نوع المستخدم الحالي
      final userType = await UserService.getCurrentUserType();
      
      if (userType != null) {
        // المستخدم مسجل دخول
        setState(() {
          isLoggedIn = true;
        });
        
        if (userType == 'company') {
          // جلب بيانات الشركة
          final companyData = await CompanyApi().getSavedCompanyData();
          setState(() {
            name = companyData['company_name'] ?? 'شركة';
            email = companyData['company_email'];
          });
        } else if (userType == 'seeker') {
          // جلب بيانات المستخدم العادي
          // final userInfo = await ProfileApi().getMyProfileInformation();
          setState(() {
            name = 'مستخدم';
            email = null;
          });
        }
      } else {
        // لا يوجد مستخدم مسجل دخول
        setState(() {
          isLoggedIn = false;
          name = null;
          email = null;
        });
      }
    } catch (e) {
      setState(() {
        isLoggedIn = false;
        name = null;
        email = null;
      });
    }
  }

  // عرض نافذة اختبار الروابط العميقة
  void _showDeepLinkTestDialog(BuildContext context) {
    final TextEditingController linkController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختبار الروابط العميقة', textAlign: TextAlign.center),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: linkController,
                decoration: const InputDecoration(
                  labelText: 'أدخل الرابط العميق',
                  hintText: 'مثال: https://wzzff.com/job/12345',
                  border: OutlineInputBorder(),
                ),
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      linkController.text = 'https://wzzff.com/job/12345';
                    },
                    child: const Text('رابط وظيفة'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      linkController.text = 'https://wzzff.com/blog/show/19';
                    },
                    child: const Text('رابط مقال'),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                final link = linkController.text.trim();
                if (link.isNotEmpty) {
                  Navigator.of(context).pop();
                  _testDeepLink(context, link);
                }
              },
              child: const Text('اختبار'),
            ),
          ],
        );
      },
    );
  }

  // اختبار رابط عميق
  void _testDeepLink(BuildContext context, String link) {

    try {
      // استخدام خدمة الروابط العميقة مباشرة
      final deepLinkService = DeepLinkService();

      // استخدام الدالة العامة لاختبار الروابط العميقة
      deepLinkService.testDeepLink(link);

      // عرض رسالة للمستخدم
      AppMessages.showInfo('تم إرسال الرابط: $link');
    } catch (e) {
      AppMessages.showError('حدث خطأ أثناء اختبار الرابط العميق');
    }
  }

  // تنفيذ عملية تسجيل الخروج
  Future<void> _performLogout() async {
    try {
      // استخدام UserService للخروج الموحد (يدعم الشركات والمستخدمين)
      await UserService.logoutCurrentUser();
      
      setState(() {
        isLoggedIn = false;
        name = null;
        email = null;
      });
      
      AppMessages.showSuccess('تم تسجيل الخروج بنجاح');
    } catch (e) {
      AppMessages.showError('حدث خطأ أثناء تسجيل الخروج');
    }
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;

    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? Theme.of(context).colorScheme.primary,
      ),
      title: Text(
        title,
        style: GoogleFonts.tajawal(
          color: textColor ?? (isDarkMode ? Colors.white : Colors.black87),
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    // استخدام مزود السمة
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(51), // 0.2 = 51
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(40),
                      child: Padding(
                        padding: const EdgeInsets.all(2.0),
                        child: Image.asset(
                          "assets/logonewold.png",
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    isLoggedIn ? (name ?? "ظ…ط±ط­ط¨ط§ظ‹ ط¨ظƒ") : "ظˆط¸ظپ ط¯ظˆطھ ظƒظˆظ…",
                    style: GoogleFonts.tajawal(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isLoggedIn && email != null)
                    Text(
                      email!,
                      style: GoogleFonts.tajawal(
                        color: Colors.white.withAlpha(204), // 0.8 = 204
                        fontSize: 14,
                      ),
                    ),
                ],
              ),
            ),

            // إضافة زر تبديل الوضع الليلي
            _buildMenuItem(
              icon: isDarkMode ? Icons.light_mode : Icons.dark_mode,
              title: isDarkMode ? "الوضع العادي" : "الوضع الليلي",
              textColor: isDarkMode ? Colors.white : Colors.black87,
              onTap: () {
                // حفظ حالة الوضع قبل التبديل
                final willBeDarkMode = !themeProvider.isDarkMode;

                // تبديل الوضع
                themeProvider.toggleTheme();

                // عرض رسالة للمستخدم
                AppMessages.showInfo(
                  willBeDarkMode
                      ? "تم تفعيل الوضع الليلي"
                      : "تم تفعيل الوضع العادي"
                );

                // إغلاق القائمة بعد التبديل
                Navigator.pop(context);
              },
            ),

            const Divider(),

            // باقي العناصر الحالية
            _buildMenuItem(
              icon: Icons.home_outlined,
              title: 'الرئيسية',
              onTap: () {
                widget.onItemSelected(0);
                Navigator.pop(context);
              },
            ),
            _buildMenuItem(
              icon: Icons.search_outlined,
              title: 'البحث',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SearchScreen(),
                  ),
                );
              },
            ),
       
            _buildMenuItem(
              icon: Icons.newspaper,
              title: 'أخبار الوظائف',
              onTap: () {
                widget.onItemSelected(2);
                Navigator.pop(context);
              },
            ),
            _buildMenuItem(
              icon: Icons.newspaper_rounded,
              title: 'المقالات',
              onTap: () {
                widget.onItemSelected(3);
                Navigator.pop(context);
              },
            ),
            _buildMenuItem(
              icon: Icons.person_outline,
              title: 'حسابي',
              onTap: () {
                widget.onItemSelected(5);
                Navigator.pop(context);
              },
            ),
                 Divider(
              height: 20,
              thickness: 1,
              indent: 20,
              endIndent: 20,
              color: Theme.of(context).dividerTheme.color,
            ),
            // Restored menu items
            _buildMenuItem(
              icon: Icons.today,
              title: 'وظائف اليوم',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TodayJobsScreen(),
                  ),
                );
              },
            ),
            _buildMenuItem(
              icon: Icons.history,
              title: 'وظائف الأمس',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => YesterdayJobsScreen(),
                  ),
                );
              },
            ),
                 // إضافة عناصر القائمة السفلية
            _buildMenuItem(
              icon: Icons.location_on_outlined,
              title: 'وظائف حسب الدولة',
              onTap: () {
                widget.onItemSelected(4);
                Navigator.pop(context);
              },
            ),
            _buildMenuItem(
              icon: Icons.work_outline,
              title: 'وظائف تقدمت لها',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AppliedJobsScreen(),
                  ),
                );
              },
            ),
            Divider(
              height: 20,
              thickness: 1,
              indent: 20,
              endIndent: 20,
              color: Theme.of(context).dividerTheme.color,
            ),
                        // إضافة خيار إنشاء السيرة الذاتية
            _buildMenuItem(
              icon: Icons.description_outlined,
              title: 'إنشاء سيرة ذاتية',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CvCreatorMain(),
                  ),
                );
              },
            ),
            _buildMenuItem(
              icon: Icons.bookmark_border_outlined,
              title: 'المفضلة',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SavedJobs(),
                  ),
                );
              },
            ),


            Divider(
              height: 20,
              thickness: 1,
              indent: 20,
              endIndent: 20,
              color: Theme.of(context).dividerTheme.color,
            ),
            _buildMenuItem(
              icon: Icons.person_outline,
              title: 'الملف الشخصي',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SeekerScreenProfile(),
                  ),
                );
              },
            ),

         
            // إضافة اتصل بنا
            _buildMenuItem(
              icon: Icons.support_agent,
              title: 'اتصل بنا',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ContactUsScreen(),
                  ),
                );
              },
            ),
            _buildMenuItem(
              icon: Icons.privacy_tip_outlined,
              title: 'سياسة الخصوصية',
              onTap: () async {
                final url = Uri.parse('https://wzzff.com/cms/%D8%B3%D9%8A%D8%A7%D8%B3%D9%8A%D8%A9-%D8%A7%D9%84%D8%AE%D8%B5%D9%88%D8%B5%D9%8A%D8%A9');
                if (await canLaunchUrl(url)) {
                  await launchUrl(url, mode: LaunchMode.externalApplication);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تعذر فتح الرابط')),
                  );
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('دليل المستخدم'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const UserGuideScreen()),
                );
              },
            ),

            // إضافة خيار اختبار الروابط العميقة
            if (isLoggedIn)
              _buildMenuItem(
                icon: Icons.logout,
                title: 'تسجيل الخروج',
                textColor: Colors.red,
                onTap: () {
                  // إغلاق القائمة الجانبية أولاً
                  Navigator.pop(context);

                  // ثم تنفيذ عملية تسجيل الخروج
                  _performLogout();
                },
              ),
            if (!isLoggedIn)
              _buildMenuItem(
                icon: Icons.login,
                title: 'تسجيل الدخول',
                textColor: const Color(0xFF2daae2),
                onTap: () {
                  // استخدام Navigator للانتقال مباشرة إلى شاشة LoginOrRegisterScreen
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LoginOrRegisterScreen(),
                    ),
                  );
                },
              ),
            Divider(
              height: 20,
              thickness: 1,
              indent: 20,
              endIndent: 20,
              color: Theme.of(context).dividerTheme.color,
            ),
            // عناصر الشركة
          
         
          ],
        ),
      ),
    );
  }
}

class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height - 40);

    var firstControlPoint = Offset(size.width / 4, size.height);
    var firstEndPoint = Offset(size.width / 2, size.height - 20);
    path.quadraticBezierTo(
      firstControlPoint.dx, firstControlPoint.dy,
      firstEndPoint.dx, firstEndPoint.dy
    );

    var secondControlPoint = Offset(size.width - (size.width / 4), size.height - 40);
    var secondEndPoint = Offset(size.width, size.height - 10);
    path.quadraticBezierTo(
      secondControlPoint.dx, secondControlPoint.dy,
      secondEndPoint.dx, secondEndPoint.dy
    );

    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}




