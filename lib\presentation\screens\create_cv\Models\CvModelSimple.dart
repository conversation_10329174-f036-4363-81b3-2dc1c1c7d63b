import 'package:flutter/foundation.dart';

class CvModelSimple {
  int id;

  String name;

  String image;

  String ltf;

  CvModelSimple({
    required int this.id,
    required String this.name,
    required String this.image,
    required String this.ltf,
  });

  factory CvModelSimple.fromJson(c) {
    return CvModelSimple(
        id: c["id"], name: c["name"], image: c["image"], ltf: c["ltf"]);
  }
}
