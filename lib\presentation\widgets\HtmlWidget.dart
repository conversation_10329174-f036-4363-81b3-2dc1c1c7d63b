import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:html/dom.dart';
import 'package:wzzff/services/google_ad_service.dart';

// واجهة متوافقة مع HtmlWidget من flutter_widget_from_html
class HtmlWidget extends StatelessWidget {
  final String html;
  final TextStyle? textStyle;

  const HtmlWidget(this.html, {super.key, this.textStyle});

  @override
  Widget build(BuildContext context) {
    return Html(
      data: html,
      style: {
        "body": Style(
          fontSize: FontSize(textStyle?.fontSize ?? 14),
          fontWeight: textStyle?.fontWeight ?? FontWeight.normal,
          color: textStyle?.color ?? Colors.black87,
          fontFamily: 'Tajawal',
          lineHeight: LineHeight(textStyle?.height ?? 1.5),
        ),
      },
      onLinkTap: (url, attributes, element) {
        if (url != null) {
          final uri = Uri.tryParse(url);
          if (uri != null) {
            Future.microtask(() async {
              final adService = GoogleAdService();
              final shown = await adService.showInterstitialAd();
              // بعد إغلاق الإعلان أو إذا لم يكن متاحًا، افتح الرابط
              launchUrl(uri);
            });
          }
        }
      },
    );
  }
}