import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';
import 'package:wzzff/presentation/widgets/job_card.dart';
import 'package:wzzff/presentation/widgets/ad_job_card.dart';
import 'package:wzzff/services/google_ad_service.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';

class YesterdayJobsScreen extends StatefulWidget {
  const YesterdayJobsScreen({super.key});

  @override
  State<YesterdayJobsScreen> createState() => _YesterdayJobsScreenState();
}

class _YesterdayJobsScreenState extends State<YesterdayJobsScreen> {
  bool isLoading = true;
  List<dynamic> yesterdayJobs = [];
  String errorMessage = "";
  int offset = 0;
  bool isLoadingMore = false;
  final ScrollController _scrollController = ScrollController();
  int _interstitialLoadCount = 0;
  final GoogleAdService _adService = GoogleAdService();

  @override
  void initState() {
    super.initState();
    _loadYesterdayJobs();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      _loadMoreYesterdayJobs();
    }
  }

  Future<void> _loadYesterdayJobs() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = "";
        offset = 0;
      });
      _interstitialLoadCount++;
      if (_interstitialLoadCount >= 3) {
        await _adService.showInterstitialAd();
        _interstitialLoadCount = 0;
      }
      final jobs = await JobsApi().getYesterdayJobs();
      setState(() {
        yesterdayJobs = jobs;
        isLoading = false;
      });
      Provider.of<AppStateProvider>(context, listen: false).clearServerError();
    } catch (e) {
      setState(() {
        isLoading = false;
        errorMessage = "حدث خطأ أثناء تحميل الوظائف. يرجى المحاولة مرة أخرى.";
      });
      Provider.of<AppStateProvider>(context, listen: false).setServerError(
        "حدث خطأ أثناء تحميل الوظائف. سيتم إعادة المحاولة تلقائيًا.",
        _loadYesterdayJobs,
      );
    }
  }

  Future<void> _loadMoreYesterdayJobs() async {
    if (isLoadingMore) return;
    try {
      setState(() {
        isLoadingMore = true;
      });
      _interstitialLoadCount++;
      if (_interstitialLoadCount >= 3) {
        await _adService.showInterstitialAd();
        _interstitialLoadCount = 0;
      }
      offset += yesterdayJobs.length;
      final moreJobs = await JobsApi().getYesterdayJobs();
      if (moreJobs.isNotEmpty) {
        setState(() {
          yesterdayJobs.addAll(moreJobs);
          isLoadingMore = false;
        });
      } else {
        setState(() {
          isLoadingMore = false;
        });
      }
      Provider.of<AppStateProvider>(context, listen: false).clearServerError();
    } catch (e) {
      setState(() {
        isLoadingMore = false;
      });
      Provider.of<AppStateProvider>(context, listen: false).setServerError(
        "حدث خطأ أثناء تحميل المزيد من الوظائف. سيتم إعادة المحاولة تلقائيًا.",
        _loadMoreYesterdayJobs,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : Colors.white,
        appBar: AppBar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          elevation: 0,
          title: Text(
            "الوظائف المضافة بالأمس",
            style: GoogleFonts.tajawal(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          centerTitle: true,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(
                Icons.arrow_forward,
                color: Colors.white,
                size: 20,
              ),
              onPressed: () => Navigator.pop(context),
              tooltip: 'رجوع',
            ),
          ],
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Theme.of(context).colorScheme.primary,
            statusBarIconBrightness: Brightness.light,
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withOpacity(0.8),
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
              ],
              stops: const [0.0, 0.1, 0.3],
            ),
          ),
          child: Column(
            children: [

              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 20),
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).scaffoldBackgroundColor
                        : Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(
                            Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: _buildContent(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 20),
            Text(
              "جاري تحميل وظائف الأمس...",
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    if (errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 60,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.red[200]
                  : Colors.red[300],
            ),
            const SizedBox(height: 20),
            Text(
              errorMessage,
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: _loadYesterdayJobs,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Text(
                "إعادة المحاولة",
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (yesterdayJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_off,
              size: 60,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[500]
                  : Colors.grey[400],
            ),
            const SizedBox(height: 20),
            Text(
              "لا توجد وظائف مضافة بالأمس",
              style: GoogleFonts.tajawal(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.titleLarge?.color
                    : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 10),
            Text(
              "يرجى التحقق من وظائف اليوم أو الوظائف الأخرى",
              style: GoogleFonts.tajawal(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadYesterdayJobs,
      color: Theme.of(context).colorScheme.primary,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        itemCount: yesterdayJobs.length + (isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == 0) {
            // إعلان بانر أعلى القائمة
            return const Padding(
              padding: EdgeInsets.only(bottom: 12),
              child: AdJobCard(),
            );
          }
          if (index > 1 && (index - 1) % 10 == 0) {
            return Column(
              children: [
                const AdJobCard(),
                _buildCustomJobCard(yesterdayJobs[index - 1]),
              ],
            );
          }
          final job = yesterdayJobs[index - 1];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(
                        Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildCustomJobCard(job),
            ),
          );
        },
      ),
    );
  }

  // إنشاء كارت مخصص للوظيفة
  Widget _buildCustomJobCard(dynamic job) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DetailJobScreen(
              title: job.title ?? '',
              code_address: job.code_address ?? '',
              des: job.description ?? '',
              cat: job.cat ?? '',
              city_name: job.city_name ?? '',
              company_name: job.company_name ?? '',
              country_name: job.country_name ?? '',
              created_at_date: job.created_at_date ?? '',
              edu: job.edu ?? '',
              email: job.email,
              end_at: job.end_at ?? '',
              exp: job.exp ?? '',
              gender: job.gender ?? '',
              job_type_name: job.job_type_name ?? '',
              number: job.number,
              salary: job.salary,
              salary_currency: job.salary_currency ?? '',
              slug: job.slug ?? '',
              state_name: job.state_name ?? '',
              time: job.time ?? '',
            ),
          ),
        );
      },
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الوظيفة والشركة
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(
                        Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.work_outline,
                    color: Theme.of(context).colorScheme.primary,
                    size: 22,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        job.title ?? '',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: GoogleFonts.tajawal(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        job.company_name ?? '',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: GoogleFonts.tajawal(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).textTheme.bodyMedium?.color
                              : Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // معلومات الوظيفة
            Row(
              children: [
                // الموقع
                if (job.city_name != null && job.city_name.isNotEmpty)
                  Expanded(
                    child: _buildInfoItem(
                      Icons.location_on_outlined,
                      '${job.city_name}, ${job.country_name ?? ''}'
                    ),
                  ),
                const SizedBox(width: 12),
                // نوع الوظيفة
                if (job.job_type_name != null && job.job_type_name.isNotEmpty)
                  Expanded(
                    child: _buildInfoItem(
                      Icons.business_center_outlined,
                      job.job_type_name
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 12),

            // تاريخ النشر والتعليم
            Row(
              children: [
                // تاريخ النشر
                if (job.created_at_date != null && job.created_at_date.isNotEmpty)
                  Expanded(
                    child: _buildInfoItem(
                      Icons.calendar_today_outlined,
                      job.created_at_date
                    ),
                  ),
                const SizedBox(width: 12),
                // الخبرة المطلوبة
                if (job.exp != null && job.exp.isNotEmpty)
                  Expanded(
                    child: _buildInfoItem(
                      Icons.timeline_outlined,
                      job.exp
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 16),

            // وصف مختصر للوظيفة
            job.description != null && job.description.isNotEmpty
            ? (() {
                // إزالة عناصر HTML من الوصف
                String cleanDescription = job.description.replaceAll(RegExp(r'<[^>]*>'), ' ');
                cleanDescription = cleanDescription.replaceAll('&nbsp;', ' ');
                cleanDescription = cleanDescription.replaceAll('&amp;', '&');
                cleanDescription = cleanDescription.replaceAll('&lt;', '<');
                cleanDescription = cleanDescription.replaceAll('&gt;', '>');

                return Text(
                  cleanDescription,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: GoogleFonts.tajawal(
                    fontSize: 13,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).textTheme.bodySmall?.color
                        : Colors.grey[600],
                    height: 1.5,
                  ),
                );
              })()
            : const SizedBox(),

            const SizedBox(height: 16),

            // زر التقديم
            SizedBox(
              width: double.infinity,
              height: 40,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => DetailJobScreen(
                        title: job.title ?? '',
                        code_address: job.code_address ?? '',
                        des: job.description ?? '',
                        cat: job.cat ?? '',
                        city_name: job.city_name ?? '',
                        company_name: job.company_name ?? '',
                        country_name: job.country_name ?? '',
                        created_at_date: job.created_at_date ?? '',
                        edu: job.edu ?? '',
                        email: job.email,
                        end_at: job.end_at ?? '',
                        exp: job.exp ?? '',
                        gender: job.gender ?? '',
                        job_type_name: job.job_type_name ?? '',
                        number: job.number,
                        salary: job.salary,
                        salary_currency: job.salary_currency ?? '',
                        slug: job.slug ?? '',
                        state_name: job.state_name ?? '',
                        time: job.time ?? '',
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'عرض التفاصيل',
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة مساعدة لإنشاء عنصر معلومات
  Widget _buildInfoItem(IconData icon, String text) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 6),
        Flexible(
          child: Text(
            text,
            overflow: TextOverflow.ellipsis,
            style: GoogleFonts.tajawal(
              fontSize: 13,
              color: isDarkMode
                  ? Theme.of(context).textTheme.bodySmall?.color
                  : Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }
}












