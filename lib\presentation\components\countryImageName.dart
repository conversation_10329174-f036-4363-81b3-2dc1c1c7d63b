import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/jobs/JobsByCountries.dart';
import 'package:wzzff/presentation/components/TransitionPage.dart';
import 'package:wzzff/presentation/widgets/custom_theme.dart';

class countryImageName extends StatelessWidget {
  final String image;
  final String name;

  const countryImageName({
    super.key,
    required this.image,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          TransitionPage(JobsByCountries(country: name)),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: 80,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Image.asset(
                  "assets/$image.jpg",
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              name,
              style: GoogleFonts.tajawal(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.titleMedium?.color
                    : const Color(0xFF2B2B2B),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
