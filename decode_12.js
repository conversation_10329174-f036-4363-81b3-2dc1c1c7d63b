// سكريبت لفك تشفير كود Obfuscated من ملف 12.js
const fs = require('fs');

// قراءة الكود المشفر
const code = fs.readFileSync('12.js', 'utf8');

// محاولة استخراج الدالة والمصفوفة الرئيسية
// غالباً ما تكون الدالة مثل _0x1a4e والمصفوفة مثل _0x2c43
// سنحاول تنفيذ الكود في بيئة آمنة مع طباعة النتائج

// ملاحظة: هذا السكريبت يحاول فقط فك التشويش البرمجي الشائع
// إذا كان هناك طبقات تشويش متعددة قد تحتاج لتكرار العملية

// استخراج دالة وفكها تلقائياً
function tryEvalObfuscated(code) {
    try {
        // إزالة أي أكواد ضارة محتملة (تنفيذ فقط الدوال والمصفوفة)
        // استخراج جزء الدوال والمصفوفة فقط
        const match = code.match(/function _0x1a4e[\s\S]+?\}\);/);
        const arrMatch = code.match(/function \(\) \{[\s\S]+?return [^;]+;\n\}/);
        if (match && arrMatch) {
            const context = `${arrMatch[0]}
${match[0]}
console.log(_0x1a4e(0, 'dummy'));`;
            // تنفيذ في بيئة Node.js
            require('fs').writeFileSync('decode_tmp.js', context);
            const { execSync } = require('child_process');
            const result = execSync('node decode_tmp.js').toString();
            return result;
        }
    } catch (e) {
        return 'فشل في فك التشفير تلقائياً: ' + e.message;
    }
    return 'لم يتم العثور على دوال فك التشفير المناسبة.';
}

const result = tryEvalObfuscated(code);
fs.writeFileSync('12_decoded.js', result);
console.log('تم حفظ الكود المفكوك في 12_decoded.js'); 