import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:wzzff/presentation/widgets/job_card.dart';
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';

class SavedJobs extends StatefulWidget {
  const SavedJobs({super.key});

  @override
  State<SavedJobs> createState() => _SavedJobsState();
}

class _SavedJobsState extends State<SavedJobs> {
  List<String> savedJobs = [];
  Map<String, dynamic> jobsData = {};
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedJobs();
  }

  Future<void> _loadSavedJobs() async {
    setState(() {
      isLoading = true;
    });

    final prefs = await SharedPreferences.getInstance();
    try {
      savedJobs = prefs.getStringList('savedJobs') ?? [];
      final savedJobsData = prefs.getString('savedJobsData') ?? '{}';
      jobsData = json.decode(savedJobsData);
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      return;
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _removeFromFavorites(String slug) async {
    final prefs = await SharedPreferences.getInstance();
    savedJobs.remove(slug);
    await prefs.setStringList('savedJobs', savedJobs);

    jobsData.remove(slug);
    await prefs.setString('savedJobsData', json.encode(jobsData));

    setState(() {});

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تمت إزالة الوظيفة من المفضلة'),
        backgroundColor: Colors.grey,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد على RTL
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : Colors.grey[50], // خلفية متوافقة مع الوضع الليلي
        appBar: AppBar(
          title: Text(
            "الوظائف المفضلة",
            style: GoogleFonts.tajawal(
              fontSize: 16, // تصغير حجم الخط
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          centerTitle: true,
          backgroundColor: Theme.of(context).colorScheme.primary,
          elevation: 0,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(16),
            ),
          ),
          // نقل زر العودة إلى اليسار
          leading: null, // إزالة الزر من اليمين
          automaticallyImplyLeading: false, // إيقاف الزر التلقائي
          actions: [
            // إضافة زر العودة في جهة اليسار (يظهر في اليسار بسبب RTL)
            IconButton(
              icon: const Icon(Icons.arrow_forward, size: 22), // تغيير الأيقونة إلى arrow_forward
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
        body: isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  color: Color(0xFF2daae2),
                ),
              )
            : savedJobs.isEmpty
                ? _buildEmptyState()
                : _buildSavedJobsList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 100, // تصغير حجم الأيقونة
            height: 100,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(
                  Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.bookmark_border,
              color: Theme.of(context).colorScheme.primary,
              size: 50, // تصغير حجم الأيقونة
            ),
          ),
          const SizedBox(height: 20),
          Text(
            "لم تقم بحفظ أي وظائف بعد",
            style: GoogleFonts.tajawal(
              fontSize: 16, // تصغير حجم الخط
              fontWeight: FontWeight.bold,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).textTheme.titleLarge?.color
                  : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Text(
              "قم بحفظ الوظائف المفضلة لديك للرجوع إليها لاحقاً",
              textAlign: TextAlign.center,
              style: GoogleFonts.tajawal(
                fontSize: 13, // تصغير حجم الخط
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyMedium?.color
                    : Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10), // تصغير حجم الزر
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 2,
            ),
            child: Text(
              "استكشف الوظائف",
              style: GoogleFonts.tajawal(
                fontSize: 14, // تصغير حجم الخط
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedJobsList() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : Colors.grey[50], // استخدام لون خلفية متوافق مع الوضع الليلي
      ),
      child: RefreshIndicator(
        onRefresh: _loadSavedJobs,
        color: Theme.of(context).colorScheme.primary,
        child: ListView.builder(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
          itemCount: savedJobs.length + 1,
          itemBuilder: (context, index) {
            if (index == 0) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  "الوظائف المحفوظة (${savedJobs.length})",
                  style: GoogleFonts.tajawal(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).textTheme.titleMedium?.color
                        : Colors.grey[700],
                  ),
                ),
              );
            }

            final jobIndex = index - 1;
            final slug = savedJobs[jobIndex];
            final jobData = jobsData[slug];

            if (jobData == null) return const SizedBox.shrink();

            // إصلاح مشكلة right overflow وإزالة اللون الوردي
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Dismissible(
                key: Key(slug),
                direction: DismissDirection.endToStart,
                background: Container(
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(right: 20),
                  decoration: BoxDecoration(
                    color: Colors.red[400],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.delete_outline,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                onDismissed: (direction) {
                  _removeFromFavorites(slug);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).cardTheme.color
                        : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(
                            Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.05),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  // استخدام SizedBox لتحديد عرض ثابت للكارت
                  width: MediaQuery.of(context).size.width - 32, // عرض الشاشة ناقص الهوامش
                  child: _buildCustomJobCard(jobData, slug),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // إنشاء كارت مخصص بدلاً من استخدام JobCard مباشرة
  Widget _buildCustomJobCard(Map<String, dynamic> jobData, String slug) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      jobData['title'] ?? '',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: GoogleFonts.tajawal(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      jobData['company_name'] ?? '',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: GoogleFonts.tajawal(
                        fontSize: 11,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyMedium?.color
                            : Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () => _removeFromFavorites(slug),
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(
                        Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.bookmark,
                    color: Theme.of(context).colorScheme.primary,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Divider(
              height: 1,
              thickness: 0.5,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.withOpacity(0.2)
                  : Colors.grey.withOpacity(0.3)),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // عرض المدينة والدولة معًا
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[400]
                          : Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        "${jobData['city_name'] ?? ''} - ${jobData['country_name'] ?? ''}",
                        overflow: TextOverflow.ellipsis,
                        style: GoogleFonts.tajawal(
                          fontSize: 11,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).textTheme.bodySmall?.color
                              : Colors.grey[800],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  Icon(
                    Icons.calendar_month,
                    size: 14,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.orange[300]
                        : Colors.orange[700],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    jobData['time'] ?? '',
                    style: GoogleFonts.tajawal(
                      fontSize: 11,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).textTheme.bodySmall?.color
                          : Colors.grey[800],
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(
                      Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  jobData['job_type_name'] ?? '',
                  style: GoogleFonts.tajawal(
                    fontSize: 10,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          // إضافة زر لعرض تفاصيل الوظيفة
          const SizedBox(height: 10),
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DetailJobScreen(
                    title: jobData['title'] ?? '',
                    code_address: jobData['code_address'] ?? '',
                    des: jobData['des'] ?? '',
                    cat: jobData['cat'] ?? '',
                    city_name: jobData['city_name'] ?? '',
                    company_name: jobData['company_name'] ?? '',
                    country_name: jobData['country_name'] ?? '',
                    created_at_date: jobData['created_at_date'] ?? '',
                    edu: jobData['edu'] ?? '',
                    email: jobData['email'],
                    end_at: jobData['end_at'] ?? '',
                    exp: jobData['exp'] ?? '',
                    gender: jobData['gender'] ?? '',
                    job_type_name: jobData['job_type_name'] ?? '',
                    number: jobData['number'],
                    salary: jobData['salary'],
                    salary_currency: jobData['salary_currency'] ?? '',
                    slug: slug,
                    state_name: jobData['state_name'] ?? '',
                    time: jobData['time'] ?? '',
                  ),
                ),
              );
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(
                    Theme.of(context).brightness == Brightness.dark ? 0.15 : 0.05),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Center(
                child: Text(
                  "عرض التفاصيل",
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
