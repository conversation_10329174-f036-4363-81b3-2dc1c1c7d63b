import 'package:flutter/material.dart';
import 'package:wzzff/services/deep_link_handler.dart';

/// أداة لاختبار الروابط العميقة داخل التطبيق
class DeepLinkTester {
  final DeepLinkHandler _deepLinkHandler;

  DeepLinkTester({required DeepLinkHandler deepLinkHandler}) : _deepLinkHandler = deepLinkHandler;

  /// عرض نافذة لاختبار الروابط العميقة
  void showDeepLinkTestDialog(BuildContext context) {
    final TextEditingController _linkController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('اختبار الروابط العميقة', textAlign: TextAlign.center),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _linkController,
                decoration: const InputDecoration(
                  labelText: 'أدخل الرابط العميق',
                  hintText: 'مثال: https://wzzff.com/job/12345',
                  border: OutlineInputBorder(),
                ),
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      _linkController.text = 'https://wzzff.com/job/12345';
                    },
                    child: const Text('رابط وظيفة'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      _linkController.text = 'https://wzzff.com/blog/show/19';
                    },
                    child: const Text('رابط مقال'),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                final link = _linkController.text.trim();
                if (link.isNotEmpty) {
                  Navigator.of(context).pop();
                  _testDeepLink(link);
                }
              },
              child: const Text('اختبار'),
            ),
          ],
        );
      },
    );
  }

  /// اختبار رابط عميق
  void _testDeepLink(String link) {
    debugPrint('اختبار الرابط العميق: $link');
    // استخدام نفس آلية معالجة الروابط العميقة
   // _deepLinkHandler._listenToDeepLinks();
   // _deepLinkHandler._handleDeepLink(link);
  }
}
