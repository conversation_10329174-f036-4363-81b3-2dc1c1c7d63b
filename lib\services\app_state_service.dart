import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:wzzff/core/constants/Constants.dart';
import 'package:wzzff/models/app_state_model.dart';

/// خدمة للتحقق من حالة التطبيق من السيرفر
class AppStateService {
  static const String _appStateEndpoint = '/apiwzzff/appStates';

  /// الحصول على حالة التطبيق من السيرفر أو من التخزين المؤقت
  Future<AppStateModel> getAppState({bool forceRefresh = false}) async {
    try {
      // إنشاء عنوان URL للطلب
      final url = Uri.parse('${Constants.url}$_appStateEndpoint');
      debugPrint('AppStateService: إرسال طلب إلى: $url');
      
      // إرسال طلب HTTP POST مع بيانات فارغة
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({}),
      );

      debugPrint('AppStateService: كود الاستجابة: ${response.statusCode}');
      debugPrint('AppStateService: محتوى الاستجابة: ${response.body}');

      // التحقق من نجاح الطلب
      if (response.statusCode == 200) {
        // تحليل البيانات JSON
        final Map<String, dynamic> data = json.decode(response.body);

        // إنشاء نموذج حالة التطبيق
        final appState = AppStateModel.fromJson(data);

        debugPrint('تم الحصول على بيانات حالة التطبيق من السيرفر');
        debugPrint('الإعلان enabled: ${appState.announcement.enabled}');
        debugPrint('عنوان الإعلان: ${appState.announcement.title}');
        
        return appState;
      } else {
        debugPrint('فشل الطلب، كود الخطأ: ${response.statusCode}، استخدام بيانات حالة التطبيق الافتراضية');
        return _getDefaultAppState();
      }
    } catch (e) {
      debugPrint('حدث استثناء في AppStateService: $e، استخدام بيانات حالة التطبيق الافتراضية');
      return _getDefaultAppState();
    }
  }

  /// الحصول على نموذج حالة التطبيق الافتراضي
  AppStateModel _getDefaultAppState() {
    return AppStateModel(
      appVersionAndroid: '',
      appVersionIos: '',
      minimumSupportedVersionAndroid: '',
      minimumSupportedVersionIos: '',
      maintenanceMode: false,
      maintenanceEndDate: DateTime.now().add(const Duration(days: 1)),
      forceUpdate: false,
      updateUrls: {'android': '', 'ios': ''},
      messages: AppMessages(
        maintenanceMessage: 'نقوم حاليًا بإجراء صيانة للنظام. الرجاء المحاولة لاحقًا.',
        forceUpdateMessage: 'يوجد تحديث جديد للتطبيق. الرجاء التحديث للمتابعة.',
        optionalUpdateMessage: 'يتوفر إصدار جديد من التطبيق. ننصحك بالتحديث للحصول على أفضل تجربة.',
      ),
      announcement: AppAnnouncement(
        enabled: false,
        title: '',
        body: '',
        link: '',
      ),
      darkMode: false,
      authControl: AppAuthControl(
        registrationEnabled: true,
        loginEnabled: true,
      ),
    );
  }
}
