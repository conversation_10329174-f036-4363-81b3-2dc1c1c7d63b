import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wzzff/models/notification_model.dart' as app_model;
import 'package:wzzff/services/Notifications.dart';
import '../presentation/screens/job_seeker/Notifications.dart';
import 'package:wzzff/services/local_notification_service.dart';

// دالة معالجة الإشعارات في الخلفية
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // تهيئة Firebase
  await Firebase.initializeApp();
  debugPrint('تم استلام إشعار في الخلفية: ${message.messageId}');

  try {
    // حفظ الإشعار في التخزين المحلي
    await _saveNotificationToStorage(message);

    // تحديث عدد الإشعارات غير المقروءة
    final prefs = await SharedPreferences.getInstance();

    // الحصول على الإشعارات المحفوظة
    final notificationsJson = prefs.getStringList('notifications') ?? [];

    // تحويل الإشعارات إلى قائمة
    List<app_model.NotificationModel> notifications = notificationsJson
        .map((json) => app_model.NotificationModel.fromJson(json))
        .toList();

    // حساب عدد الإشعارات غير المقروءة
    final unreadCount = notifications.where((n) => !n.isRead).length;

    // حفظ عدد الإشعارات غير المقروءة
    await prefs.setInt('unread_notifications_count', unreadCount);

    // تعيين علامة تحديث الإشعارات
    await prefs.setBool('notifications_updated', true);
    await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);

    debugPrint('تم تحديث الإشعارات في الخلفية. عدد الإشعارات غير المقروءة: $unreadCount');
  } catch (e) {
    debugPrint('خطأ في معالجة الإشعار في الخلفية: $e');
  }
}

// دالة لحفظ الإشعار في التخزين المحلي
Future<void> _saveNotificationToStorage(RemoteMessage message) async {
  try {
    // إنشاء نموذج الإشعار
    final notification = app_model.NotificationModel(
      id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: message.notification?.title ?? 'إشعار جديد',
      body: message.notification?.body ?? '',
      imageUrl: message.notification?.android?.imageUrl ?? message.notification?.apple?.imageUrl,
      data: message.data,
      time: DateTime.now(),
      isRead: false,
    );

    // الحصول على الإشعارات المحفوظة
    final prefs = await SharedPreferences.getInstance();
    final notificationsJson = prefs.getStringList('notifications') ?? [];

    // تحويل الإشعارات إلى قائمة
    List<app_model.NotificationModel> notifications = notificationsJson
        .map((json) => app_model.NotificationModel.fromJson(json))
        .toList();

    // التحقق من عدم وجود إشعار بنفس المعرف
    final existingIndex = notifications.indexWhere((n) => n.id == notification.id);

    if (existingIndex >= 0) {
      // تحديث الإشعار الموجود
      notifications[existingIndex] = notification;
    } else {
      // إضافة إشعار جديد
      notifications.insert(0, notification);
    }

    // ترتيب الإشعارات من الأحدث إلى الأقدم
    notifications.sort((a, b) => b.time.compareTo(a.time));

    // تحويل الإشعارات إلى JSON وحفظها
    final updatedNotificationsJson = notifications.map((n) => n.toJson()).toList();
    await prefs.setStringList('notifications', updatedNotificationsJson);

    debugPrint('تم حفظ الإشعار في التخزين المحلي');
  } catch (e) {
    debugPrint('خطأ في حفظ الإشعار: $e');
  }
}

class FirebaseMessagingService {
  static final FirebaseMessagingService _instance = FirebaseMessagingService._internal();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final storage = const FlutterSecureStorage();

  factory FirebaseMessagingService() {
    return _instance;
  }

  FirebaseMessagingService._internal();

  // تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // تهيئة خدمة الإشعارات المحلية
    await LocalNotificationService().initialize();

    // تعيين معالج الإشعارات في الخلفية
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // طلب إذن الإشعارات
    await _requestPermission();

    // تسجيل معالجات الإشعارات
    _registerNotificationHandlers();

    // الحصول على رمز الجهاز وتسجيله
    await getAndRegisterToken();

    // الاشتراك في المواضيع الافتراضية
    await subscribeToDefaultTopics();
  }



  // طلب إذن الإشعارات
  Future<void> _requestPermission() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('إعدادات إذن الإشعارات: ${settings.authorizationStatus}');
  }

  // تسجيل معالجات الإشعارات
  void _registerNotificationHandlers() {
    // معالجة الإشعارات عندما يكون التطبيق في المقدمة
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('تم استلام إشعار في المقدمة: ${message.messageId}');
      _showNotification(message);
    });

    // معالجة النقر على الإشعار عندما يكون التطبيق في الخلفية ولكن مفتوح
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('تم فتح التطبيق من الإشعار: ${message.messageId}');

      // تعليم الإشعار كمقروء
      _markNotificationAsRead(message.messageId);

      // التنقل إلى صفحة الإشعارات
      _navigateToNotificationsPage(null);
    });

    // التحقق من الإشعارات التي تم النقر عليها عندما كان التطبيق مغلقًا
    _firebaseMessaging.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        debugPrint('تم فتح التطبيق من الإشعار (التطبيق مغلق): ${message.messageId}');

        // تعليم الإشعار كمقروء
        _markNotificationAsRead(message.messageId);

        // التنقل إلى صفحة الإشعارات
        _navigateToNotificationsPage(null);
      }
    });
  }

  // تعليم الإشعار كمقروء
  Future<void> _markNotificationAsRead(String? notificationId) async {
    if (notificationId == null || notificationId.isEmpty) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList('notifications') ?? [];

      // تحويل الإشعارات إلى قائمة
      List<app_model.NotificationModel> notifications = notificationsJson
          .map((json) => app_model.NotificationModel.fromJson(json))
          .toList();

      // البحث عن الإشعار وتعليمه كمقروء
      final index = notifications.indexWhere((n) => n.id == notificationId);
      if (index >= 0) {
        notifications[index] = notifications[index].copyWith(isRead: true);

        // حفظ الإشعارات المحدثة
        final updatedNotificationsJson = notifications.map((n) => n.toJson()).toList();
        await prefs.setStringList('notifications', updatedNotificationsJson);

        debugPrint('تم تعليم الإشعار كمقروء: $notificationId');
      }
    } catch (e) {
      debugPrint('خطأ في تعليم الإشعار كمقروء: $e');
    }
  }

  // التنقل إلى صفحة الإشعارات
  void _navigateToNotificationsPage(BuildContext? context) {
    if (context != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const Notifications(),
        ),
      );
    } else {
      debugPrint('لا يوجد سياق متاح للتنقل إلى صفحة الإشعارات');
    }
  }

  // عرض الإشعار المحلي
  Future<void> _showNotification(RemoteMessage message) async {
    RemoteNotification? notification = message.notification;

    try {
      // حفظ الإشعار في التخزين المحلي
      await _saveNotificationToStorage(message);

      // تحديث مزود الإشعارات
      await _updateNotificationProvider();

      // عرض الإشعار باستخدام خدمة الإشعارات المخصصة
      if (notification != null) {
        debugPrint('تم استلام إشعار: ${notification.title} - ${notification.body}');

        // استخدام خدمة الإشعارات المحلية لعرض الإشعار
        final localNotificationService = LocalNotificationService();
        await localNotificationService.initialize();

        // تفويض عرض الإشعار إلى خدمة الإشعارات المحلية
        await localNotificationService.showNotification(
          id: message.hashCode,
          title: notification.title ?? 'إشعار جديد',
          body: notification.body ?? '',
          payload: {
            'messageId': message.messageId ?? '',
            'data': message.data,
          },
        );
      }
    } catch (e) {
      debugPrint('خطأ في عرض الإشعار: $e');
    }
  }

  // تحديث مزود الإشعارات
  Future<void> _updateNotificationProvider() async {
    try {
      // تحديث علامة في SharedPreferences تشير إلى أن هناك إشعارات جديدة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notifications_updated', true);
      await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);

      // حساب عدد الإشعارات غير المقروءة
      final notificationsJson = prefs.getStringList('notifications') ?? [];
      final notifications = notificationsJson
          .map((json) => app_model.NotificationModel.fromJson(json))
          .toList();
      final unreadCount = notifications.where((n) => !n.isRead).length;

      // حفظ عدد الإشعارات غير المقروءة
      await prefs.setInt('unread_notifications_count', unreadCount);

      debugPrint('تم تحديث علامة الإشعارات الجديدة. عدد الإشعارات غير المقروءة: $unreadCount');
    } catch (e) {
      debugPrint('خطأ في تحديث مزود الإشعارات: $e');
    }
  }

  // الحصول على رمز الجهاز وتسجيله
  Future<void> getAndRegisterToken() async {
    try {
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        debugPrint('رمز FCM: $token');

        // تخزين الرمز محليًا
        await storage.write(key: 'fcm_token', value: token);

        // إرسال الرمز إلى السيرفر
        await _sendTokenToServer(token);

        // الاستماع لتغييرات الرمز
        _firebaseMessaging.onTokenRefresh.listen((newToken) {
          debugPrint('تم تحديث رمز FCM: $newToken');
          _sendTokenToServer(newToken);
        });
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على رمز FCM: $e');
    }
  }

  // إرسال الرمز إلى السيرفر
  Future<void> _sendTokenToServer(String token) async {
    try {
      // التحقق من تسجيل الدخول
      const storage = FlutterSecureStorage();
      String? apiToken = await storage.read(key: "api_token");

      if (apiToken != null) {
        // إرسال الرمز إلى السيرفر
        // يمكن استخدام LoginAndCheckAndRegi لإرسال الرمز
        // مثال:
        // await LoginAndCheckAndRegi().updateFcmToken(token);

        // يمكن إضافة دالة updateFcmToken في LoginAndCheckAndRegi
      }
    } catch (e) {
      debugPrint('خطأ في إرسال رمز FCM إلى السيرفر: $e');
    }
  }

  // تسجيل الاشتراك في موضوع معين
  Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
    debugPrint('تم الاشتراك في الموضوع: $topic');
  }

  // إلغاء الاشتراك من موضوع معين
  Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
    debugPrint('تم إلغاء الاشتراك من الموضوع: $topic');
  }

  // الاشتراك في المواضيع الافتراضية عند فتح التطبيق لأول مرة
  Future<void> subscribeToDefaultTopics() async {
    try {
      // التحقق مما إذا كان المستخدم قد اشترك في المواضيع من قبل
      final prefs = await SharedPreferences.getInstance();
      final hasSubscribedToTopics = prefs.getBool('has_subscribed_to_topics') ?? false;

      if (!hasSubscribedToTopics) {
        // الاشتراك في المواضيع الثلاثة
        await subscribeToTopic('jobs');
        await subscribeToTopic('jobs_news');
        await subscribeToTopic('news');

        // تخزين حالة الاشتراك
        await prefs.setBool('has_subscribed_to_topics', true);

        // تخزين حالة الاشتراك في كل موضوع
        await prefs.setBool('subscribed_to_jobs', true);
        await prefs.setBool('subscribed_to_jobs_news', true);
        await prefs.setBool('subscribed_to_news', true);

        debugPrint('تم الاشتراك في المواضيع الافتراضية: jobs, jobs_news, news');
      } else {
        // التحقق من حالة الاشتراك في كل موضوع
        final isSubscribedToJobs = prefs.getBool('subscribed_to_jobs') ?? true;
        final isSubscribedToJobsNews = prefs.getBool('subscribed_to_jobs_news') ?? true;
        final isSubscribedToNews = prefs.getBool('subscribed_to_news') ?? true;

        // التأكد من أن المستخدم مشترك في المواضيع التي اختارها
        if (isSubscribedToJobs) {
          await subscribeToTopic('jobs');
        } else {
          await unsubscribeFromTopic('jobs');
        }

        if (isSubscribedToJobsNews) {
          await subscribeToTopic('jobs_news');
        } else {
          await unsubscribeFromTopic('jobs_news');
        }

        if (isSubscribedToNews) {
          await subscribeToTopic('news');
        } else {
          await unsubscribeFromTopic('news');
        }

        // التحقق من اشتراكات البلد
        await _checkCountrySubscriptions(prefs);

        debugPrint('تم تحديث اشتراكات المواضيع وفقًا لتفضيلات المستخدم');
      }
    } catch (e) {
      debugPrint('خطأ في الاشتراك في المواضيع الافتراضية: $e');
    }
  }

  // التحقق من اشتراكات البلد
  Future<void> _checkCountrySubscriptions(SharedPreferences prefs) async {
    try {
      // الحصول على جميع المفاتيح المخزنة
      final keys = prefs.getKeys();

      // البحث عن مفاتيح اشتراكات البلد
      final countrySubscriptionKeys = keys.where((key) => key.startsWith('subscribed_to_country_')).toList();

      // التحقق من كل اشتراك بلد
      for (final key in countrySubscriptionKeys) {
        final isSubscribed = prefs.getBool(key) ?? false;
        final countryCode = key.replaceFirst('subscribed_to_country_', '');

        if (isSubscribed) {
          await subscribeToTopic(countryCode);
          debugPrint('تم الاشتراك في موضوع البلد: $countryCode');
        } else {
          await unsubscribeFromTopic(countryCode);
          debugPrint('تم إلغاء الاشتراك من موضوع البلد: $countryCode');
        }
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من اشتراكات البلد: $e');
    }
  }
}
