import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

/// خدمة إعلانات Google AdMob المركزية
/// 
/// تدير جميع أنواع الإعلانات في التطبيق:
/// - إعلانات فتح التطبيق (App Open Ads)
/// - الإعلانات البينية (Interstitial Ads)
/// - إعلانات البانر (Banner Ads)
/// - الإعلانات التحفيزية (Rewarded Ads)
/// - الإعلانات التحفيزية البينية (Rewarded Interstitial Ads)
/// - الإعلانات الأصلية (Native Ads)
class GoogleAdService {
  static final GoogleAdService _instance = GoogleAdService._internal();
  factory GoogleAdService() => _instance;
  GoogleAdService._internal();

  // ========== معرفات الإعلانات ==========
  // يمكن تغيير هذه المعرفات فقط لاستخدام إعلانات حقيقية
  
  /// معرفات الإعلانات التجريبية - للاختبار فقط
  /// استبدل هذه بمعرفات إعلانات حقيقية من Google AdMob
  static const bool _useTestAds = true; // تم التغيير لاستخدام إعلانات حقيقية
  
  // معرفات الإعلانات التجريبية (Test Ads)
  static const Map<String, Map<String, String>> _testAdIds = {
    'android': {
      'app_open': 'ca-app-pub-3940256099942544/9257395921',
      'interstitial': 'ca-app-pub-3940256099942544/1033173712',
      'banner': 'ca-app-pub-3940256099942544/6300978111',
      'rewarded': 'ca-app-pub-3940256099942544/5224354917',
      'rewarded_interstitial': 'ca-app-pub-3940256099942544/5354046379',
      'native': 'ca-app-pub-3940256099942544/2247696110',
    },
    'ios': {
      'app_open': 'ca-app-pub-3940256099942544/5662855259',
      'interstitial': 'ca-app-pub-3940256099942544/4411468910',
      'banner': 'ca-app-pub-3940256099942544/2934735716',
      'rewarded': 'ca-app-pub-3940256099942544/1712485313',
      'rewarded_interstitial': 'ca-app-pub-3940256099942544/6978759866',
      'native': 'ca-app-pub-3940256099942544/3986624511',
    },
  };

  // معرفات الإعلانات الحقيقية (Production Ads)
  // استبدل هذه بمعرفات إعلانات حقيقية من Google AdMob
  static const Map<String, Map<String, String>> _productionAdIds = {
    'android': {
      'app_open': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف إعلان فتح التطبيق للأندرويد
      'interstitial': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان البيني للأندرويد
      'banner': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف إعلان البانر للأندرويد
      'rewarded': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان التحفيزي للأندرويد
      'rewarded_interstitial': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان التحفيزي البيني للأندرويد
      'native': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان الأصلي للأندرويد
    },
    'ios': {
      'app_open': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف إعلان فتح التطبيق للـ iOS
      'interstitial': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان البيني للـ iOS
      'banner': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف إعلان البانر للـ iOS
      'rewarded': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان التحفيزي للـ iOS
      'rewarded_interstitial': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان التحفيزي البيني للـ iOS
      'native': 'ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx', // ضع معرف الإعلان الأصلي للـ iOS
    },
  };

  // ========== Getters لمعرفات الإعلانات ==========
  
  /// الحصول على معرف الإعلان بناءً على النوع والنظام
  String _getAdUnitId(String adType) {
    final platform = Platform.isAndroid ? 'android' : 'ios';
    final adIds = _useTestAds ? _testAdIds : _productionAdIds;
    
    final adId = adIds[platform]?[adType];
    if (adId == null) {
      throw Exception('معرف الإعلان غير موجود: $adType لنظام $platform');
    }
    
    return adId;
  }

  String get appOpenAdUnitId => _getAdUnitId('app_open');
  String get interstitialAdUnitId => _getAdUnitId('interstitial');
  String get bannerAdUnitId => _getAdUnitId('banner');
  String get rewardedAdUnitId => _getAdUnitId('rewarded');
  String get rewardedInterstitialAdUnitId => _getAdUnitId('rewarded_interstitial');
  String get nativeAdUnitId => _getAdUnitId('native');

  // ========== متغيرات الإعلانات ==========
  
  // إعلانات فتح التطبيق
  AppOpenAd? _appOpenAd;
  bool _isShowingAppOpenAd = false;
  bool _isAppOpenAdLoaded = false;
  DateTime? _appOpenAdLoadTime;
  
  // الإعلانات البينية
  InterstitialAd? _interstitialAd;
  int _numInterstitialLoadAttempts = 0;
  static const int _maxInterstitialLoadAttempts = 3;
  
  // الإعلانات التحفيزية
  RewardedAd? _rewardedAd;
  bool _isRewardedAdLoaded = false;
  
  // الإعلانات التحفيزية البينية
  RewardedInterstitialAd? _rewardedInterstitialAd;
  bool _isRewardedInterstitialAdLoaded = false;
  
  // الإعلانات الأصلية
  NativeAd? _nativeAd;
  bool _isNativeAdLoaded = false;

  // ========== تهيئة الخدمة ==========
  
  /// تهيئة خدمة الإعلانات
  Future<void> initialize() async {
    try {
      // تهيئة MobileAds SDK
      await MobileAds.instance.initialize();

      // تسجيل معالج حالة التطبيق لإعلانات فتح التطبيق
      AppStateEventNotifier.startListening();

      // تحميل إعلانات أساسية
      await _preloadAds();
      
    } catch (e) {
      rethrow;
    }
  }

  /// تحميل مسبق للإعلانات الأساسية
  Future<void> _preloadAds() async {
    // تحميل إعلانات متوازية لتحسين الأداء
    await Future.wait([
      _loadAppOpenAd(),
      loadInterstitialAd(),
      loadRewardedAd(),
    ]);
  }

  // ========== إعلانات فتح التطبيق ==========
  
  /// تحميل إعلان فتح التطبيق
  Future<void> _loadAppOpenAd() async {
    if (_isAppOpenAdLoaded && _appOpenAd != null) {
      return;
    }

    _cleanupAppOpenAd();

    try {
      await AppOpenAd.load(
        adUnitId: appOpenAdUnitId,
        orientation: AppOpenAd.orientationPortrait,
        request: const AdRequest(),
        adLoadCallback: AppOpenAdLoadCallback(
          onAdLoaded: (ad) {
            _appOpenAd = ad;
            _isAppOpenAdLoaded = true;
            _appOpenAdLoadTime = DateTime.now();
          },
          onAdFailedToLoad: (error) {
            _isAppOpenAdLoaded = false;
            _appOpenAd = null;
            
            // إعادة المحاولة بعد تأخير
            Future.delayed(const Duration(seconds: 10), _loadAppOpenAd);
          },
        ),
      );
    } catch (e) {
      _isAppOpenAdLoaded = false;
      _appOpenAd = null;
      
      // إعادة المحاولة بعد تأخير
      Future.delayed(const Duration(seconds: 10), _loadAppOpenAd);
    }
  }

  /// عرض إعلان فتح التطبيق
  Future<bool> showAppOpenAd() async {
    if (!_isAppOpenAdLoaded || _appOpenAd == null || _isShowingAppOpenAd) {
      return false;
    }

    // التحقق من انتهاء صلاحية الإعلان (4 ساعات)
    if (_appOpenAdLoadTime != null) {
      final timeSinceLoad = DateTime.now().difference(_appOpenAdLoadTime!);
      if (timeSinceLoad.inHours >= 4) {
        _cleanupAppOpenAd();
        _loadAppOpenAd();
        return false;
      }
    }

    _isShowingAppOpenAd = true;

    _appOpenAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        // الإعلان يُعرض
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        _isShowingAppOpenAd = false;
        _cleanupAppOpenAd();
        _loadAppOpenAd();
      },
      onAdDismissedFullScreenContent: (ad) {
        _isShowingAppOpenAd = false;
        _cleanupAppOpenAd();
        _loadAppOpenAd();
      },
    );

    try {
      await _appOpenAd!.show();
      return true;
    } catch (e) {
      _isShowingAppOpenAd = false;
      _cleanupAppOpenAd();
      _loadAppOpenAd();
      return false;
    }
  }

  /// تنظيف إعلان فتح التطبيق
  void _cleanupAppOpenAd() {
    _appOpenAd?.dispose();
    _appOpenAd = null;
    _isAppOpenAdLoaded = false;
    _appOpenAdLoadTime = null;
  }

  // ========== الإعلانات البينية ==========
  
  /// تحميل إعلان بيني
  Future<void> loadInterstitialAd() async {
    if (_interstitialAd != null) {
      return;
    }

    _numInterstitialLoadAttempts++;

    try {
      await InterstitialAd.load(
        adUnitId: interstitialAdUnitId,
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) {
            _interstitialAd = ad;
            _numInterstitialLoadAttempts = 0;
          },
          onAdFailedToLoad: (error) {
            _interstitialAd = null;
            
            if (_numInterstitialLoadAttempts < _maxInterstitialLoadAttempts) {
              Future.delayed(const Duration(seconds: 5), loadInterstitialAd);
            }
          },
        ),
      );
    } catch (e) {
      _interstitialAd = null;
      
      if (_numInterstitialLoadAttempts < _maxInterstitialLoadAttempts) {
        Future.delayed(const Duration(seconds: 5), loadInterstitialAd);
      }
    }
  }

  /// عرض إعلان بيني
  Future<bool> showInterstitialAd() async {
    if (_interstitialAd == null) {
      await loadInterstitialAd();
      return false;
    }

    _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        // الإعلان يُعرض
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        ad.dispose();
        _interstitialAd = null;
      },
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _interstitialAd = null;
      },
    );

    try {
      await _interstitialAd!.show();
      return true;
    } catch (e) {
      _interstitialAd?.dispose();
      _interstitialAd = null;
      return false;
    }
  }

  // ========== الإعلانات التحفيزية ==========
  
  /// تحميل إعلان تحفيزي
  Future<void> loadRewardedAd() async {
    if (_rewardedAd != null) {
      return;
    }

    try {
      await RewardedAd.load(
        adUnitId: rewardedAdUnitId,
        request: const AdRequest(),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (ad) {
            _rewardedAd = ad;
            _isRewardedAdLoaded = true;
          },
          onAdFailedToLoad: (error) {
            _rewardedAd = null;
            _isRewardedAdLoaded = false;
            
            Future.delayed(const Duration(seconds: 10), loadRewardedAd);
          },
        ),
      );
    } catch (e) {
      _rewardedAd = null;
      _isRewardedAdLoaded = false;
      
      Future.delayed(const Duration(seconds: 10), loadRewardedAd);
    }
  }

  /// عرض إعلان تحفيزي
  Future<bool> showRewardedAd({Function(AdWithoutView, RewardItem)? onUserEarnedReward}) async {
    if (_rewardedAd == null || !_isRewardedAdLoaded) {
      await loadRewardedAd();
      return false;
    }

    _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        // الإعلان يُعرض
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        ad.dispose();
        _rewardedAd = null;
        _isRewardedAdLoaded = false;
        loadRewardedAd();
      },
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _rewardedAd = null;
        _isRewardedAdLoaded = false;
        loadRewardedAd();
      },
    );

    try {
      await _rewardedAd!.show(
        onUserEarnedReward: onUserEarnedReward ?? (ad, reward) {
          // المكافأة الافتراضية
        },
      );
      return true;
    } catch (e) {
      _rewardedAd?.dispose();
      _rewardedAd = null;
      _isRewardedAdLoaded = false;
      loadRewardedAd();
      return false;
    }
  }

  // ========== إعلانات البانر ==========
  
  /// إنشاء إعلان بانر
  BannerAd createBannerAd({
    AdSize adSize = AdSize.banner,
    Function(Ad)? onAdLoaded,
    Function(Ad, LoadAdError)? onAdFailedToLoad,
  }) {
    return BannerAd(
      adUnitId: bannerAdUnitId,
      size: adSize,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          onAdLoaded?.call(ad);
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
          onAdFailedToLoad?.call(ad, error);
        },
      ),
    );
  }

  // ========== الإعلانات الأصلية ==========
  
  /// تحميل إعلان أصلي
  Future<void> loadNativeAd() async {
    if (_nativeAd != null) {
      return;
    }

    try {
      _nativeAd = NativeAd(
        adUnitId: nativeAdUnitId,
        listener: NativeAdListener(
          onAdLoaded: (ad) {
            _isNativeAdLoaded = true;
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();
            _nativeAd = null;
            _isNativeAdLoaded = false;
            
            Future.delayed(const Duration(seconds: 10), loadNativeAd);
          },
        ),
        request: const AdRequest(),
        nativeTemplateStyle: NativeTemplateStyle(
          templateType: TemplateType.medium,
          mainBackgroundColor: Colors.white,
          cornerRadius: 10.0,
        ),
      );

      await _nativeAd!.load();
    } catch (e) {
      _nativeAd?.dispose();
      _nativeAd = null;
      _isNativeAdLoaded = false;
      
      Future.delayed(const Duration(seconds: 10), loadNativeAd);
    }
  }

  /// الحصول على الإعلان الأصلي
  NativeAd? get nativeAd => _isNativeAdLoaded ? _nativeAd : null;

  // ========== تنظيف الموارد ==========
  
  /// تنظيف جميع الإعلانات
  void dispose() {
    _cleanupAppOpenAd();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
    _rewardedInterstitialAd?.dispose();
    _nativeAd?.dispose();
    
    _interstitialAd = null;
    _rewardedAd = null;
    _rewardedInterstitialAd = null;
    _nativeAd = null;
    _isRewardedAdLoaded = false;
    _isRewardedInterstitialAdLoaded = false;
    _isNativeAdLoaded = false;
  }

  // ========== حالة الإعلانات ==========
  
  /// التحقق من جاهزية الإعلانات
  bool get isAppOpenAdReady => _isAppOpenAdLoaded && _appOpenAd != null && !_isShowingAppOpenAd;
  bool get isInterstitialAdReady => _interstitialAd != null;
  bool get isRewardedAdReady => _isRewardedAdLoaded && _rewardedAd != null;
  bool get isNativeAdReady => _isNativeAdLoaded && _nativeAd != null;

  /// معلومات حالة الخدمة
  Map<String, dynamic> get serviceStatus => {
    'appOpenAdReady': isAppOpenAdReady,
    'interstitialAdReady': isInterstitialAdReady,
    'rewardedAdReady': isRewardedAdReady,
    'nativeAdReady': isNativeAdReady,
    'useTestAds': _useTestAds,
  };
}