import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/components/textfeild.dart';
import 'package:wzzff/components/textfeild_register.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:wzzff/core/utils/app_messages.dart';
import 'package:wzzff/models/SeekerModel.dart';
import '../privacy_policy/privacy_policy.dart';
import '../privacy_policy/terms_OfUsing.dart';
import '../../../components/iconbott.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import '../../../Apis/ProfileApi.dart';
import 'profile_steps/personal_info_step.dart';
import 'profile_steps/contact_info_step.dart';
import 'profile_steps/qualifications_step.dart';
import 'profile_steps/preferences_step.dart';
import 'profile_steps/profile_step_header.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class ProfileSeekerInformations extends StatefulWidget {
  const ProfileSeekerInformations({super.key});

  @override
  State<ProfileSeekerInformations> createState() =>
      _ProfileSeekerInformationsState();
}

class _ProfileSeekerInformationsState extends State<ProfileSeekerInformations> {
  final _keyRegisterForm = GlobalKey<FormState>();

  // متغير لتتبع الخطوة الحالية
  int _currentStep = 0;

  // حالة التحميل
  bool isLoading = true;

  // حالة الحفظ
  bool isSaving = false;

  // متغيرات لتخزين بيانات المستخدم
  SeekerModel? _userModel;
  final userDataMap = <String, dynamic>{};

  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    _loadProfileData();
  }

  Future<void> _loadProfileData() async {
    setState(() => isLoading = true);
    final prefs = await SharedPreferences.getInstance();
    Map<String, dynamic>? draft;
    final draftString = prefs.getString('profile_draft');
    if (draftString != null) {
      try {
        draft = Map<String, dynamic>.from(await Future.value(jsonDecode(draftString)));
      } catch (_) {}
    }
    try {
      final user = await ProfileApi().getMyProfileInformation();
      if (draft != null) {
        userDataMap.addAll(draft);
      }
      setState(() {
        _userModel = user;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _saveDraft() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('profile_draft', jsonEncode(userDataMap));
  }

  Future<void> _clearDraft() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('profile_draft');
  }

  Future<bool> _onWillPop() async {
    if (_hasUnsavedChanges) {
      final shouldLeave = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('تأكيد الخروج'),
          content: Text('لديك تغييرات غير محفوظة، هل تريد الخروج بدون حفظ؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('خروج'),
            ),
          ],
        ),
      );
      return shouldLeave ?? false;
    }
    return true;
  }

  void _onDataChanged(Map<String, dynamic> data) {
    userDataMap.addAll(data);
    _hasUnsavedChanges = true;
    _saveDraft();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).scaffoldBackgroundColor
            : Colors.white,
        appBar: AppBar(
          elevation: 0,
          centerTitle: true,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              onPressed: () async {
                if (await _onWillPop()) {
                  Navigator.pop(context);
                }
              },
              icon: const Icon(
                Icons.arrow_forward,
                color: Colors.white,
              ),
              tooltip: 'رجوع',
            ),
          ],
          title: Text(
            "المعلومات الشخصية",
            style: GoogleFonts.tajawal(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
        body: isLoading
            ? Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.primary,
                ),
              )
            : _userModel == null
                ? Center(
                    child: Text(
                      "لا توجد بيانات متاحة",
                      style: GoogleFonts.tajawal(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyMedium?.color
                            : Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  )
                : Directionality(
                    textDirection: TextDirection.rtl,
                    child: Form(
                      key: _keyRegisterForm,
                      child: Column(
                        children: [
                          ProfileStepHeader(
                            currentStep: _currentStep,
                          ),
                          Expanded(
                            child: _buildStepContent(_userModel!),
                          ),
                          _buildBottomNavigation(),
                        ],
                      ),
                    ),
                  ),
      ),
    );
  }

  Widget _buildStepContent(SeekerModel user) {
    // دمج بيانات userDataMap مع بيانات SeekerModel لإعطاء الأولوية للمدخلات المؤقتة
    final mergedUser = user.copyWithMap(userDataMap);
    switch (_currentStep) {
      case 0:
        return PersonalInfoStep(
          user: mergedUser,
          onDataChanged: _onDataChanged,
        );
      case 1:
        return ContactInfoStep(
          user: mergedUser,
          onDataChanged: _onDataChanged,
        );
      case 2:
        return QualificationsStep(
          user: mergedUser,
          onDataChanged: _onDataChanged,
        );
      case 3:
        return PreferencesStep(
          user: mergedUser,
          onDataChanged: _onDataChanged,
          onSubmit: _updateProfile,
        );
      default:
        return PersonalInfoStep(
          user: mergedUser,
          onDataChanged: _onDataChanged,
        );
    }
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).cardTheme.color
            : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(
                Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (_currentStep > 0)
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _currentStep--;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
                foregroundColor: Theme.of(context).colorScheme.primary,
                elevation: 0,
                side: BorderSide(
                  color: Theme.of(context).colorScheme.primary,
                  width: 1,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Builder(
                    builder: (context) {
                      final isRtl = Directionality.of(context) == TextDirection.rtl;
                      return Icon(
                        isRtl ? Icons.arrow_back_ios_new : Icons.arrow_back_ios_new,
                        size: 16,
                      );
                    },
                  ),
                  const SizedBox(width: 8),
                  Text(
                    "السابق",
                    style: GoogleFonts.tajawal(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            )
          else
            const SizedBox(width: 100),

          if (_currentStep < 3)
            ElevatedButton(
              onPressed: () {
                // التحقق من صحة البيانات قبل الانتقال للخطوة التالية
                if (_keyRegisterForm.currentState!.validate()) {
                  setState(() {
                    _currentStep++;
                  });
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    "التالي",
                    style: GoogleFonts.tajawal(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Builder(
                    builder: (context) {
                      final isRtl = Directionality.of(context) == TextDirection.rtl;
                      return Icon(
                        isRtl ? Icons.arrow_forward_ios : Icons.arrow_forward_ios,
                        size: 16,
                      );
                    },
                  ),
                ],
              ),
            )
          else
            ElevatedButton(
              onPressed: isSaving ? null : _updateProfile,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Row(
                      children: [
                        Text(
                          "حفظ المعلومات",
                          style: GoogleFonts.tajawal(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Icon(Icons.check, size: 16),
                      ],
                    ),
            ),
        ],
      ),
    );
  }

  // إرسال البيانات للسيرفر
  Future<void> _updateProfile() async {
    if (!_keyRegisterForm.currentState!.validate()) {
      AppMessages.showWarning("يرجى تعبئة جميع الحقول المطلوبة");
      return;
    }

    setState(() {
      isSaving = true;
    });

    try {
      // استدعاء API لتحديث البيانات
      final dataToSend = Map<String, dynamic>.from(userDataMap);
      dataToSend.remove("email");
      await ProfileApi().updateSeekerProfile(dataToSend);

      // عرض رسالة نجاح
      AppMessages.showSuccess("تم تحديث الملف الشخصي بنجاح");

      // التحقق من أن الـ Widget لا يزال مثبتًا قبل استخدام السياق
      if (mounted) {
        await _clearDraft();
        _hasUnsavedChanges = false;
        // العودة للصفحة السابقة
        Navigator.pop(context);
      }
    } catch (e) {
      // عرض رسالة خطأ
      AppMessages.showError("حدث خطأ أثناء تحديث الملف الشخصي");
    } finally {
      if (mounted) {
        setState(() {
          isSaving = false;
        });
      }
    }
  }
}
