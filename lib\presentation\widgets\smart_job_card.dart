import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/models/ai_match_result.dart';
import 'package:wzzff/presentation/widgets/job_card.dart';

/// بطاقة وظيفة ذكية تعرض معلومات التطابق والذكاء الاصطناعي
class SmartJobCard extends StatelessWidget {
  final AIMatchResult result;
  final bool showMatchInfo;

  const SmartJobCard({
    Key? key,
    required this.result,
    this.showMatchInfo = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getMatchBorderColor(result.matchScore),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // شريط معلومات التطابق الذكي
          if (showMatchInfo) _buildMatchInfoHeader(context),
          
          // بطاقة الوظيفة الأساسية
          JobCard(
            title: result.job.title ?? '',
            des: result.job.description ?? '',
            time: result.job.time ?? '',
            code_address: result.job.code_address ?? '',
            email: result.job.email ?? '',
            number: result.job.number ?? '',
            salary_currency: result.job.salary_currency ?? '',
            salary: result.job.salary ?? '',
            cat: result.job.cat ?? '',
            gender: result.job.gender ?? '',
            state_name: result.job.state_name ?? '',
            country_name: result.job.country_name ?? '',
            job_type_name: result.job.job_type_name ?? '',
            city_name: result.job.city_name ?? '',
            company_name: result.job.company_name ?? '',
            edu: result.job.edu ?? '',
            exp: result.job.exp ?? '',
            end_at: result.job.end_at ?? '',
            created_at_date: result.job.created_at_date ?? '',
            slug: result.job.slug,
            showCity: true,
          ),
          
          // معلومات التطابق التفصيلية
          if (showMatchInfo) _buildMatchDetails(context),
        ],
      ),
    );
  }

  Widget _buildMatchInfoHeader(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final matchColor = _getMatchBorderColor(result.matchScore);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: matchColor.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.psychology,
            size: 20,
            color: matchColor,
          ),
          const SizedBox(width: 8),
          Text(
            'تطابق ذكي: ${result.matchPercentage}',
            style: GoogleFonts.tajawal(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: matchColor,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: matchColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  result.matchIcon,
                  style: const TextStyle(fontSize: 12),
                ),
                const SizedBox(width: 4),
                Text(
                  MatchCategoryExtension.fromScore(result.matchScore).arabicName,
                  style: GoogleFonts.tajawal(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMatchDetails(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode 
          ? Colors.grey[900]?.withOpacity(0.5) 
          : Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(14),
          bottomRight: Radius.circular(14),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // شرح التطابق
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  result.explanation,
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),
          
          // عوامل الصلة
          if (result.relevanceFactors.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              'عوامل التطابق:',
              style: GoogleFonts.tajawal(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 6),
            Wrap(
              spacing: 6,
              runSpacing: 4,
              children: result.relevanceFactors.map((factor) => 
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    factor,
                    style: GoogleFonts.tajawal(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                )
              ).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Color _getMatchBorderColor(double score) {
    if (score >= 0.8) return Colors.green;
    if (score >= 0.6) return Colors.orange;
    return Colors.red;
  }
} 