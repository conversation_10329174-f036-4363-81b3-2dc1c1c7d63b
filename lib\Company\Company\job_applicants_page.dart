import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/core/theme/app_colors.dart';
import 'package:wzzff/Apis/CompanyApi.dart';
import 'package:url_launcher/url_launcher.dart';

class JobApplicantsPage extends StatefulWidget {
  final int jobId;
  final String jobTitle;
  final Map<String, dynamic> jobData;

  const JobApplicantsPage({
    Key? key,
    required this.jobId,
    required this.jobTitle,
    required this.jobData,
  }) : super(key: key);

  @override
  State<JobApplicantsPage> createState() => _JobApplicantsPageState();
}

class _JobApplicantsPageState extends State<JobApplicantsPage> with TickerProviderStateMixin {
  final CompanyApi _companyApi = CompanyApi();
  List<Map<String, dynamic>> _applicants = [];
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'الكل';
  String _sortBy = 'التاريخ';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<String> _filterOptions = ['الكل', 'قيد المراجعة', 'مقبول', 'مرفوض', 'قيد المقابلة'];
  final List<String> _sortOptions = ['التاريخ', 'الاسم', 'الراتب المتوقع'];

  /// تحويل حالة المتقدم من الإنجليزية إلى العربية
  String _mapStatusToArabic(String status) {
    final arabicStatus = () {
      switch (status?.toLowerCase()) {
        case 'pending':
          return 'قيد المراجعة';
        case 'accepted':
        case 'approved':
          return 'مقبول';
        case 'rejected':
        case 'declined':
          return 'مرفوض';
        case 'interview':
        case 'interviewing':
          return 'قيد المقابلة';
        case 'قيد المراجعة':
        case 'مقبول':
        case 'مرفوض':
        case 'قيد المقابلة':
          return status; // Already in Arabic
        default:
          return 'قيد المراجعة'; // Default fallback
      }
    }();
    
   // print('🔄 تحويل الحالة: "$status" -> "$arabicStatus"');
    return arabicStatus;
  }

  /// تحويل حالة المتقدم من العربية إلى الإنجليزية للإرسال للخادم
  String _mapStatusToEnglish(String status) {
    switch (status) {
      case 'قيد المراجعة':
        return 'pending';
      case 'مقبول':
        return 'accepted';
      case 'مرفوض':
        return 'rejected';
      case 'قيد المقابلة':
        return 'interview';
      default:
        return 'pending';
    }
  }

  /// عرض رسالة للمستخدم
  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _fetchApplicants();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
  }

  Future<void> _fetchApplicants() async {
    setState(() { _isLoading = true; _error = null; });
    
    try {
      final response = await _companyApi.getJobApplicants(widget.jobId);
      
      if (response['success'] == true) {
        setState(() {
          // تحويل حالات المتقدمين من الإنجليزية للعربية
          _applicants = List<Map<String, dynamic>>.from(response['data']).map((applicant) {
            applicant['status'] = _mapStatusToArabic(applicant['status']);
            return applicant;
          }).toList();
          _isLoading = false;
        });
        _animationController.forward();
      } else {
        setState(() {
          _error = response['message'] ?? 'حدث خطأ أثناء جلب بيانات المتقدمين';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'تعذر الاتصال بالخادم';
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> get _filteredAndSortedApplicants {
    var filtered = _applicants;
    
    // Apply filter
    if (_selectedFilter != 'الكل') {
      filtered = filtered.where((applicant) => applicant['status'] == _selectedFilter).toList();
    }
    
    // Apply sort
    filtered.sort((a, b) {
      switch (_sortBy) {
        case 'الاسم':
          return a['name'].compareTo(b['name']);
        case 'الراتب المتوقع':
          return int.parse(b['expectedSalary']).compareTo(int.parse(a['expectedSalary']));
        case 'التاريخ':
        default:
          return DateTime.parse(b['appliedAt']).compareTo(DateTime.parse(a['appliedAt']));
      }
    });
    
    return filtered;
  }

  Color _statusColor(String status) {
    switch (status) {
      case 'مقبول':
        return Colors.green;
      case 'مرفوض':
        return Colors.red;
      case 'قيد المقابلة':
        return Colors.blue;
      case 'قيد المراجعة':
      default:
        return Colors.orange;
    }
  }

  IconData _statusIcon(String status) {
    switch (status) {
      case 'مقبول':
        return Icons.check_circle;
      case 'مرفوض':
        return Icons.cancel;
      case 'قيد المقابلة':
        return Icons.video_call;
      case 'قيد المراجعة':
      default:
        return Icons.schedule;
    }
  }

  Widget _buildJobInfoCard() {
    final cardColor = AppColors.getCardColor(context);
    final textColor = AppColors.getTextColor(context);
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.work,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.jobTitle,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      widget.jobData['department'] ?? '',
                      style: TextStyle(
                        color: AppColors.getSecondaryTextColor(context),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildJobInfoChip(Icons.location_on, widget.jobData['location'], Colors.blue),
              const SizedBox(width: 8),
              _buildJobInfoChip(Icons.monetization_on, widget.jobData['salary'], Colors.green),
              const SizedBox(width: 8),
              _buildJobInfoChip(Icons.people, '${_applicants.length} متقدم', Colors.purple),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildJobInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsAndFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Statistics Row
          Row(
            children: [
              Expanded(child: _buildStatCard('إجمالي المتقدمين', _applicants.length.toString(), Icons.people, Colors.blue)),
              const SizedBox(width: 8),
              Expanded(child: _buildStatCard('قيد المراجعة', _applicants.where((a) => a['status'] == 'قيد المراجعة').length.toString(), Icons.schedule, Colors.orange)),
              const SizedBox(width: 8),
              Expanded(child: _buildStatCard('مقبول', _applicants.where((a) => a['status'] == 'مقبول').length.toString(), Icons.check_circle, Colors.green)),
              const SizedBox(width: 8),
              Expanded(child: _buildStatCard('مرفوض', _applicants.where((a) => a['status'] == 'مرفوض').length.toString(), Icons.cancel, Colors.red)),
            ],
          ),
          const SizedBox(height: 16),
          // Filters Row
          Row(
            children: [
              Expanded(
                child: _buildFilterDropdown(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSortDropdown(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    final cardColor = AppColors.getCardColor(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: AppColors.getSecondaryTextColor(context),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: AppColors.getCardColor(context),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: _selectedFilter,
          icon: const Icon(Icons.filter_list, size: 20),
          items: _filterOptions.map((filter) {
            return DropdownMenuItem<String>(
              value: filter,
              child: Text(filter, style: const TextStyle(fontSize: 14)),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedFilter = value!;
            });
          },
        ),
      ),
    );
  }

  Widget _buildSortDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: AppColors.getCardColor(context),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: _sortBy,
          icon: const Icon(Icons.sort, size: 20),
          items: _sortOptions.map((sort) {
            return DropdownMenuItem<String>(
              value: sort,
              child: Text(sort, style: const TextStyle(fontSize: 14)),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _sortBy = value!;
            });
          },
        ),
      ),
    );
  }

  Widget _buildApplicantCard(Map<String, dynamic> applicant, int index) {
    final cardColor = AppColors.getCardColor(context);
    final textColor = AppColors.getTextColor(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: isDarkMode 
                    ? Colors.black.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showApplicantDetails(applicant),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with name and status
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 24,
                            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            child: Text(
                              applicant['name'].split(' ')[0][0] + applicant['name'].split(' ')[1][0],
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  applicant['name'],
                                  style: TextStyle(
                                    color: textColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  applicant['experience'],
                                  style: TextStyle(
                                    color: AppColors.getSecondaryTextColor(context),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                            decoration: BoxDecoration(
                              color: _statusColor(applicant['status']).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _statusIcon(applicant['status']),
                                  color: _statusColor(applicant['status']),
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  applicant['status'],
                                  style: TextStyle(
                                    color: _statusColor(applicant['status']),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Application title
                      Text(
                        applicant['applicationTitle'],
                        style: TextStyle(
                          color: textColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Application description (truncated)
                      Text(
                        applicant['applicationDescription'],
                        style: TextStyle(
                          color: AppColors.getSecondaryTextColor(context),
                          fontSize: 13,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Details row
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Icon(Icons.email, size: 14, color: Colors.grey[600]),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    applicant['email'],
                                    style: TextStyle(
                                      color: AppColors.getSecondaryTextColor(context),
                                      fontSize: 12,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${applicant['expectedSalary']} ',
                              style: const TextStyle(
                                color: Colors.green,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Skills
                      Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: (applicant['skills'] as List).take(3).map((skill) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              skill,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontSize: 10,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Actions row
                      Row(
                        children: [
                          Text(
                            _formatDate(applicant['appliedAt']),
                            style: TextStyle(
                              color: AppColors.getSecondaryTextColor(context),
                              fontSize: 12,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(Icons.visibility, size: 18),
                            onPressed: () => _showApplicantDetails(applicant),
                            tooltip: 'عرض التفاصيل',
                          ),
                          const SizedBox(width: 8),
                          DropdownButton<String>(
                            value: _filterOptions.skip(1).contains(applicant['status']) 
                                ? applicant['status'] 
                                : 'قيد المراجعة',
                            underline: Container(),
                            icon: const Icon(Icons.arrow_drop_down, size: 18),
                            items: _filterOptions.skip(1).map((status) {
                              return DropdownMenuItem<String>(
                                value: status,
                                child: Text(status, style: const TextStyle(fontSize: 12)),
                              );
                            }).toList(),
                            onChanged: (newStatus) {
                                                        setState(() {
                            applicant['status'] = newStatus;
                          });
                          _updateApplicantStatus(applicant['id'], newStatus!, applicant['type']);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(String dateTimeString) {
    final date = DateTime.parse(dateTimeString);
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inMinutes} دقيقة';
    }
  }

  Future<void> _updateApplicantStatus(int applicantId, String newStatus, String applicantType) async {
    try {
      // تحويل الحالة من العربية للإنجليزية قبل الإرسال للخادم
      final englishStatus = _mapStatusToEnglish(newStatus);
      //print('🔄 تحديث حالة المتقدم: ID=$applicantId, Status=$englishStatus, Type=$applicantType');
      
      final response = await _companyApi.updateApplicantStatus(
        applicantId, 
        englishStatus, 
        type: applicantType
      );
      
      if (response['success'] == true) {
        // إعادة تحميل البيانات من السيرفر للتأكد من التحديث
        await _fetchApplicants();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تغيير حالة المتقدم إلى: $newStatus'),
            backgroundColor: _statusColor(newStatus),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response['message'] ?? 'حدث خطأ أثناء تحديث حالة المتقدم'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print('❌ خطأ في تحديث حالة المتقدم: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تعذر الاتصال بالخادم'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showApplicantDetails(Map<String, dynamic> applicant) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ApplicantDetailsSheet(applicant: applicant),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDarkMode 
          ? Theme.of(context).scaffoldBackgroundColor
          : const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Text(
          'المتقدمون',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Icon(
              Icons.arrow_forward,
              color: Colors.white,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _error!,
                        style: TextStyle(color: Colors.red, fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _fetchApplicants,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                )
              : _applicants.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 64,
                            color: AppColors.getTextColor(context).withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا يوجد متقدمون حتى الآن',
                            style: TextStyle(
                              color: AppColors.getTextColor(context),
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'ستظهر هنا قائمة المتقدمين عند التقديم',
                            style: TextStyle(
                              color: AppColors.getSecondaryTextColor(context),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _fetchApplicants,
                      child: CustomScrollView(
                        slivers: [
                          SliverToBoxAdapter(child: _buildJobInfoCard()),
                          SliverToBoxAdapter(child: _buildStatsAndFilters()),
                          SliverToBoxAdapter(child: const SizedBox(height: 16)),
                          SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final filteredApplicants = _filteredAndSortedApplicants;
                                if (index >= filteredApplicants.length) return null;
                                return _buildApplicantCard(filteredApplicants[index], index);
                              },
                              childCount: _filteredAndSortedApplicants.length,
                            ),
                          ),
                          const SliverToBoxAdapter(child: SizedBox(height: 20)),
                        ],
                      ),
                    ),
    );
  }
}

class ApplicantDetailsSheet extends StatefulWidget {
  final Map<String, dynamic> applicant;

  const ApplicantDetailsSheet({Key? key, required this.applicant}) : super(key: key);

  @override
  State<ApplicantDetailsSheet> createState() => _ApplicantDetailsSheetState();
}

class _ApplicantDetailsSheetState extends State<ApplicantDetailsSheet> {
  /// فتح تطبيق الإيميل
  Future<void> _launchEmail(String email) async {
    if (email.isEmpty) {
      _showMessage('لا يوجد بريد إلكتروني متاح', isError: true);
      return;
    }
    
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=بخصوص طلب الوظيفة',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
        //print('📧 تم فتح تطبيق الإيميل: $email');
      } else {
        _showMessage('لا يمكن فتح تطبيق الإيميل', isError: true);
      }
    } catch (e) {
      print('❌ خطأ في فتح الإيميل: $e');
      _showMessage('حدث خطأ في فتح تطبيق الإيميل', isError: true);
    }
  }

  /// فتح تطبيق الاتصال
  Future<void> _launchPhone(String phone) async {
    if (phone.isEmpty) {
      _showMessage('لا يوجد رقم هاتف متاح', isError: true);
      return;
    }

    final Uri phoneUri = Uri(scheme: 'tel', path: phone);

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
        //print('📞 تم فتح تطبيق الاتصال: $phone');
      } else {
        _showMessage('لا يمكن فتح تطبيق الاتصال', isError: true);
      }
    } catch (e) {
      print('❌ خطأ في فتح تطبيق الاتصال: $e');
      _showMessage('حدث خطأ في فتح تطبيق الاتصال', isError: true);
    }
  }

  /// فتح رابط السيرة الذاتية
  Future<void> _launchCV(String cvUrl) async {
    if (cvUrl.isEmpty || cvUrl == "null" || cvUrl == null) {
      _showMessage('لا توجد سيرة ذاتية متاحة', isError: true);
      return;
    }

    try {
      final Uri uri = Uri.parse(cvUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        //print('📄 تم فتح السيرة الذاتية: $cvUrl');
      } else {
        _showMessage('لا يمكن فتح السيرة الذاتية', isError: true);
      }
    } catch (e) {
      print('❌ خطأ في فتح السيرة الذاتية: $e');
      _showMessage('حدث خطأ في فتح السيرة الذاتية', isError: true);
    }
  }

  /// فتح رابط المعرض
  Future<void> _launchPortfolio(String portfolioUrl) async {
    if (portfolioUrl.isEmpty || portfolioUrl == "null" || portfolioUrl == null) {
      _showMessage('لا يوجد معرض متاح', isError: true);
      return;
    }

    try {
      final Uri uri = Uri.parse(portfolioUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        //print('🎨 تم فتح المعرض: $portfolioUrl');
      } else {
        _showMessage('لا يمكن فتح المعرض', isError: true);
      }
    } catch (e) {
      print('❌ خطأ في فتح المعرض: $e');
      _showMessage('حدث خطأ في فتح المعرض', isError: true);
    }
  }

  /// عرض رسالة للمستخدم
  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final applicant = widget.applicant;
    final cardColor = AppColors.getCardColor(context);
    final textColor = AppColors.getTextColor(context);
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  child: Text(
                    applicant['name'].split(' ')[0][0] + applicant['name'].split(' ')[1][0],
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        applicant['name'],
                        style: TextStyle(
                          color: textColor,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        applicant['education'],
                        style: TextStyle(
                          color: AppColors.getSecondaryTextColor(context),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailSection('معلومات الاتصال', [
                    _buildDetailRow(Icons.email, 'البريد الإلكتروني', applicant['email'], onTap: () => _launchEmail(applicant['email'])),
                    _buildDetailRow(Icons.phone, 'رقم الهاتف', applicant['phone'], onTap: () => _launchPhone(applicant['phone'])),
                  ]),
                  
                  const SizedBox(height: 20),
                  
                  _buildDetailSection('تفاصيل الطلب', [
                    _buildDetailRow(Icons.title, 'عنوان الطلب', applicant['applicationTitle']),
                    _buildDetailRow(Icons.monetization_on, 'الراتب المتوقع', '${applicant['expectedSalary']} '),
                    _buildDetailRow(Icons.work, 'الخبرة', applicant['experience']),
                  ]),
                  
                  const SizedBox(height: 20),
                  
                  _buildDetailSection('وصف الطلب', [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        applicant['applicationDescription'],
                        style: TextStyle(
                          color: textColor,
                          fontSize: 14,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ]),
                  
                  const SizedBox(height: 20),
                  
                  _buildDetailSection('المهارات', [
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: (applicant['skills'] as List).map((skill) {
                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            skill,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ]),
                  
                  const SizedBox(height: 20),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          icon: const Icon(Icons.picture_as_pdf),
                          label: const Text('عرض السيرة الذاتية'),
                          onPressed: () => _launchCV(applicant['cvUrl']),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          icon: const Icon(Icons.link),
                          label: const Text('المعرض'),
                          onPressed: () => _launchPortfolio(applicant['portfolioUrl']),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value, {VoidCallback? onTap}) {
    final isClickable = onTap != null;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Row(
            children: [
              Icon(
                icon, 
                size: 16, 
                color: isClickable ? Theme.of(context).colorScheme.primary : Colors.grey[600]
              ),
              const SizedBox(width: 8),
              SizedBox(
                width: 100,
                child: Text(
                  '$label:',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    color: isClickable ? Theme.of(context).colorScheme.primary : null,
                    decoration: isClickable ? TextDecoration.underline : null,
                    decorationColor: isClickable ? Theme.of(context).colorScheme.primary : null,
                  ),
                ),
              ),
              if (isClickable) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.launch, 
                  size: 14, 
                  color: Theme.of(context).colorScheme.primary
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
} 