import 'package:flutter/foundation.dart';

class CvModel {
  int id;
  String name;

  String blade;

  String ltf;

  String image;

  String image_circle;

  int summary;

  int first_last_name;

  int nationality;

  int job_name;

  int face;

  int twt;

  int phone;

  int linked_in;

  int email;

  int behance;

  int exp_from;

  int exp_title;

  int exp_end;

  int exp_des;

  int exp_company_name;

  int course_from;

  int course_end;

  int course_title;

  int course_des;

  int edu_des;

  int edu_from;

  int course_place_name;

  int edu_end;

  int edu_level;

  int edu_title;

  int edu_place_name;

  int skill;

  int skill_process;

  int lang;

  int lang_level;

  int age;

  int birthday;

  int social_status;

  int graduation;

  int working_now;

  int working_now_name;

  int location;

  int used;
  CvModel({
    required int this.id,
    required String this.name,
    required String this.blade,
    required String this.ltf,
    required String this.image,
    required String this.image_circle,
    required int this.summary,
    required int this.first_last_name,
    required int this.nationality,
    required int this.job_name,
    required int this.face,
    required int this.twt,
    required int this.linked_in,
    required int this.phone,
    required int this.email,
    required int this.behance,
    required int this.exp_from,
    required int this.exp_end,
    required int this.exp_title,
    required int this.exp_des,
    required int this.exp_company_name,
    required int this.course_from,
    required int this.course_end,
    required int this.course_title,
    required int this.course_des,
    required int this.course_place_name,
    required int this.edu_from,
    required int this.edu_end,
    required int this.edu_level,
    required int this.edu_title,
    required int this.edu_des,
    required int this.edu_place_name,
    required int this.skill,
    required int this.skill_process,
    required int this.lang,
    required int this.lang_level,
    required int this.age,
    required int this.birthday,
    required int this.social_status,
    required int this.graduation,
    required int this.working_now,
    required int this.working_now_name,
    required int this.location,
    required int this.used,
  });

  factory CvModel.fromJson(Map c) {
    return CvModel(
        id: c["id"],
        name: c["name"],
        blade: c["blade"],
        ltf: c["ltf"],
        image: c["image"],
        image_circle: c["image_circle"],
        summary: c["summary"],
        first_last_name: c["first_last_name"],
        nationality: c["nationality"],
        job_name: c["job_name"],
        face: c["face"],
        twt: c["twt"],
        linked_in: c["linked_in"],
        phone: c["phone"],
        email: c["email"],
        behance: c["behance"],
        exp_from: c["exp_from"],
        exp_end: c["exp_end"],
        exp_title: c["exp_title"],
        exp_des: c["exp_des"],
        exp_company_name: c["exp_company_name"],
        course_from: c["course_from"],
        course_end: c["course_end"],
        course_title: c["course_title"],
        course_des: c["course_des"],
        course_place_name: c["course_place_name"],
        edu_from: c["edu_from"],
        edu_end: c["edu_end"],
        edu_level: c["edu_level"],
        edu_title: c["edu_title"],
        edu_des: c["edu_des"],
        edu_place_name: c["edu_place_name"],
        skill: c["skill"],
        skill_process: c["skill_process"],
        lang: c["lang"],
        lang_level: c["lang_level"],
        age: c["age"],
        birthday: c["birthday"],
        social_status: c["social_status"],
        graduation: c["graduation"],
        working_now: c["working_now"],
        working_now_name: c["working_now_name"],
        location: c["location"],
        used: c["used"]);
  }
}
