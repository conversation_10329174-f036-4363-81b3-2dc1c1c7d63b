import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:wzzff/core/theme/app_colors.dart';
import 'package:wzzff/presentation/screens/job_seeker/profile_steps/common_widgets.dart';
import 'package:wzzff/Apis/CompanyApi.dart';
import 'package:wzzff/Company/Company/register_company_page.dart';
import 'package:wzzff/Company/Company/edit_company_data_page.dart';
import 'package:wzzff/Company/Company/company_dashboard.dart';

class CompanyLoginPage extends StatefulWidget {
  @override
  _CompanyLoginPageState createState() => _CompanyLoginPageState();
}

class _CompanyLoginPageState extends State<CompanyLoginPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  
  // Animation Controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  // Form Controllers
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _animationController.forward();
  }

  Future<void> _loginCompany() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    
    try {
      final response = await CompanyApi().loginCompany(
        _emailController.text.trim(),
        _passwordController.text,
      );
      
      if (response['success'] == true) {
        _showSuccessMessage('تم تسجيل الدخول بنجاح');
        
        // إرسال Firebase token بعد نجاح تسجيل الدخول
        await _sendFirebaseToken();
        
        await Future.delayed(const Duration(seconds: 1));
        if (mounted) {
          // العودة للتاب السابق مع إشارة بنجاح تسجيل الدخول
          Navigator.pop(context, {'success': true, 'userType': 'company'});
        }
      } else {
        _showErrorMessage(response['message'] ?? 'حدث خطأ أثناء تسجيل الدخول');
      }
    } catch (e) {
      _showErrorMessage('تعذر الاتصال بالخادم، يرجى المحاولة لاحقاً');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // إرسال Firebase token للسيرفر
  Future<void> _sendFirebaseToken() async {
    try {
      // الحصول على Firebase token
      String? firebaseToken = await FirebaseMessaging.instance.getToken();
      
      if (firebaseToken != null) {
        print('🔔 Firebase Token للشركة: $firebaseToken');
        
        // إرسال Token للسيرفر
        final response = await CompanyApi().sendFirebaseToken(firebaseToken);
        
        if (response['success'] == true) {
          print('✅ تم إرسال Firebase token بنجاح للشركة');
        } else {
          print('❌ فشل في إرسال Firebase token: ${response['message']}');
        }
      } else {
        print('❌ لم يتم الحصول على Firebase token');
      }
    } catch (e) {
      print('❌ خطأ في إرسال Firebase token: $e');
      // لا نعرض رسالة خطأ للمستخدم لأن هذا ليس مهماً لنجاح تسجيل الدخول
    }
  }

  Future<void> _resetPassword() async {
    if (_emailController.text.trim().isEmpty) {
      _showErrorMessage('يرجى إدخال البريد الإلكتروني أولاً');
      return;
    }
    
    try {
      final response = await CompanyApi().resetPassword(_emailController.text.trim());
      if (response['success'] == true) {
        _showSuccessMessage(response['message']);
      } else {
        _showErrorMessage(response['message']);
      }
    } catch (e) {
      _showErrorMessage('حدث خطأ أثناء إرسال الطلب');
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.tajawal()),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.tajawal()),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    return null;
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xff2daae2),
                  const Color(0xff2daae2).withOpacity(0.8),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xff2daae2).withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: const Icon(Icons.business_center, color: Colors.white, size: 40),
          ),
          const SizedBox(height: 20),
          Text(
            'تسجيل دخول الشركات',
            style: GoogleFonts.tajawal(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppColors.getTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سجل دخولك لإدارة وظائف شركتك والبحث عن أفضل المواهب',
            style: GoogleFonts.tajawal(
              fontSize: 16,
              color: AppColors.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.getCardColor(context),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تسجيل الدخول',
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
       
            const SizedBox(height: 20),
            
            buildInputField(
              controller: _emailController,
              label: 'البريد الإلكتروني',
              icon: Icons.email,
              isRequired: true,
              keyboardType: TextInputType.emailAddress,
              validator: _validateEmail,
            ),
            
            const SizedBox(height: 16),
            
            buildInputField(
              controller: _passwordController,
              label: 'كلمة المرور',
              icon: Icons.lock,
              isRequired: true,
              isPassword: !_isPasswordVisible,
              validator: _validatePassword,
            ),
            
            const SizedBox(height: 12),
            
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton(
                onPressed: _resetPassword,
                child: Text(
                  'نسيت كلمة المرور؟',
                  style: GoogleFonts.tajawal(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Checkbox(
                  value: _isPasswordVisible,
                  onChanged: (value) => setState(() => _isPasswordVisible = value ?? false),
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
                Text(
                  'إظهار كلمة المرور',
                  style: GoogleFonts.tajawal(
                    color: AppColors.getSecondaryTextColor(context),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _loginCompany,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 8,
                shadowColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Text(
                      'تسجيل الدخول',
                      style: GoogleFonts.tajawal(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => RegisterCompanyPage()),
                );
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
                side: BorderSide(color: Theme.of(context).colorScheme.primary),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Text(
                'تسجيل شركة جديدة',
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: isDarkMode 
            ? Theme.of(context).scaffoldBackgroundColor
            : const Color(0xFFF8FAFC),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: Icon(
                Icons.arrow_forward,
                color: AppColors.getTextColor(context),
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildLoginForm(),
                  const SizedBox(height: 32),
                  _buildActionButtons(),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 