import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';
import 'job_form_data.dart';

class StepPreview extends StatelessWidget {
  final JobFormData formData;

  const StepPreview({
    Key? key,
    required this.formData,
  }) : super(key: key);

  // Theme helpers
  bool _isDarkMode(BuildContext context) => Theme.of(context).brightness == Brightness.dark;
  Color _primaryColor(BuildContext context) => Theme.of(context).colorScheme.primary;
  Color _cardColor(BuildContext context) => _isDarkMode(context) ? Colors.grey[850]! : Colors.white;
  Color _surfaceColor(BuildContext context) => _isDarkMode(context) ? Colors.grey[800]! : Colors.grey[50]!;
  Color _textColor(BuildContext context) => _isDarkMode(context) ? Colors.white : Colors.black87;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _cardColor(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _isDarkMode(context) ? Colors.black.withOpacity(0.3) : Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.preview, color: _primaryColor(context), size: 18),
              const SizedBox(width: 8),
              Text(
                'معاينة الوظيفة',
                style: GoogleFonts.tajawal(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: _primaryColor(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildJobSummary(context),
          const SizedBox(height: 16),
          Center(
            child: ElevatedButton.icon(
              onPressed: () => _previewJob(context),
              icon: const Icon(Icons.preview, size: 16),
              label: Text('معاينة الوظيفة', style: GoogleFonts.tajawal(fontSize: 14)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.secondary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _previewJob(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DetailJobScreen(
          title: formData.titleController.text,
          des: formData.descController.text,
          code_address: 'wzzff-preview-${DateTime.now().millisecondsSinceEpoch}',
          email: formData.companyEmailController.text.isNotEmpty ? formData.companyEmailController.text : null,
          number: formData.companyPhoneController.text.isNotEmpty ? formData.companyPhoneController.text : null,
          salary_currency: formData.selectedCurrency ?? 'ريال سعودي',
          salary: formData.salaryController.text.isNotEmpty ? formData.salaryController.text : null,
          cat: formData.selectedCategory ?? '',
          gender: formData.selectedGender ?? '',
          state_name: formData.selectedCountry?.name ?? '',
          country_name: formData.selectedCountry?.name ?? '',
          job_type_name: formData.selectedJobType ?? '',
          city_name: formData.selectedCity?.name ?? '',
          company_name: 'شركتي',
          edu: formData.selectedEducation ?? '',
          exp: formData.selectedExperience ?? '',
          end_at: formData.endDate != null ? '${formData.endDate!.day}/${formData.endDate!.month}/${formData.endDate!.year}' : '',
          slug: 'preview-job',
          created_at_date: 'اليوم',
          time: '${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
        ),
      ),
    );
  }

  Widget _buildJobSummary(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('ملخص الوظيفة:', style: GoogleFonts.tajawal(
          fontWeight: FontWeight.bold, 
          fontSize: 14,
          color: _textColor(context),
        )),
        const SizedBox(height: 12),
        _buildSummaryRow(context, 'عنوان الوظيفة', formData.titleController.text),
        _buildSummaryRow(context, 'نوع الوظيفة', formData.selectedJobType ?? ''),
        _buildSummaryRow(context, 'التصنيف', formData.selectedCategory ?? ''),
        _buildSummaryRow(context, 'الموقع', '${formData.selectedCity?.name ?? ''}, ${formData.selectedCountry?.name ?? ''}'),
        _buildSummaryRow(context, 'المستوى التعليمي', formData.selectedEducation ?? ''),
        _buildSummaryRow(context, 'مستوى الخبرة', formData.selectedExperience ?? ''),
        _buildSummaryRow(context, 'الجنس المطلوب', formData.selectedGender ?? ''),
        _buildSummaryRow(context, 'الراتب', formData.salaryController.text.isNotEmpty ? '${formData.salaryController.text} ${formData.selectedCurrency ?? ''}' : ''),
        _buildSummaryRow(context, 'البريد الإلكتروني', formData.companyEmailController.text),
        _buildSummaryRow(context, 'رقم الهاتف', formData.companyPhoneController.text),
        _buildSummaryRow(context, 'تاريخ الانتهاء', formData.endDate != null ? '${formData.endDate!.day}/${formData.endDate!.month}/${formData.endDate!.year}' : ''),
      ],
    );
  }

  Widget _buildSummaryRow(BuildContext context, String label, String value) {
    if (value.isEmpty) return const SizedBox();
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 3),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _surfaceColor(context),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: _isDarkMode(context) ? Colors.grey[600]! : Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: GoogleFonts.tajawal(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: _primaryColor(context),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.tajawal(
                fontSize: 12,
                color: _textColor(context),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 