import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wzzff/core/theme/app_theme.dart';
import 'dart:io';
import 'package:provider/provider.dart';

import '../Api/ApiCvs.dart';
import '../Models/CvModelSimple.dart';
import '../Widgets/CvCardWidget.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';

class ArabicScreen extends StatefulWidget {
  const ArabicScreen({super.key});

  @override
  State<ArabicScreen> createState() => _ArabicScreenState();
}

class _ArabicScreenState extends State<ArabicScreen> {
  Future<File> _getImageFile(String imageUrl) async {
    final file = await DefaultCacheManager().getSingleFile(imageUrl);
    return file;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    const primaryColor = AppTheme.primaryColor;

    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: FutureBuilder<List<CvModelSimple>>(
        future: ApiCvs().allCvs(ltf: 0),
        builder: (context, AsyncSnapshot<List<CvModelSimple>> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: CircularProgressIndicator(
                color: primaryColor,
              ),
            );
          } else if (snapshot.hasError) {
            Provider.of<AppStateProvider>(context, listen: false).setServerError(
              'حدث خطأ أثناء تحميل القوالب العربية. سيتم إعادة المحاولة تلقائيًا.',
              () => setState(() {}),
            );
            return const SizedBox.shrink();
          } else if (snapshot.hasData && snapshot.data!.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 60,
                    color: isDarkMode ? Colors.white54 : Colors.black38,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد قوالب عربية متاحة',
                    style: GoogleFonts.tajawal(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                  ),
                ],
              ),
            );
          } else if (snapshot.hasData) {
            return GridView.builder(
              padding: const EdgeInsets.all(8),
              physics: const BouncingScrollPhysics(),
              shrinkWrap: true,
              itemCount: snapshot.data!.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                childAspectRatio: 2 / 2.8,
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemBuilder: (context, index) {
                final imageUrl = "https://wassta.net/${Uri.encodeFull(snapshot.data![index].image)}";

                return CvCardWidget(
                  imageUrl: imageUrl,
                  cvId: snapshot.data![index].id,
                  getImageFile: _getImageFile,
                );
              },
            );
          } else {
            return Center(
              child: CircularProgressIndicator(
                color: primaryColor,
              ),
            );
          }
        },
      ),
    );
  }
}
