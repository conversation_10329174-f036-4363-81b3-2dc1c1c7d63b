import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart' as image_picker;
import 'package:wzzff/presentation/screens/create_cv/Components/FieldTxt.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';
import 'package:wzzff/services/google_ad_service.dart';

class BasicInfoStep extends StatefulWidget {
  final CvModel cvData;
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic> currentData;
  final image_picker.XFile? currentImage;
  final Function(image_picker.XFile?) onImageChanged;

  const BasicInfoStep({
    Key? key,
    required this.cvData,
    required this.onDataChanged,
    required this.currentData,
    required this.currentImage,
    required this.onImageChanged,
  }) : super(key: key);

  @override
  State<BasicInfoStep> createState() => _BasicInfoStepState();
}

class _BasicInfoStepState extends State<BasicInfoStep> 
    with TickerProviderStateMixin {
  final image_picker.ImagePicker _picker = image_picker.ImagePicker();
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _locationController;
  late TextEditingController _jobNameController;
  late TextEditingController _nationalityController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;

  // Animation Controllers
  late AnimationController _fadeAnimationController;
  late AnimationController _scaleAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeControllers();
    _startAnimations();
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _scaleAnimationController.forward();
      }
    });
  }

  void _initializeControllers() {
    _firstNameController = TextEditingController(text: widget.currentData['first_name']);
    _lastNameController = TextEditingController(text: widget.currentData['last_name']);
    _locationController = TextEditingController(text: widget.currentData['location']);
    _jobNameController = TextEditingController(text: widget.currentData['job_name']);
    _nationalityController = TextEditingController(text: widget.currentData['nationality']);
    _phoneController = TextEditingController(text: widget.currentData['phone']);
    _emailController = TextEditingController(text: widget.currentData['email']);

    // إضافة المستمعين للتحديث التلقائي
    _firstNameController.addListener(_updateData);
    _lastNameController.addListener(_updateData);
    _locationController.addListener(_updateData);
    _jobNameController.addListener(_updateData);
    _nationalityController.addListener(_updateData);
    _phoneController.addListener(_updateData);
    _emailController.addListener(_updateData);
  }

  void _updateData() {
    widget.onDataChanged({
      'first_name': _firstNameController.text,
      'last_name': _lastNameController.text,
      'location': _locationController.text,
      'job_name': _jobNameController.text,
      'nationality': _nationalityController.text,
      'phone': _phoneController.text,
      'email': _emailController.text,
    });
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _scaleAnimationController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _locationController.dispose();
    _jobNameController.dispose();
    _nationalityController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final image_picker.XFile? image = await _picker.pickImage(
      source: image_picker.ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 800,
      imageQuality: 85,
    );
    if (image != null) {
      widget.onImageChanged(image);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Directionality(
      textDirection: widget.cvData.ltf == "0" ? TextDirection.rtl : TextDirection.ltr,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(12),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم الصورة الشخصية
                _buildProfileImageSection(isDarkMode),
                
                const SizedBox(height: 16),
                
                // قسم المعلومات الأساسية
                _buildBasicInfoSection(isDarkMode),
                
                const SizedBox(height: 12),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImageSection(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            primaryColor.withOpacity(0.1),
            primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            widget.cvData.ltf == "0" ? 'الصورة الشخصية' : 'Profile Picture',
            style: GoogleFonts.tajawal(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: primaryColor,
            ),
          ),
          const SizedBox(height: 10),
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: 85,
              height: 85,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: widget.currentImage == null
                    ? LinearGradient(
                        colors: [
                          primaryColor.withOpacity(0.2),
                          primaryColor.withOpacity(0.1),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
                border: Border.all(
                  color: primaryColor,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: widget.currentImage != null
                  ? ClipOval(
                      child: Image.file(
                        File(widget.currentImage!.path),
                        fit: BoxFit.cover,
                        width: 85,
                        height: 85,
                      ),
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.camera_alt,
                          size: 26,
                          color: primaryColor,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.cvData.ltf == "0" ? 'اضغط لإضافة' : 'Tap to add',
                          style: GoogleFonts.tajawal(
                            fontSize: 9,
                            fontWeight: FontWeight.w600,
                            color: primaryColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.cvData.ltf == "0" 
                ? 'الصورة الشخصية مطلوبة لإكمال السيرة الذاتية'
                : 'Profile picture is required to complete CV',
            style: GoogleFonts.tajawal(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.person,
                  color: primaryColor,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                widget.cvData.ltf == "0" ? 'المعلومات الأساسية' : 'Basic Information',
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 14),
          
          // حقول الإدخال
          if (widget.cvData.first_last_name == 1) ...[
            Row(
              children: [
                Expanded(
                  child: _buildModernTextField(
                    controller: _firstNameController,
                    label: Lang().getWord("first_name", widget.cvData.ltf),
                    hint: Lang().getWord("first_name_hint", widget.cvData.ltf),
                    icon: Icons.person_outline,
                    isRequired: true,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: _buildModernTextField(
                    controller: _lastNameController,
                    label: Lang().getWord("last_name", widget.cvData.ltf),
                    hint: Lang().getWord("last_name_hint", widget.cvData.ltf),
                    icon: Icons.person_outline,
                    isRequired: true,
                  ),
                ),
              ],
            ),
          ] else if (widget.cvData.first_last_name == 0) ...[
            _buildModernTextField(
              controller: _firstNameController,
              label: Lang().getWord("full_name", widget.cvData.ltf),
              hint: Lang().getWord("full_name_hint", widget.cvData.ltf),
              icon: Icons.person,
              isRequired: true,
            ),
          ],
          
          const SizedBox(height: 14),
          
          if (widget.cvData.location == 1)
            _buildModernTextField(
              controller: _locationController,
              label: Lang().getWord("location", widget.cvData.ltf),
              hint: Lang().getWord("location_hint", widget.cvData.ltf),
              icon: Icons.location_on,
              isRequired: true,
            ),
          
          if (widget.cvData.location == 1) const SizedBox(height: 14),
          
          if (widget.cvData.job_name == 1)
            _buildModernTextField(
              controller: _jobNameController,
              label: Lang().getWord("job_name", widget.cvData.ltf),
              hint: Lang().getWord("job_name_hint", widget.cvData.ltf),
              icon: Icons.work,
              isRequired: true,
            ),
          
          if (widget.cvData.job_name == 1) const SizedBox(height: 14),
          
          if (widget.cvData.nationality == 1)
            _buildModernTextField(
              controller: _nationalityController,
              label: Lang().getWord("nationality", widget.cvData.ltf),
              hint: Lang().getWord("nationality_hint", widget.cvData.ltf),
              icon: Icons.flag,
              isRequired: true,
            ),
          
          if (widget.cvData.nationality == 1) const SizedBox(height: 14),
          
          if (widget.cvData.phone == 1)
            _buildModernTextField(
              controller: _phoneController,
              label: Lang().getWord("phone", widget.cvData.ltf),
              hint: Lang().getWord("phone_hint", widget.cvData.ltf),
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
              isRequired: true,
            ),
          
          if (widget.cvData.phone == 1) const SizedBox(height: 14),
          
          if (widget.cvData.email == 1)
            _buildModernTextField(
              controller: _emailController,
              label: Lang().getWord("email", widget.cvData.ltf),
              hint: Lang().getWord("email_hint", widget.cvData.ltf),
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
              isRequired: true,
            ),
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool isRequired = false,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 14,
              color: primaryColor,
            ),
            const SizedBox(width: 5),
            Text(
              label + (isRequired ? ' *' : ''),
              style: GoogleFonts.tajawal(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.grey[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 5),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.06),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            style: GoogleFonts.tajawal(
              fontSize: 12,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: GoogleFonts.tajawal(
                fontSize: 12,
                color: Colors.grey[500],
              ),
              filled: true,
              fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[50],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: primaryColor,
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey.withOpacity(0.3),
                  width: 1,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 10,
                vertical: 8,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
