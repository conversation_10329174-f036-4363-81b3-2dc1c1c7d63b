﻿import 'package:flutter/material.dart';
import 'package:list_load_more/list_load_more/list_load_more.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:convert';
import 'dart:io';

import 'package:wzzff/Apis/JobsApi.dart';
import 'package:wzzff/presentation/components/ListShimmer.dart';
import 'package:wzzff/presentation/widgets/job_card.dart';
import 'package:wzzff/presentation/widgets/ad_job_card.dart';
import 'package:wzzff/models/JobModel.dart';
import 'package:wzzff/services/google_ad_service.dart';
import 'package:wzzff/services/firebase_messaging_service.dart';
import 'package:wzzff/services/daily_notification_service.dart';
import 'package:wzzff/services/scheduled_notification_service.dart';

class LoadJobScreen extends StatefulWidget {
  double paddingTop;
  String where;
  Map? extraData;

  bool showCity;

  LoadJobScreen(
      {super.key,
      required this.where,
      required this.paddingTop,
      required this.showCity,
      this.extraData});
  @override
  State<LoadJobScreen> createState() => _LoadJobScreenState();
}

class _LoadJobScreenState extends State<LoadJobScreen> {
  final GoogleAdService _adService = GoogleAdService();
  final DailyNotificationService _dailyNotificationService = DailyNotificationService();
  final ScheduledNotificationService _scheduledNotificationService = ScheduledNotificationService();
  
  int _jobLoadCount = 0; 

  List<JobModel> jobsList = [];
  bool _isCacheLoaded = false;

  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    if (widget.where != 'search') {
      _loadJobsFromCache();
    }
    _loadData(offset: 0);
    _adService.loadInterstitialAd();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  ListLoadMoreStatus _status = ListLoadMoreStatus.none;

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 && !_isLoadingMore && _hasMore) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    _isLoadingMore = true;
    final prevCount = jobsList.length;
    await _loadData(offset: jobsList.length);
    if (jobsList.length == prevCount) {
      _hasMore = false;
    }
    _isLoadingMore = false;
  }

  Future<void> _loadJobsFromCache() async {
    if (widget.where == 'search') return;
    final prefs = await SharedPreferences.getInstance();
    final cacheKey = _getCacheKey();
    final cached = prefs.getString(cacheKey);
    if (cached != null) {
      final List<dynamic> decoded = json.decode(cached);
      if (mounted) {
        setState(() {
          jobsList = decoded.map((e) => JobModel.fromJson(e ?? {})).toList();
          _isCacheLoaded = true;
        });
      }
    }
  }

  Future<void> _cacheJobs(List<JobModel> jobs) async {
    if (widget.where == 'search') return;
    final prefs = await SharedPreferences.getInstance();
    final cacheKey = _getCacheKey();
    final encoded = json.encode(jobs.map((e) => e.toJson()).toList());
    await prefs.setString(cacheKey, encoded);
  }

  String _getCacheKey() {
    String key = 'jobs_cache_${widget.where}';
    if (widget.extraData != null && widget.extraData!["country"] != null) {
      key += '_${widget.extraData!["country"]}';
    }
    return key;
  }

  Future _loadData({required int offset}) async {
    if (offset == 0) {
      if (mounted) {
        setState(() {
          _status = ListLoadMoreStatus.loadRefresh;
          _hasMore = true;
        });
      }
    }
    try {
      List<JobModel> jobs = [];
      switch (widget.where) {
        case "home":
          jobs = await JobsApi().getJobsMainIndex(offset: offset);
          break;
        case "search":
          jobs = await JobsApi().searchApi(
              offset: offset, data: widget.extraData as Map<dynamic, dynamic>);
          
          // طھطھط¨ط¹ ط§ظ„ط¨ط­ط« ط§ظ„ط­ظ‚ظٹظ‚ظٹ ظپظ‚ط· ط¹ظ†ط¯ offset = 0 (ط¨ط­ط« ط¬ط¯ظٹط¯)
          if (offset == 0) {
            _checkAndSubscribeToCountryTopic(widget.extraData?["country"] as String?);
            
           
            
          }
          break;
        case "countryByName":
          jobs = await JobsApi().jobsByCountryName(
              offset: offset, data: widget.extraData as Map<dynamic, dynamic>);
          if (offset == 0) {
            _checkAndSubscribeToCountryTopic(widget.extraData?["country"] as String?);
          }
          break;
        case "getDataFromStorage":
          jobs = await JobsApi().jobsByCountryName(
              offset: offset, data: widget.extraData as Map<dynamic, dynamic>);
          if (offset == 0) {
            _checkAndSubscribeToCountryTopic(widget.extraData?["country"] as String?);
          }
          break;
        case "getmyfavjobs":
          jobs = await JobsApi().getmyfavjobs(
              offset: offset, data: widget.extraData as Map<dynamic, dynamic>);
          break;
      }
      if (mounted) {
        setState(() {
          if (offset == 0) {
            jobsList = jobs;
          } else {
            jobsList.addAll(jobs);
          }
          _status = ListLoadMoreStatus.none;
          _jobLoadCount++;
        });
      }
      if (offset == 0) {
        await _cacheJobs(jobsList);
      }
      if (_jobLoadCount % 3 == 0 && _jobLoadCount > 0) {
        await _adService.showInterstitialAd();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _status = ListLoadMoreStatus.error;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      color: isDarkMode
          ? Theme.of(context).scaffoldBackgroundColor
          : const Color(0xFFF5F5F5),
      child: Padding(
        padding: EdgeInsets.only(top: widget.paddingTop),
        child: RefreshIndicator(
          onRefresh: () async {
            await _loadData(offset: 0);
          },
          color: primaryColor,
          child: jobsList.isEmpty && _status == ListLoadMoreStatus.loadRefresh
              ? ListShimmer(itemCount: 30)
              : ListView.builder(
                  controller: _scrollController,
                  itemCount: jobsList.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == jobsList.length) {
                      // ظ…ط¤ط´ط± طھط­ظ…ظٹظ„ ط§ظ„ظ…ط²ظٹط¯
                      return Container(
                        alignment: Alignment.center,
                        height: MediaQuery.of(context).size.height * 0.15,
                        child: const CircularProgressIndicator(),
                      );
                    }
                    final item = jobsList[index];
                    if (index > 0 && index % 10 == 0) {
                      return Column(
                        children: [
                          const AdJobCard(),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 2),
                            child: JobCard(
                              title: item.title,
                              code_address: item.code_address,
                              des: item.description,
                              cat: item.cat,
                              city_name: item.city_name,
                              company_name: item.company_name,
                              country_name: item.country_name,
                              created_at_date: item.created_at_date,
                              edu: item.edu,
                              email: item.email,
                              end_at: item.end_at,
                              exp: item.exp,
                              gender: item.gender,
                              job_type_name: item.job_type_name,
                              number: item.number,
                              salary: item.salary,
                              salary_currency: item.salary_currency,
                              slug: item.slug,
                              state_name: item.state_name,
                              time: item.time,
                              showCity: widget.showCity,
                            ),
                          ),
                        ],
                      );
                    }
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 2),
                      child: JobCard(
                        title: item.title,
                        code_address: item.code_address,
                        des: item.description,
                        cat: item.cat,
                        city_name: item.city_name,
                        company_name: item.company_name,
                        country_name: item.country_name,
                        created_at_date: item.created_at_date,
                        edu: item.edu,
                        email: item.email,
                        end_at: item.end_at,
                        exp: item.exp,
                        gender: item.gender,
                        job_type_name: item.job_type_name,
                        number: item.number,
                        salary: item.salary,
                        salary_currency: item.salary_currency,
                        slug: item.slug,
                        state_name: item.state_name,
                        time: item.time,
                        showCity: widget.showCity,
                      ),
                    );
                  },
                ),
        ),
      ),
    );
  }

  // ط¯ط§ظ„ط© ظ„ظ„طھط­ظ‚ظ‚ ظ…ظ† ط§ط´طھط±ط§ظƒ ط§ظ„ظ…ط³طھط®ط¯ظ… ظپظٹ ظ…ظˆط¶ظˆط¹ ط§ظ„ط¨ظ„ط¯ ظˆط¥ط¶ط§ظپطھظ‡ ط¥ط°ط§ ظ„ظ… ظٹظƒظ† ظ…ط´طھط±ظƒظ‹ط§
  Future<void> _checkAndSubscribeToCountryTopic(String? countryName) async {

    if (countryName == null || countryName.isEmpty) {
      return;
    }

    try {
      // طھط­ظˆظٹظ„ ط§ط³ظ… ط§ظ„ط¨ظ„ط¯ ط¥ظ„ظ‰ ط±ظ…ط² ط§ظ„ط¨ظ„ط¯
      final countryCode = _getCountryCode(countryName);

      if (countryCode.isEmpty) {
        return;
      }

      // ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط­ط§ظ„ط© ط§ظ„ط§ط´طھط±ط§ظƒ ظپظٹ ظ…ظˆط¶ظˆط¹ ط§ظ„ط¨ظ„ط¯
      final prefs = await SharedPreferences.getInstance();
      final topicKey = 'subscribed_to_country_$countryCode';
      final isSubscribed = prefs.getBool(topicKey) ?? false;

      // ط¥ط°ط§ ظ„ظ… ظٹظƒظ† ط§ظ„ظ…ط³طھط®ط¯ظ… ظ…ط´طھط±ظƒظ‹ط§ ظپظٹ ظ…ظˆط¶ظˆط¹ ط§ظ„ط¨ظ„ط¯طŒ ظ†ظ‚ظˆظ… ط¨ط¥ط¶ط§ظپطھظ‡
      if (!isSubscribed) {

        // ط¥ظ†ط´ط§ط، ط®ط¯ظ…ط© Firebase Messaging
        final messagingService = FirebaseMessagingService();

        // ط§ظ„ط§ط´طھط±ط§ظƒ ظپظٹ ظ…ظˆط¶ظˆط¹ ط§ظ„ط¨ظ„ط¯
        await messagingService.subscribeToTopic(countryCode);

        // طھط®ط²ظٹظ† ط­ط§ظ„ط© ط§ظ„ط§ط´طھط±ط§ظƒ
        await prefs.setBool(topicKey, true);

        // ط¹ط±ط¶ ط±ط³ط§ظ„ط© ظ„ظ„ظ…ط³طھط®ط¯ظ… (ط§ط®طھظٹط§ط±ظٹ) ظ…ط¹ ط§ظ„طھط­ظ‚ظ‚ ظ…ظ† ط£ظ† ط§ظ„ظ€ widget ظ„ط§ ظٹط²ط§ظ„ ظ…ط«ط¨طھظ‹ط§
        if (mounted) {
          /*
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('طھظ… ط§ظ„ط§ط´طھط±ط§ظƒ ظپظٹ ط¥ط´ط¹ط§ط±ط§طھ ظˆط¸ط§ط¦ظپ $countryName'),
              duration: const Duration(seconds: 2),
            ),
          );
          */
        }

      } else {
      }
    } catch (e) {
    }
  }

  // ط¯ط§ظ„ط© ظ…ط³ط§ط¹ط¯ط© ظ„ظ„ط­طµظˆظ„ ط¹ظ„ظ‰ ط±ظ…ط² ط§ظ„ط¨ظ„ط¯
  String _getCountryCode(String country) {
    switch (country) {
      case "ط§ظ„ط³ط¹ظˆط¯ظٹط©": return "sa";
      case "ظ…طµط±": return "eg";
      case "ط§ظ„ط§ظ…ط§ط±ط§طھ": return "ae";
      case "ط§ظ„ط£ط±ط¯ظ†": return "jo";
      case "ط§ظ„ط¨ط­ط±ظٹظ†": return "bh";
      case "ط§ظ„ظƒظˆظٹطھ": return "kw";
      case "ظ‚ط·ط±": return "qa";
      case "ط¹ظ…ط§ظ†": return "om";
      case "ط§ظ„ط¹ط±ط§ظ‚": return "iq";
      case "ط§ظ„ط¬ط²ط§ط¦ط±": return "dz";
      case "ط§ظ„ظ…ط؛ط±ط¨": return "ma";
      case "طھظˆظ†ط³": return "tn";
      case "ظ„ط¨ظ†ط§ظ†": return "lb";
      case "ط³ظˆط±ظٹط§": return "sy";
      case "ط§ظ„ط³ظˆط¯ط§ظ†": return "sd";
      case "ظ„ظٹط¨ظٹط§": return "ly";
      case "ظپظ„ط³ط·ظٹظ†": return "ps";
      case "ط§ظ„ظٹظ…ظ†": return "ye";
      default: return country.toLowerCase();
    }
  }
}
