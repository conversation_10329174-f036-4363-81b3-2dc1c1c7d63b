﻿import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import 'package:wzzff/presentation/utils/Constants.dart' as Constants;
import 'package:wzzff/presentation/screens/jobs/detail_job_screen.dart';
import 'package:wzzff/presentation/components/TransitionPage.dart';
import 'package:wzzff/models/JobModel.dart';
import 'custom_theme.dart';

class JobCard extends StatefulWidget {
  String title;
  String? email;
  String? number;
  String salary_currency;
  String code_address;
  String des;
  String? salary;
  String gender;
  String cat;
  String state_name;
  String country_name;
  String city_name;
  String job_type_name;
  String company_name;
  String edu;
  String end_at;
  String exp;
  String created_at_date;
  String slug;
  String time;
  bool showCity;

  JobCard({
    super.key,
    required this.title,
    required this.des,
    required this.time,
    required this.code_address,
    required this.email,
    required this.number,
    required this.salary_currency,
    required this.salary,
    required this.cat,
    required this.gender,
    required this.state_name,
    required this.country_name,
    required this.job_type_name,
    required this.city_name,
    required this.company_name,
    required this.edu,
    required this.exp,
    required this.end_at,
    required this.created_at_date,
    required this.slug,
    required this.showCity,
  });

  @override
  State<JobCard> createState() => _JobCardState();
}

class _JobCardState extends State<JobCard> {
  bool isFavorite = false;

  @override
  void initState() {
    super.initState();
    _checkIfFavorite();
  }

  // التحقق مما إذا كانت الوظيفة مفضلة
  Future<void> _checkIfFavorite() async {
    final prefs = await SharedPreferences.getInstance();
    final savedJobs = prefs.getStringList('savedJobs') ?? [];

    setState(() {
      isFavorite = savedJobs.contains(widget.slug);
    });
  }

  // حفظ أو إزالة الوظيفة من المفضلة
  Future<void> _toggleFavorite() async {
    // حفظ حالة المفضلة قبل التغيير
    final newFavoriteState = !isFavorite;

    // حفظ سياق البناء وألوان السمة قبل العمليات غير المتزامنة
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final greyColor = isDarkMode ? Colors.grey[700] : Colors.grey;

    setState(() {
      isFavorite = newFavoriteState;
    });

    final prefs = await SharedPreferences.getInstance();
    final savedJobs = prefs.getStringList('savedJobs') ?? [];
    final savedJobsData = prefs.getString('savedJobsData') ?? '{}';
    Map<String, dynamic> jobsData = json.decode(savedJobsData);

    if (newFavoriteState) {
      // إضافة الوظيفة إلى المفضلة
      if (!savedJobs.contains(widget.slug)) {
        savedJobs.add(widget.slug);
        await prefs.setStringList('savedJobs', savedJobs);

        // حفظ بيانات الوظيفة
        jobsData[widget.slug] = {
          'title': widget.title,
          'company_name': widget.company_name,
          'city_name': widget.city_name,
          'country_name': widget.country_name,
          'job_type_name': widget.job_type_name,
          'time': widget.time,
          'salary': widget.salary,
          'salary_currency': widget.salary_currency,
          'des': widget.des,
          'email': widget.email,
          'number': widget.number,
          'code_address': widget.code_address,
          'gender': widget.gender,
          'cat': widget.cat,
          'state_name': widget.state_name,
          'edu': widget.edu,
          'end_at': widget.end_at,
          'exp': widget.exp,
          'created_at_date': widget.created_at_date,
          'slug': widget.slug,
          'added_to_favorites_at': DateTime.now().toIso8601String(),
        };
        await prefs.setString('savedJobsData', json.encode(jobsData));

        // تتبع إضافة الوظيفة للمفضلة في النظام الذكي
        await _trackFavoriteAction(true);

        // التحقق من أن الـ Widget لا يزال مثبتاً قبل استخدام السياق
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Text('تمت إضافة الوظيفة إلى المفضلة'),
              backgroundColor: primaryColor,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } else {
      // إزالة الوظيفة من المفضلة
      savedJobs.remove(widget.slug);
      await prefs.setStringList('savedJobs', savedJobs);

      // إزالة بيانات الوظيفة
      jobsData.remove(widget.slug);
      await prefs.setString('savedJobsData', json.encode(jobsData));

      // تتبع إزالة الوظيفة من المفضلة في النظام الذكي
      await _trackFavoriteAction(false);

      // التحقق من أن الـ Widget لا يزال مثبتاً قبل استخدام السياق
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: const Text('تمت إزالة الوظيفة من المفضلة'),
            backgroundColor: greyColor,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// تتبع إجراءات المفضلة في النظام الذكي
  Future<void> _trackFavoriteAction(bool isAdded) async {
    try {
      // إنشاء JobModel من بيانات الـ widget
      final jobModel = JobModel(
        title: widget.title,
        description: widget.des,
        email: widget.email,
        number: widget.number,
        salary_currency: widget.salary_currency,
        code_address: widget.code_address,
        salary: widget.salary,
        gender: widget.gender,
        cat: widget.cat,
        state_name: widget.state_name,
        country_name: widget.country_name,
        city_name: widget.city_name,
        job_type_name: widget.job_type_name,
        company_name: widget.company_name,
        edu: widget.edu,
        end_at: widget.end_at,
        exp: widget.exp,
        created_at_date: widget.created_at_date,
        slug: widget.slug,
        time: widget.time,
      );

   
      
    } catch (e) {
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
            context,
            TransitionPage(DetailJobScreen(
              title: widget.title,
              code_address: widget.code_address,
              des: widget.des,
              cat: widget.cat,
              city_name: widget.city_name,
              company_name: widget.company_name,
              country_name: widget.country_name,
              created_at_date: widget.created_at_date,
              edu: widget.edu,
              email: widget.email,
              end_at: widget.end_at,
              exp: widget.exp,
              gender: widget.gender,
              job_type_name: widget.job_type_name,
              number: widget.number,
              salary: widget.salary,
              salary_currency: widget.salary_currency,
              slug: widget.slug,
              state_name: widget.state_name,
              time: widget.time,
            )));
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 2),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).cardTheme.color
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.black.withOpacity(0.2)
                  : Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 100,
                        child: Text(
                          widget.title,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.tajawal(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      SizedBox(
                        width: MediaQuery.of(context).size.width - 100,
                        child: Text(
                          widget.company_name,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.tajawal(
                            fontSize: 11,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Theme.of(context).textTheme.bodySmall?.color
                                : Colors.grey[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Material(
                    color: Colors.transparent,
                    shape: const CircleBorder(),
                    clipBehavior: Clip.hardEdge,
                    child: IconButton(
                      icon: Icon(
                        isFavorite ? Icons.bookmark : Icons.bookmark_border,
                        color: isFavorite
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).brightness == Brightness.dark
                                ? Colors.grey[400]
                                : grey,
                        size: 20,
                      ),
                      onPressed: () {
                        _toggleFavorite();
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Divider(
                height: 1,
                thickness: 0.5,
                color: Theme.of(context).dividerTheme.color,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).colorScheme.primary.withOpacity(0.7)
                            : grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.showCity == true
                            ? widget.city_name
                            : widget.country_name,
                        style: GoogleFonts.tajawal(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).textTheme.bodyMedium?.color
                              : const Color.fromARGB(255, 49, 1, 99),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_month,
                        size: 16,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).colorScheme.secondary
                            : const Color.fromARGB(255, 230, 142, 11),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.time.toString(),
                        style: GoogleFonts.tajawal(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).textTheme.bodyMedium?.color
                              : black,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(
                          Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.job_type_name,
                      style: GoogleFonts.tajawal(
                        fontSize: 10,
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}



