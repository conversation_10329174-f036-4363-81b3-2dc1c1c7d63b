package com.wzzff.app

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity: FlutterActivity() {
    private lateinit var notificationService: NotificationService

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // تهيئة خدمة الإشعارات
        notificationService = NotificationService(applicationContext, flutterEngine)
    }
}
