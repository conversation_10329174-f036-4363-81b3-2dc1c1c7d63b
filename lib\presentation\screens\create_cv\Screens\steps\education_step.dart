import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:wzzff/presentation/screens/create_cv/Components/FieldTxt.dart';
import 'package:wzzff/presentation/screens/create_cv/Lang.dart';
import 'package:wzzff/presentation/screens/create_cv/Models/CvModel.dart';
import 'package:google_fonts/google_fonts.dart';

class EducationStep extends StatefulWidget {
  final CvModel cvData;
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic> currentData;
  final List<Map<String, dynamic>> educationList;
  final Function(List<Map<String, dynamic>>) onEducationListChanged;

  const EducationStep({
    Key? key,
    required this.cvData,
    required this.onDataChanged,
    required this.currentData,
    required this.educationList,
    required this.onEducationListChanged,
  }) : super(key: key);

  @override
  State<EducationStep> createState() => _EducationStepState();
}

class _EducationStepState extends State<EducationStep> 
    with TickerProviderStateMixin {
  late TextEditingController _eduTitleController;
  late TextEditingController _eduDesController;
  late TextEditingController _eduPlaceNameController;

  String? _eduFrom;
  String? _eduEnd;
  String? _eduLevel;
  int? _editingIndex;

  // Animation Controllers
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Colors
  Color get primaryColor => const Color(0xff2daae2);
  Color get accentColor => const Color(0xff1e88e5);

  final List<String> yearsOption = [for (int i = 1980; i <= 2025; i++) i.toString()];
  final List<String> eduLevelsOptions = ['امتياز', 'جيد جدا', 'جيد', 'مقبول'];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeControllers();
    _startAnimations();
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
      }
    });
  }

  void _initializeControllers() {
    _eduTitleController = TextEditingController(text: widget.currentData['edu_title']);
    _eduDesController = TextEditingController(text: widget.currentData['edu_des']);
    _eduPlaceNameController = TextEditingController(text: widget.currentData['edu_place_name']);

    _eduFrom = widget.currentData['edu_from'];
    _eduEnd = widget.currentData['edu_end'];
    _eduLevel = widget.currentData['edu_level'];
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _eduTitleController.dispose();
    _eduDesController.dispose();
    _eduPlaceNameController.dispose();
    super.dispose();
  }

  void _updateData(String field, String? value) {
    final newData = Map<String, dynamic>.from(widget.currentData);
    newData[field] = value;
    widget.onDataChanged(newData);
  }

  void _addEducation() {
    if (_eduFrom == null || _eduEnd == null ||
        _eduTitleController.text.isEmpty ||
        _eduDesController.text.isEmpty ||
        _eduPlaceNameController.text.isEmpty ||
        _eduLevel == null) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Lang().getWord("fill_all_fields", widget.cvData.ltf),
            style: GoogleFonts.tajawal(fontSize: 12, color: Colors.white),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
      return;
    }

    final education = {
      "edu_from": _eduFrom,
      "edu_end": _eduEnd,
      "edu_title": _eduTitleController.text,
      "edu_des": _eduDesController.text,
      "edu_place_name": _eduPlaceNameController.text,
      "edu_level": _eduLevel,
    };

    final newList = List<Map<String, dynamic>>.from(widget.educationList);

    if (_editingIndex != null) {
      newList[_editingIndex!] = education;
      _editingIndex = null;
    } else {
      newList.add(education);
    }

    widget.onEducationListChanged(newList);

    // إعادة تعيين الحقول
    _eduTitleController.clear();
    _eduDesController.clear();
    _eduPlaceNameController.clear();
    setState(() {
      _eduFrom = null;
      _eduEnd = null;
      _eduLevel = null;
    });
  }

  void _editEducation(int index) {
    final education = widget.educationList[index];

    setState(() {
      _eduFrom = education["edu_from"];
      _eduEnd = education["edu_end"];
      _eduTitleController.text = education["edu_title"];
      _eduDesController.text = education["edu_des"];
      _eduPlaceNameController.text = education["edu_place_name"];
      _eduLevel = education["edu_level"];
      _editingIndex = index;
    });
  }

  void _deleteEducation(int index) {
    final newList = List<Map<String, dynamic>>.from(widget.educationList);
    newList.removeAt(index);
    widget.onEducationListChanged(newList);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // إذا تم العودة من الخطوة التالية، احذف آخر تعليم من المصفوفة وأعد تعبئة الحقول
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map && args['action'] == 'back_step' && widget.educationList.isNotEmpty) {
      final lastEdu = widget.educationList.last;
      widget.onEducationListChanged(List<Map<String, dynamic>>.from(widget.educationList)..removeLast());
      setState(() {
        _eduFrom = lastEdu["edu_from"];
        _eduEnd = lastEdu["edu_end"];
        _eduTitleController.text = lastEdu["edu_title"];
        _eduDesController.text = lastEdu["edu_des"];
        _eduPlaceNameController.text = lastEdu["edu_place_name"];
        _eduLevel = lastEdu["edu_level"];
        _editingIndex = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = Theme.of(context).textTheme.bodyLarge?.color;

    return Directionality(
      textDirection: widget.cvData.ltf == "0" ? TextDirection.rtl : TextDirection.ltr,
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // هيدر القسم
                _buildSectionHeader(),
                
                const SizedBox(height: 14),

                // قائمة التعليم المضاف
                if (widget.educationList.isNotEmpty) ...[
                  _buildEducationList(textColor),
                  const Divider(height: 20),
                ],

                const SizedBox(height: 12),

                // نموذج إضافة تعليم جديد
                _buildEducationForm(isDarkMode, textColor),
                
                const SizedBox(height: 16),

                // زر الإضافة
                _buildAddButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [primaryColor, primaryColor.withOpacity(0.7)],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withOpacity(0.10),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.school_outlined, color: Colors.white, size: 20),
          const SizedBox(width: 6),
          Text(
            Lang().getWord("edu", widget.cvData.ltf),
            style: GoogleFonts.tajawal(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEducationList(Color? textColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang().getWord("added_education", widget.cvData.ltf),
          style: GoogleFonts.tajawal(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
        const SizedBox(height: 6),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.educationList.length,
          itemBuilder: (context, index) {
            final education = widget.educationList[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 6),
              elevation: 1.5,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                title: Text(
                  education["edu_title"],
                  style: GoogleFonts.tajawal(
                    fontWeight: FontWeight.bold, 
                    fontSize: 12,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      education["edu_place_name"], 
                      style: GoogleFonts.tajawal(fontSize: 10),
                    ),
                    Text(
                      "${education["edu_from"]} - ${education["edu_end"]}", 
                      style: GoogleFonts.tajawal(fontSize: 10),
                    ),
                    Text(
                      "${Lang().getWord("level", widget.cvData.ltf)}: ${education["edu_level"]}", 
                      style: GoogleFonts.tajawal(fontSize: 10),
                    ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, size: 16),
                      onPressed: () => _editEducation(index),
                      padding: const EdgeInsets.all(4),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 16),
                      onPressed: () => _deleteEducation(index),
                      padding: const EdgeInsets.all(4),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildEducationForm(bool isDarkMode, Color? textColor) {
    final cardColor = Theme.of(context).cardColor;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // سنة البداية
          Text(
            "${Lang().getWord("edu_from", widget.cvData.ltf)}*",
            style: GoogleFonts.tajawal(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 4),
          FormBuilderDropdown(
            name: 'edu_from',
            initialValue: _eduFrom,
            decoration: InputDecoration(
              filled: true,
              fillColor: cardColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              prefixIcon: const Icon(Icons.calendar_today, size: 16),
              hintText: Lang().getWord("choose", widget.cvData.ltf),
              hintStyle: GoogleFonts.tajawal(fontSize: 11),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            ),
            items: yearsOption
                .map((year) => DropdownMenuItem(
                      value: year,
                      child: Text(year, style: GoogleFonts.tajawal(fontSize: 11)),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _eduFrom = value;
              });
              _updateData('edu_from', value);
            },
          ),
          const SizedBox(height: 12),

          // سنة النهاية
          Text(
            "${Lang().getWord("edu_end", widget.cvData.ltf)}*",
            style: GoogleFonts.tajawal(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 4),
          FormBuilderDropdown(
            name: 'edu_end',
            initialValue: _eduEnd,
            decoration: InputDecoration(
              filled: true,
              fillColor: cardColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              prefixIcon: const Icon(Icons.calendar_today, size: 16),
              hintText: Lang().getWord("choose", widget.cvData.ltf),
              hintStyle: GoogleFonts.tajawal(fontSize: 11),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            ),
            items: yearsOption
                .map((year) => DropdownMenuItem(
                      value: year,
                      child: Text(year, style: GoogleFonts.tajawal(fontSize: 11)),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _eduEnd = value;
              });
              _updateData('edu_end', value);
            },
          ),
          const SizedBox(height: 12),

          // عنوان التعليم
          FieldTxt(
            fieldLabel: Lang().getWord("edu_title", widget.cvData.ltf),
            handlerInput: (value) => _updateData('edu_title', value),
            fieldname: 'edu_title',
            hint: "",
            iconField: const Icon(Icons.school_outlined, size: 16),
            inputRequired: widget.cvData.edu_title == 1,
            textEditingController: _eduTitleController,
          ),
          const SizedBox(height: 12),

          // مستوى التعليم
          Text(
            "${Lang().getWord("edu_level", widget.cvData.ltf)}*",
            style: GoogleFonts.tajawal(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 4),
          FormBuilderDropdown(
            name: 'edu_level',
            initialValue: _eduLevel,
            decoration: InputDecoration(
              filled: true,
              fillColor: cardColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              prefixIcon: const Icon(Icons.grade, size: 16),
              hintText: Lang().getWord("choose", widget.cvData.ltf),
              hintStyle: GoogleFonts.tajawal(fontSize: 11),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            ),
            items: eduLevelsOptions
                .map((level) => DropdownMenuItem(
                      value: level,
                      child: Text(level, style: GoogleFonts.tajawal(fontSize: 11)),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _eduLevel = value;
              });
              _updateData('edu_level', value);
            },
          ),
          const SizedBox(height: 12),

          // وصف التعليم
          Text(
            Lang().getWord("edu_des", widget.cvData.ltf),
            style: GoogleFonts.tajawal(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 4),
          TextFormField(
            controller: _eduDesController,
            maxLines: 2,
            style: GoogleFonts.tajawal(fontSize: 11),
            decoration: InputDecoration(
              filled: isDarkMode,
              fillColor: isDarkMode ? Theme.of(context).inputDecorationTheme.fillColor : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: primaryColor, width: 1.5),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5),
                borderSide: BorderSide(color: isDarkMode ? Colors.red[300]! : Colors.red),
              ),
              hintText: Lang().getWord(" ...... ", widget.cvData.ltf),
              hintStyle: GoogleFonts.tajawal(
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                fontSize: 11,
              ),
              contentPadding: const EdgeInsets.all(10),
            ),
            onChanged: (value) => _updateData('edu_des', value),
          ),
          const SizedBox(height: 12),

          // مكان التعليم
          FieldTxt(
            fieldLabel: Lang().getWord("edu_place_name", widget.cvData.ltf),
            handlerInput: (value) => _updateData('edu_place_name', value),
            fieldname: "edu_place_name",
            hint: Lang().getWord("edu_place_name_hint", widget.cvData.ltf),
            iconField: const Icon(Icons.location_city, size: 16),
            inputRequired: widget.cvData.edu_place_name == 1,
            textEditingController: _eduPlaceNameController,
          ),
        ],
      ),
    );
  }

  Widget _buildAddButton() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _addEducation,
            icon: Icon(_editingIndex != null ? Icons.save : Icons.add, size: 14),
            label: Text(
              _editingIndex != null
                  ? Lang().getWord("save_changes", widget.cvData.ltf)
                  : Lang().getWord("add_education", widget.cvData.ltf),
              style: GoogleFonts.tajawal(fontSize: 11),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
