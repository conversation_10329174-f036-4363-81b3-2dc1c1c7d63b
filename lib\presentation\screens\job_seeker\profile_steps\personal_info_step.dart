﻿import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wzzff/models/SeekerModel.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
// import 'package:wzzff/Apis/ProfileApi.dart';
import 'package:wzzff/core/utils/app_messages.dart';
import 'common_widgets.dart';

class PersonalInfoStep extends StatefulWidget {
  final SeekerModel user;
  final Function(Map<String, dynamic>) onDataChanged;

  const PersonalInfoStep({
    super.key,
    required this.user,
    required this.onDataChanged,
  });

  @override
  State<PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends State<PersonalInfoStep> {
  final _firstNameController = TextEditingController();
  final _middleNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  String? selectedValueGender;
  String? selectedValueCountry;

  // متغيرات للصورة
  File? _selectedImage;
  String? _profileImageUrl;
  bool _isUploading = false;

  final List<String> countries = [
    'السعودية', 'مصر', 'الامارات', 'الأردن', 'البحرين',
    'الكويت', 'قطر', 'عمان', 'العراق', 'الجزائر',
    'المغرب', 'تونس', 'لبنان', 'سوريا', 'السودان',
    'ليبيا', 'فلسطين', 'اليمن',
  ];

  final Map<String, int> countryIdMap = {
    "مصر": 74,
    "الامارات": 244,
    "عمان": 172,
    "الاردن": 111,
    "البحرين": 17,
    "الجزائر": 3,
    "العراق": 104,
    "الكويت": 117,
    "المغرب": 148,
    "السعودية": 191,
    "اليمن": 243,
    "قطر": 181,
    "تونس": 216,
    "لبنان": 122,
    "السودان": 207,
    "ليبيا": 127,
    "فلسطين": 181,
    "سوريا": 207,
  };

  List<String> typeDarasa = ["ط§ظ†ط«ظٹ", "ط°ظƒط±"];

  @override
  void initState() {
    super.initState();
    _fillUserData();

    // Ensure no duplicate values in dropdown lists
    // countries = countries.toSet().toList(); // Remove any duplicates (commented: countries is final)
    typeDarasa = typeDarasa.toSet().toList(); // Remove any duplicates

    // Validate selected values exist in the lists
    if (selectedValueCountry != null && !countries.contains(selectedValueCountry)) {
      selectedValueCountry = null;
    }

    if (selectedValueGender != null && !typeDarasa.contains(selectedValueGender)) {
      selectedValueGender = null;
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  String? getCountryNameById(dynamic id) {
    if (id is int) {
      return countryIdMap.entries
          .firstWhere(
            (entry) => entry.value == id,
            orElse: () => const MapEntry('', 0),
          )
          .key
          .isNotEmpty
          ? countryIdMap.entries.firstWhere((entry) => entry.value == id).key
          : null;
    } else if (id is String) {
      return countryIdMap.entries
          .firstWhere(
            (entry) => entry.key == id,
            orElse: () => const MapEntry('', 0),
          )
          .key
          .isNotEmpty
          ? countryIdMap.entries.firstWhere((entry) => entry.key == id).key
          : null;
    }
    return null;
  }

  void _fillUserData() {
    // ظپظ‚ط· ط¥ط°ط§ ظ„ظ… ظٹظƒظ† ظ‡ظ†ط§ظƒ ظ‚ظٹظ…ط© ط­ط§ظ„ظٹط© ظپظٹ ط§ظ„ط­ظ‚ظˆظ„طŒ ط¹ظٹظ‘ظ† ظ…ظ† ط§ظ„ط³ظٹط±ظپط±
    if (_firstNameController.text.isEmpty) {
      _firstNameController.text = widget.user.firstName ?? widget.user.first_name ?? '';
    }
    if (_middleNameController.text.isEmpty) {
      _middleNameController.text = widget.user.middleName ?? widget.user.middle_name ?? '';
    }
    if (_lastNameController.text.isEmpty) {
      _lastNameController.text = widget.user.lastName ?? widget.user.last_name ?? '';
    }
    // طھط¹ظٹظٹظ† ط§ظ„ط¬ظ†ط³ ظ…ظ† ط§ظ„ط³ظٹط±ظپط± ط¥ط°ط§ ظ„ظ… ظٹظƒظ† ظ…ط­ط¯ط¯
    if (selectedValueGender == null) {
      if (widget.user.gender != null) {
        selectedValueGender = widget.user.gender;
      } else {
        if (widget.user.gender_id == 1) {
          selectedValueGender = "ط§ظ†ط«ظٹ";
        } else if (widget.user.gender_id == 2) {
          selectedValueGender = "ط°ظƒط±";
        }
      }
    }
    // طھط¹ظٹظٹظ† ط§ظ„ط¯ظˆظ„ط© ظ…ظ† ط§ظ„ط³ظٹط±ظپط± ط¥ط°ط§ ظ„ظ… طھظƒظ† ظ…ط­ط¯ط¯ط©
    if (selectedValueCountry == null) {
      var countryValue = widget.user.country;
      // ط¥ط°ط§ ظƒط§ظ†طھ country_id ظ…ظˆط¬ظˆط¯ط© ظˆط§ط³طھط®ط¯ظ…طھ ظƒط±ظ‚ظ…
      if ((countryValue == null || countryValue.isEmpty) && widget.user.country_id != null) {
        countryValue = getCountryNameById(widget.user.country_id);
      }
      selectedValueCountry = countryValue;
    }
    _profileImageUrl = widget.user.profile_image;
    _updateData();
  }

  void _updateData() {
    // طھط­ظˆظٹظ„ ط§ظ„ط¬ظ†ط³ ط¥ظ„ظ‰ ط§ظ„ط±ظ‚ظ… ط§ظ„ظ…ظ†ط§ط³ط¨
    int genderId = 0;
    if (selectedValueGender == "ط°ظƒط±") {
      genderId = 2;
    } else if (selectedValueGender == "ط§ظ†ط«ظٹ") {
      genderId = 1;
    }

    widget.onDataChanged({
      "first_name": _firstNameController.text,
      "middle_name": _middleNameController.text,
      "last_name": _lastNameController.text,
      "gender_id": genderId,
      "country": selectedValueCountry,
      "profile_image": _profileImageUrl,
    });
  }

  // ط¯ط§ظ„ط© ط§ط®طھظٹط§ط± ط§ظ„طµظˆط±ط© ظ…ظ† ط§ظ„ظ…ط¹ط±ط¶ ط£ظˆ ط§ظ„ظƒط§ظ…ظٹط±ط§
  Future<void> _pickImage() async {
    try {
      // ط¹ط±ط¶ ط®ظٹط§ط±ط§طھ ط§ط®طھظٹط§ط± ط§ظ„طµظˆط±ط©
      await showModalBottomSheet(
        context: context,
        backgroundColor: Theme.of(context).cardTheme.color,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (BuildContext context) {
          return SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "ط§ط®طھط± طµظˆط±ط© ط§ظ„ظ…ظ„ظپ ط§ظ„ط´ط®طµظٹ",
                    style: GoogleFonts.tajawal(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // ط®ظٹط§ط± ط§ظ„ظƒط§ظ…ظٹط±ط§
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          _getImage(ImageSource.camera);
                        },
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.camera_alt,
                                size: 30,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              "ط§ظ„ظƒط§ظ…ظٹط±ط§",
                              style: GoogleFonts.tajawal(
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // ط®ظٹط§ط± ط§ظ„ظ…ط¹ط±ط¶
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          _getImage(ImageSource.gallery);
                        },
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.photo_library,
                                size: 30,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              "ط§ظ„ظ…ط¹ط±ط¶",
                              style: GoogleFonts.tajawal(
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      AppMessages.showError("ط­ط¯ط« ط®ط·ط£ ط£ط«ظ†ط§ط، ظپطھط­ ط§ط®طھظٹط§ط± ط§ظ„طµظˆط±ط©");
    }
  }

  // ط¯ط§ظ„ط© ط§ظ„ط­طµظˆظ„ ط¹ظ„ظ‰ ط§ظ„طµظˆط±ط© ظ…ظ† ط§ظ„ظ…طµط¯ط± ط§ظ„ظ…ط­ط¯ط¯
  Future<void> _getImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _isUploading = true;
        });

        // ط±ظپط¹ ط§ظ„طµظˆط±ط© ط¥ظ„ظ‰ ط§ظ„ط³ظٹط±ظپط±
        // final imageUrl = await ProfileApi().uploadProfileImage(_selectedImage!);
        const imageUrl = '';

        if (imageUrl != null) {
          setState(() {
            _profileImageUrl = imageUrl;
            _isUploading = false;
          });

          // طھط­ط¯ظٹط« ط§ظ„ط¨ظٹط§ظ†ط§طھ
          _updateData();

          // ط¹ط±ط¶ ط±ط³ط§ظ„ط© ظ†ط¬ط§ط­
          AppMessages.showSuccess("طھظ… ط±ظپط¹ ط§ظ„طµظˆط±ط© ط¨ظ†ط¬ط§ط­");
        } else {
          setState(() {
            _isUploading = false;
          });

          // ط¹ط±ط¶ ط±ط³ط§ظ„ط© ط®ط·ط£
          AppMessages.showError("ظپط´ظ„ ظپظٹ ط±ظپط¹ ط§ظ„طµظˆط±ط©");
        }
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
      });

      // ط¹ط±ط¶ ط±ط³ط§ظ„ط© ط®ط·ط£
      AppMessages.showError("ط­ط¯ط« ط®ط·ط£ ط£ط«ظ†ط§ط، ط§ط®طھظٹط§ط± ط§ظ„طµظˆط±ط©");
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSectionTitle("ط§ظ„ط¨ظٹط§ظ†ط§طھ ط§ظ„ط´ط®طµظٹط©", Icons.person),
          const SizedBox(height: 16),

          _buildProfileImage(),
          const SizedBox(height: 24),

          buildInputField(
            controller: _firstNameController,
            label: "ط§ظ„ط§ط³ظ… ط§ظ„ط£ظˆظ„",
            icon: Icons.person_outline,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "ط§ظ„ط±ط¬ط§ط، ط¥ط¯ط®ط§ظ„ ط§ظ„ط§ط³ظ… ط§ظ„ط£ظˆظ„";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),

          buildInputField(
            controller: _middleNameController,
            label: "ط§ظ„ط§ط³ظ… ط§ظ„ط£ظˆط³ط·",
            icon: Icons.person_outline,
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),

          buildInputField(
            controller: _lastNameController,
            label: "ط§ظ„ظ„ظ‚ط¨",
            icon: Icons.person_outline,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "ط§ظ„ط±ط¬ط§ط، ط¥ط¯ط®ط§ظ„ ط§ظ„ظ„ظ‚ط¨";
              }
              return null;
            },
            onChanged: (value) => _updateData(),
          ),
          const SizedBox(height: 16),

          buildDropdownField(
            label: "ط§ظ„ط¬ظ†ط³",
            value: selectedValueGender,
            items: typeDarasa,
            onChanged: (value) {
              setState(() {
                selectedValueGender = value;
                _updateData();
              });
            },
            icon: Icons.wc,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "ط§ظ„ط±ط¬ط§ط، ط§ط®طھظٹط§ط± ط§ظ„ط¬ظ†ط³";
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          buildDropdownField(
            label: "ط§ظ„ط¯ظˆظ„ط©",
            value: selectedValueCountry,
            items: countries,
            onChanged: (value) {
              setState(() {
                selectedValueCountry = value;
                _updateData();
              });
            },
            icon: Icons.flag,
            isRequired: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "ط§ظ„ط±ط¬ط§ط، ط§ط®طھظٹط§ط± ط§ظ„ط¯ظˆظ„ط©";
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImage() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Center(
      child: Column(
        children: [
          GestureDetector(
            onTap: _pickImage,
            child: Stack(
              children: [
                // طµظˆط±ط© ط§ظ„ظ…ظ„ظپ ط§ظ„ط´ط®طµظٹ
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Theme.of(context).cardTheme.color?.withOpacity(0.5)
                        : Colors.grey[200],
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: primaryColor.withOpacity(0.5),
                      width: 2,
                    ),
                    image: _selectedImage != null
                        ? DecorationImage(
                            image: FileImage(_selectedImage!),
                            fit: BoxFit.cover,
                          )
                        : _profileImageUrl != null
                            ? DecorationImage(
                                image: NetworkImage(_profileImageUrl!),
                                fit: BoxFit.cover,
                              )
                            : null,
                  ),
                  child: _profileImageUrl == null && _selectedImage == null
                      ? Icon(
                          Icons.person,
                          size: 60,
                          color: primaryColor.withOpacity(0.7),
                        )
                      : null,
                ),

                // ط£ظٹظ‚ظˆظ†ط© ط§ظ„ظƒط§ظ…ظٹط±ط§
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: primaryColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isDarkMode ? Colors.black : Colors.white,
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),

                // ظ…ط¤ط´ط± ط§ظ„طھط­ظ…ظٹظ„
                if (_isUploading)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "ط§ط¶ط؛ط· ظ„طھط؛ظٹظٹط± ط§ظ„طµظˆط±ط©",
            style: TextStyle(
              fontSize: 14,
              color: primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}


