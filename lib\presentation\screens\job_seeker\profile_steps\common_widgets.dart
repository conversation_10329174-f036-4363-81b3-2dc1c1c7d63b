import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// عنوان القسم
Widget buildSectionTitle(String title, IconData icon) {
  return Builder(
    builder: (context) {
      final primaryColor = Theme.of(context).colorScheme.primary;

      return Row(
        children: [
          Icon(
            icon,
            color: primaryColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.tajawal(
              color: primaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    }
  );
}

// حقل إدخال النص
Widget buildInputField({
  required TextEditingController controller,
  required String label,
  required IconData icon,
  bool isRequired = false,
  bool isPassword = false,
  String? Function(String?)? validator,
  void Function(String)? onChanged,
  TextInputType keyboardType = TextInputType.text,
  int maxLines = 1,
  String? helperText,
  Widget? suffixIcon,
}) {
  return Builder(
    builder: (context) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      final primaryColor = Theme.of(context).colorScheme.primary;

      return TextFormField(
        controller: controller,
        obscureText: isPassword,
        decoration: InputDecoration(
          labelText: isRequired ? "$label *" : label,
          labelStyle: GoogleFonts.tajawal(
            color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
          ),
          prefixIcon: Icon(
            icon,
            color: primaryColor,
          ),
          suffixIcon: suffixIcon,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: primaryColor,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: isDarkMode ? Colors.red[300]! : Colors.red,
            ),
          ),
          helperText: helperText,
          helperStyle: GoogleFonts.tajawal(
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            fontSize: 12,
          ),
          filled: isDarkMode,
          fillColor: isDarkMode ? Theme.of(context).inputDecorationTheme.fillColor : null,
        ),
        style: GoogleFonts.tajawal(
          color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : Colors.black87,
          fontSize: 16,
        ),
        keyboardType: keyboardType,
        maxLines: maxLines,
        validator: validator,
        onChanged: onChanged,
      );
    }
  );
}

// حقل القائمة المنسدلة
Widget buildDropdownField({
  required String label,
  required String? value,
  required List<String> items,
  required void Function(String?) onChanged,
  required IconData icon,
  bool isRequired = false,
  String? Function(String?)? validator,
}) {
  return Builder(
    builder: (context) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      final primaryColor = Theme.of(context).colorScheme.primary;

      return DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          labelText: isRequired ? "$label *" : label,
          labelStyle: GoogleFonts.tajawal(
            color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
          ),
          prefixIcon: Icon(
            icon,
            color: primaryColor,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: primaryColor,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: isDarkMode ? Colors.red[300]! : Colors.red,
            ),
          ),
          filled: isDarkMode,
          fillColor: isDarkMode ? Theme.of(context).inputDecorationTheme.fillColor : null,
        ),
        style: GoogleFonts.tajawal(
          color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : Colors.black87,
          fontSize: 16,
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: GoogleFonts.tajawal(
                color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : Colors.black87,
              ),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        validator: validator,
        icon: Icon(
          Icons.arrow_drop_down,
          color: primaryColor,
        ),
        isExpanded: true,
        dropdownColor: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
      );
    }
  );
}

// حقل التبديل (سويتش)
Widget buildSwitchField({
  required String label,
  required bool value,
  required void Function(bool) onChanged,
  required IconData icon,
}) {
  return Builder(
    builder: (context) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      final primaryColor = Theme.of(context).colorScheme.primary;

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
          border: Border.all(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: primaryColor,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                label,
                style: GoogleFonts.tajawal(
                  color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : Colors.black87,
                  fontSize: 16,
                ),
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: primaryColor,
              activeTrackColor: primaryColor.withOpacity(0.5),
            ),
          ],
        ),
      );
    }
  );
}

// حقل التاريخ
Widget buildDateField({
  required String label,
  required String value,
  required Function() onTap,
  required IconData icon,
  bool isRequired = false,
  String? Function(String?)? validator,
}) {
  return Builder(
    builder: (context) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      final primaryColor = Theme.of(context).colorScheme.primary;

      return InkWell(
        onTap: onTap,
        child: InputDecorator(
          decoration: InputDecoration(
            labelText: isRequired ? "$label *" : label,
            labelStyle: GoogleFonts.tajawal(
              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
            ),
            prefixIcon: Icon(
              icon,
              color: primaryColor,
            ),
            suffixIcon: Icon(
              Icons.calendar_today,
              color: primaryColor,
              size: 20,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: primaryColor,
                width: 2,
              ),
            ),
            filled: isDarkMode,
            fillColor: isDarkMode ? Theme.of(context).inputDecorationTheme.fillColor : null,
          ),
          child: Text(
            value.isEmpty ? "اختر التاريخ" : value,
            style: GoogleFonts.tajawal(
              color: isDarkMode
                  ? (value.isEmpty ? Colors.grey[500] : Theme.of(context).textTheme.bodyLarge?.color)
                  : (value.isEmpty ? Colors.grey[600] : Colors.black87),
              fontSize: 16,
            ),
          ),
        ),
      );
    }
  );
}

// حقل تحميل الملفات
Widget buildFileUploadField({
  required String label,
  required String fileName,
  required Function() onTap,
  required IconData icon,
  bool isRequired = false,
}) {
  return Builder(
    builder: (context) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      final primaryColor = Theme.of(context).colorScheme.primary;

      return InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          decoration: BoxDecoration(
            color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
            border: Border.all(
              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: primaryColor,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isRequired ? "$label *" : label,
                      style: GoogleFonts.tajawal(
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        fontSize: 14,
                      ),
                    ),
                    if (fileName.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          fileName,
                          style: GoogleFonts.tajawal(
                            color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : Colors.black87,
                            fontSize: 14,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.upload_file,
                color: primaryColor,
              ),
            ],
          ),
        ),
      );
    }
  );
}

// بطاقة معلومات
Widget buildInfoCard({
  required String title,
  required String content,
  required IconData icon,
}) {
  return Builder(
    builder: (context) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      final primaryColor = Theme.of(context).colorScheme.primary;

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: primaryColor.withOpacity(isDarkMode ? 0.2 : 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: primaryColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.tajawal(
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    content,
                    style: GoogleFonts.tajawal(
                      color: isDarkMode ? Theme.of(context).textTheme.bodyLarge?.color : Colors.black87,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  );
}