﻿import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/notification_provider.dart';
import 'package:wzzff/services/Notifications.dart';

class NotificationBadge extends StatefulWidget {
  const NotificationBadge({super.key});

  @override
  State<NotificationBadge> createState() => _NotificationBadgeState();
}

class _NotificationBadgeState extends State<NotificationBadge> {
  Timer? _updateTimer;

  @override
  void initState() {
    super.initState();
    // تحديث الإشعارات عند تهيئة الشارة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateNotifications();
    });

    // إضافة مستمع لتحديث الإشعارات كل 5 ثوانِ
    // تم تعديل الكود ليكون أكثر كفاءة وأقل استهلاكاً للموارد
    // تغيير المدة من 5 ثوانِ إلى 60 ثانية لتقليل عدد مرات التحديث
    // هذا سيمنع ظهور واختفاء العدد بشكل متكرر
    _updateTimer = Timer.periodic(
      const Duration(seconds: 60),
      (Timer timer) {
        if (mounted) {
          _updateNotifications();
        }
      },
    );

    // بدء التحديث الدوري
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    super.dispose();
  }

  // تحديث الإشعارات
  Future<void> _updateNotifications() async {
    if (!mounted) return;
    
    try {
      final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
      await notificationProvider.loadNotifications();
      // تسجيل للتأكد من أن التحديث يعمل
    } catch (e) {
    }
  }

  void _navigateToNotifications() async {
    // فتح صفحة الإشعارات بطريقة آمنة
    if (mounted) {
      // تحديث الإشعارات قبل فتح الصفحة
      await _updateNotifications();
      
      // فتح صفحة الإشعارات
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const Notifications(),
        ),
      );

      // تحديث الإشعارات بعد العودة من صفحة الإشعارات
      if (mounted) {
        // استخدام تأخير قصير لضمان تحديث الإشعارات بعد العودة
        await Future.delayed(const Duration(milliseconds: 500));
        await _updateNotifications();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        final unreadCount = notificationProvider.unreadCount;

        return Stack(
          children: [
            IconButton(
              icon: const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
              ),
              onPressed: _navigateToNotifications,
            ),
            if (unreadCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    unreadCount > 99 ? '99+' : unreadCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
