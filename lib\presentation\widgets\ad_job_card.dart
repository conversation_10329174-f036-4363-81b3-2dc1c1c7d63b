import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wzzff/services/google_ad_service.dart';

/// بطاقة إعلان تظهر بين بطاقات الوظائف
class AdJobCard extends StatefulWidget {
  const AdJobCard({Key? key}) : super(key: key);

  @override
  State<AdJobCard> createState() => _AdJobCardState();
}

class _AdJobCardState extends State<AdJobCard> {
  final GoogleAdService _adService = GoogleAdService();

  @override
  void initState() {
    super.initState();
    // تهيئة خدمة الإعلانات
    _adService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? Theme.of(context).cardTheme.color : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(isDarkMode ? 51 : 13), // 0.2 = 51, 0.05 = 13
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        /*
        child: _adService.createBannerAdWidget(
          size: AdSize.mediumRectangle,
        ),
        */
      ),
    );
  }
}
