import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:wzzff/models/JobNewsModel.dart';
import 'package:provider/provider.dart';
import 'package:wzzff/core/providers/app_state_provider.dart';
import 'package:wzzff/main.dart';
import 'package:wzzff/core/utils/app_messages.dart';

class JobNewsApi {
  static Future<List<JobNewsModel>> fetchPaginated({int offset = 0}) async {
    try {
      final url = Uri.parse('https://wzzff.com/apiwzzff/jobsNewsPaginate');
      final response = await http.post(url, body: {"offset": offset.toString()});
      if (response.statusCode == 200) {
        final List data = jsonDecode(response.body);
        return data.map((e) => JobNewsModel.fromJson(e)).toList();
      } else {
        throw Exception('فشل في جلب أخبار الوظائف');
      }
    } catch (e) {
      return [];
    }
  }
}
