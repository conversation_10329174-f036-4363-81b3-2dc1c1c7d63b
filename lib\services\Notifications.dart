import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wzzff/Apis/LoginAndCheckAndRegi.dart';
import 'package:wzzff/Apis/ProfileApi.dart';
import 'package:wzzff/core/providers/notification_provider.dart';
import 'package:wzzff/models/notification_model.dart';
import 'package:wzzff/services/firebase_messaging_service.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  State<Notifications> createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications> {
  late NotificationProvider _notificationProvider;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // تهيئة مزود الإشعارات
    _notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    // استخدام Future.microtask لتأخير استدعاء _fetchNotifications حتى اكتمال البناء
    Future.microtask(() => _fetchNotifications());
  }

  Future<void> _fetchNotifications() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الإشعارات من التخزين المحلي
      await _notificationProvider.loadNotifications();

      // تعليم جميع الإشعارات كمقروءة عند فتح صفحة الإشعارات
      // يمكن تعليق هذا السطر إذا كنت تريد أن يقوم المستخدم بتعليم الإشعارات كمقروءة يدويًا
      // await _notificationProvider.markAllAsRead();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء تحميل الإشعارات',
              style: GoogleFonts.tajawal(),
              textAlign: TextAlign.center,
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(10),
          ),
        );
      }
      debugPrint('خطأ في تحميل الإشعارات: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تعليم جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    try {
      await _notificationProvider.markAllAsRead();

      // إعادة حساب عدد الإشعارات غير المقروءة
      _notificationProvider.recalculateUnreadCount();

      // تحديث علامة تحديث الإشعارات في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notifications_updated', true);
      await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);

      // التحقق من أن الـ widget لا يزال مثبتًا قبل استخدام context
      if (!mounted) return;

      // عرض رسالة نجاح
      final primaryColor = Theme.of(context).colorScheme.primary;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم تعليم جميع الإشعارات كمقروءة',
            style: GoogleFonts.tajawal(),
            textAlign: TextAlign.center,
          ),
          backgroundColor: primaryColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(10),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في تعليم جميع الإشعارات كمقروءة: $e');
    }
  }

  // حذف جميع الإشعارات
  Future<void> _clearAllNotifications() async {
    // عرض مربع حوار للتأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حذف جميع الإشعارات',
          style: GoogleFonts.tajawal(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        content: Text(
          'هل أنت متأكد من حذف جميع الإشعارات؟',
          style: GoogleFonts.tajawal(),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'إلغاء',
              style: GoogleFonts.tajawal(
                color: Colors.grey,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              'حذف',
              style: GoogleFonts.tajawal(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _notificationProvider.clearAllNotifications();

        // إعادة حساب عدد الإشعارات غير المقروءة
        _notificationProvider.recalculateUnreadCount();

        // تحديث علامة تحديث الإشعارات في SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('notifications_updated', true);
        await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف جميع الإشعارات',
                style: GoogleFonts.tajawal(),
                textAlign: TextAlign.center,
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(10),
            ),
          );
        }
      } catch (e) {
        debugPrint('خطأ في حذف جميع الإشعارات: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).scaffoldBackgroundColor
          : Colors.white,
      appBar: AppBar(
        title: Text(
          "الإشعارات",
          style: GoogleFonts.tajawal(
            fontWeight: FontWeight.bold,
            fontSize: 18,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        backgroundColor: primaryColor,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        leading:  IconButton(
            icon: const Icon(Icons.refresh, size: 22, color: Colors.white),
            onPressed: _fetchNotifications,
            tooltip: 'تحديث',
          ),
        actions: [
          // زر إدارة الاشتراكات
          IconButton(
            icon: const Icon(Icons.notifications_active, size: 22, color: Colors.white),
            onPressed: _showNotificationTopicsDialog,
            tooltip: 'إدارة اشتراكات الإشعارات',
          ),
          // زر تعليم الكل كمقروء
          IconButton(
            icon: const Icon(Icons.done_all, size: 22, color: Colors.white),
            onPressed: () {
              if (_notificationProvider.notifications.isNotEmpty) {
                _markAllAsRead();
              }
            },
            tooltip: 'تعليم الكل كمقروء',
          ),
          // زر حذف الكل
          IconButton(
            icon: const Icon(Icons.delete_sweep, size: 22, color: Colors.white),
            onPressed: () {
              if (_notificationProvider.notifications.isNotEmpty) {
                _clearAllNotifications();
              }
            },
            tooltip: 'حذف الكل',
          ),
          IconButton(
          icon: Icon(
             Icons.arrow_forward_ios ,
            size: 20,
            color: Colors.white,
          ),
          onPressed: () => Navigator.pop(context),
        )
         
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                primaryColor,
                primaryColor.withAlpha(204), // 0.8 = 204
              ],
            ),
          ),
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDarkMode ? Brightness.light : Brightness.light,
        ),
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Consumer<NotificationProvider>(
          builder: (context, provider, child) {
            if (_isLoading) {
              return Center(child: CircularProgressIndicator(color: primaryColor));
            } else if (provider.notifications.isEmpty) {
              return _buildEmptyState();
            } else {
              return _buildNotificationsList(provider.notifications);
            }
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? Colors.grey.withAlpha(51) // 0.2 = 51
                  : Colors.grey.withAlpha(26), // 0.1 = 26
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.notifications_off_outlined,
              color: primaryColor.withAlpha(179), // 0.7 = 179
              size: 80,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            "لا يوجد لديك إشعارات حالياً",
            style: GoogleFonts.tajawal(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            "سيتم إعلامك عند وصول إشعارات جديدة",
            style: GoogleFonts.tajawal(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: _fetchNotifications,
            icon: const Icon(Icons.refresh),
            label: Text(
              "تحديث",
              style: GoogleFonts.tajawal(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(List<NotificationModel> notifications) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationItem(notification);
      },
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Dismissible(
      key: Key(notification.id),
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: Colors.red,
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) async {
        await _notificationProvider.deleteNotification(notification.id);

        // إعادة حساب عدد الإشعارات غير المقروءة
        _notificationProvider.recalculateUnreadCount();

        // تحديث علامة تحديث الإشعارات في SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('notifications_updated', true);
        await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف الإشعار',
                style: GoogleFonts.tajawal(),
                textAlign: TextAlign.center,
              ),
              backgroundColor: primaryColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(10),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      },
      confirmDismiss: (direction) async {
        return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              'حذف الإشعار',
              style: GoogleFonts.tajawal(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            content: Text(
              'هل أنت متأكد من حذف هذا الإشعار؟',
              style: GoogleFonts.tajawal(),
              textAlign: TextAlign.center,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.tajawal(
                    color: Colors.grey,
                  ),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(
                  'حذف',
                  style: GoogleFonts.tajawal(
                    color: Colors.red,
                  ),
                ),
              ),
            ],
          ),
        );
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 1,
        color: notification.isRead
            ? (isDarkMode ? Theme.of(context).cardTheme.color : Colors.white)
            : (isDarkMode
                ? Theme.of(context).colorScheme.primary.withAlpha(38) // 0.15 = 38
                : Theme.of(context).colorScheme.primary.withAlpha(13)), // 0.05 = 13
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: notification.isRead
              ? BorderSide.none
              : BorderSide(
                  color: primaryColor.withAlpha(77), // 0.3 = 77
                  width: 1,
                ),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () async {
            // تعليم الإشعار كمقروء عند النقر عليه
            if (!notification.isRead) {
              await _notificationProvider.markAsRead(notification.id);

              // إعادة حساب عدد الإشعارات غير المقروءة
              _notificationProvider.recalculateUnreadCount();

              // تحديث علامة تحديث الإشعارات في SharedPreferences
              final prefs = await SharedPreferences.getInstance();
              await prefs.setBool('notifications_updated', true);
              await prefs.setInt('notifications_last_update', DateTime.now().millisecondsSinceEpoch);

              // عرض رسالة تأكيد (اختياري)
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم تعليم الإشعار كمقروء',
                      style: GoogleFonts.tajawal(),
                      textAlign: TextAlign.center,
                    ),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    margin: const EdgeInsets.all(10),
                    duration: const Duration(seconds: 1),
                  ),
                );
              }
            }

            // يمكن إضافة إجراء آخر عند النقر على الإشعار
            // مثل فتح صفحة تفاصيل الإشعار
          },
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // أيقونة الإشعار
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: primaryColor.withAlpha(isDarkMode ? 77 : 26), // 0.3 = 77, 0.1 = 26
                    shape: BoxShape.circle,
                  ),
                  child: notification.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(25),
                          child: Image.network(
                            notification.imageUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              Icons.notifications,
                              color: primaryColor,
                              size: 25,
                            ),
                          ),
                        )
                      : Icon(
                          Icons.notifications,
                          color: primaryColor,
                          size: 25,
                        ),
                ),
                const SizedBox(width: 12),
                // محتوى الإشعار
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان الإشعار
                      Text(
                        notification.title,
                        style: GoogleFonts.tajawal(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // نص الإشعار
                      Text(
                        notification.body,
                        style: GoogleFonts.tajawal(
                          fontSize: 14,
                          color: isDarkMode ? Colors.grey[300] : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // وقت الإشعار
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            notification.formattedTime,
                            style: GoogleFonts.tajawal(
                              fontSize: 12,
                              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                            ),
                          ),
                          // مؤشر الإشعار غير المقروء
                          if (!notification.isRead)
                            Container(
                              width: 10,
                              height: 10,
                              decoration: BoxDecoration(
                                color: primaryColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // عرض مربع حوار لإدارة اشتراكات الإشعارات
  Future<void> _showNotificationTopicsDialog() async {
    // إنشاء خدمة Firebase Messaging
    final messagingService = FirebaseMessagingService();

    // الحصول على حالة الاشتراك في المواضيع
    final prefs = await SharedPreferences.getInstance();

    // حالة الاشتراك في المواضيع
    bool isSubscribedToJobs = prefs.getBool('subscribed_to_jobs') ?? true;
    bool isSubscribedToJobsNews = prefs.getBool('subscribed_to_jobs_news') ?? true;
    bool isSubscribedToNews = prefs.getBool('subscribed_to_news') ?? true;

    // قائمة البلدان المتاحة للاشتراك مع أسمائها
    final availableCountries = {
      'sa': 'السعودية',
      'eg': 'مصر',
      'ae': 'الامارات',
      'jo': 'الأردن',
      'bh': 'البحرين',
      'kw': 'الكويت',
      'qa': 'قطر',
      'om': 'عمان',
      'iq': 'العراق',
      'dz': 'الجزائر',
      'ma': 'المغرب',
      'tn': 'تونس',
      'lb': 'لبنان',
      'sy': 'سوريا',
      'sd': 'السودان',
      'ly': 'ليبيا',
      'ps': 'فلسطين',
      'ye': 'اليمن',
    };

    // إنشاء قائمة البلدان مع حالة الاشتراك لكل بلد
    Map<String, Map<String, dynamic>> countriesWithSubscriptionStatus = {};

    // إضافة جميع البلدان المتاحة إلى القائمة
    for (final entry in availableCountries.entries) {
      final countryCode = entry.key;
      final countryName = entry.value;

      // التحقق من حالة الاشتراك في البلد
      final key = 'subscribed_to_country_$countryCode';
      final isSubscribed = prefs.getBool(key) ?? false;

      // إضافة البلد إلى القائمة مع حالة الاشتراك
      countriesWithSubscriptionStatus[countryCode] = {
        'name': countryName,
        'isSubscribed': isSubscribed,
      };

      debugPrint('البلد: $countryCode - $countryName (مشترك: $isSubscribed)');
    }

    // إضافة بلد المستخدم إلى القائمة إذا لم يكن موجودًا بالفعل
    try {
      final userModel = await ProfileApi().getMyProfileInformation();
      if (userModel.country != null && userModel.country!.isNotEmpty) {
        final countryCode = _getCountryCode(userModel.country!);
        if (!countriesWithSubscriptionStatus.containsKey(countryCode)) {
          // التحقق من حالة الاشتراك في بلد المستخدم
          final key = 'subscribed_to_country_$countryCode';
          final isSubscribed = prefs.getBool(key) ?? false;

          // إضافة بلد المستخدم إلى القائمة
          countriesWithSubscriptionStatus[countryCode] = {
            'name': userModel.country!,
            'isSubscribed': isSubscribed,
          };

          debugPrint('تمت إضافة بلد المستخدم: $countryCode - ${userModel.country!} (مشترك: $isSubscribed)');
        }
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات المستخدم: $e');
    }

    // طباعة قائمة البلدان مع حالة الاشتراك
    debugPrint('قائمة البلدان مع حالة الاشتراك: $countriesWithSubscriptionStatus');

    // إضافة البلدان التي تم الاشتراك فيها من خلال LoadScreen.dart
    final keys = prefs.getKeys();
    final countrySubscriptionKeys = keys.where((key) => key.startsWith('subscribed_to_country_')).toList();

    for (final key in countrySubscriptionKeys) {
      final isSubscribed = prefs.getBool(key) ?? false;

      if (isSubscribed) {
        final countryCode = key.replaceFirst('subscribed_to_country_', '');

        // إذا كان البلد موجودًا في القائمة، نحدث حالة الاشتراك
        if (countriesWithSubscriptionStatus.containsKey(countryCode)) {
          countriesWithSubscriptionStatus[countryCode]!['isSubscribed'] = true;
          debugPrint('تم تحديث حالة الاشتراك للبلد: $countryCode - ${countriesWithSubscriptionStatus[countryCode]!['name']} (مشترك: true)');
        }
        // إذا لم يكن البلد موجودًا في القائمة، نضيفه
        else {
          final countryName = _getCountryNameFromCode(countryCode);
          countriesWithSubscriptionStatus[countryCode] = {
            'name': countryName,
            'isSubscribed': true,
          };
          debugPrint('تمت إضافة البلد من SharedPreferences: $countryCode - $countryName (مشترك: true)');
        }
      }
    }

    // طباعة قائمة البلدان النهائية مع حالة الاشتراك
    debugPrint('قائمة البلدان النهائية مع حالة الاشتراك: $countriesWithSubscriptionStatus');

    // عرض مربع حوار لإدارة الاشتراكات
    if (!mounted) return;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'إدارة اشتراكات الإشعارات',
            style: GoogleFonts.tajawal(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'يمكنك إدارة أنواع الإشعارات التي ترغب في استلامها',
                  style: GoogleFonts.tajawal(),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                // اشتراك الوظائف
                SwitchListTile(
                  title: Text(
                    'الوظائف',
                    style: GoogleFonts.tajawal(),
                  ),
                  subtitle: Text(
                    'إشعارات الوظائف الجديدة',
                    style: GoogleFonts.tajawal(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  value: isSubscribedToJobs,
                  onChanged: (value) async {
                    if (value) {
                      await messagingService.subscribeToTopic('jobs');
                    } else {
                      await messagingService.unsubscribeFromTopic('jobs');
                    }
                    await prefs.setBool('subscribed_to_jobs', value);
                    setState(() {
                      isSubscribedToJobs = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
                // اشتراك أخبار الوظائف
                SwitchListTile(
                  title: Text(
                    'أخبار الوظائف',
                    style: GoogleFonts.tajawal(),
                  ),
                  subtitle: Text(
                    'إشعارات أخبار الوظائف والتوظيف',
                    style: GoogleFonts.tajawal(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  value: isSubscribedToJobsNews,
                  onChanged: (value) async {
                    if (value) {
                      await messagingService.subscribeToTopic('jobs_news');
                    } else {
                      await messagingService.unsubscribeFromTopic('jobs_news');
                    }
                    await prefs.setBool('subscribed_to_jobs_news', value);
                    setState(() {
                      isSubscribedToJobsNews = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
                // اشتراك الأخبار
                SwitchListTile(
                  title: Text(
                    'الأخبار',
                    style: GoogleFonts.tajawal(),
                  ),
                  subtitle: Text(
                    'إشعارات الأخبار العامة',
                    style: GoogleFonts.tajawal(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  value: isSubscribedToNews,
                  onChanged: (value) async {
                    if (value) {
                      await messagingService.subscribeToTopic('news');
                    } else {
                      await messagingService.unsubscribeFromTopic('news');
                    }
                    await prefs.setBool('subscribed_to_news', value);
                    setState(() {
                      isSubscribedToNews = value;
                    });
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
                // عنوان قسم البلدان
                Padding(
                  padding: const EdgeInsets.only(top: 16, bottom: 8),
                  child: Text(
                    'اشتراكات البلدان',
                    style: GoogleFonts.tajawal(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // اشتراكات البلدان
                ...countriesWithSubscriptionStatus.entries.map((entry) {
                  final countryCode = entry.key;
                  final countryData = entry.value;
                  final countryName = countryData['name'] as String;
                  final isSubscribed = countryData['isSubscribed'] as bool;

                  return SwitchListTile(
                    title: Text(
                      'إشعارات $countryName',
                      style: GoogleFonts.tajawal(),
                    ),
                    subtitle: Text(
                      'إشعارات خاصة بوظائف $countryName',
                      style: GoogleFonts.tajawal(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                    value: isSubscribed,
                    onChanged: (value) async {
                      // تحديث حالة الاشتراك في واجهة المستخدم فوراً
                      setState(() {
                        // تحديث القيمة في نموذج البيانات
                        countriesWithSubscriptionStatus[countryCode]!['isSubscribed'] = value;
                      });

                      // تنفيذ عملية الاشتراك أو إلغاء الاشتراك
                      if (value) {
                        await messagingService.subscribeToTopic(countryCode);
                        debugPrint('تم الاشتراك في موضوع البلد: $countryCode');
                      } else {
                        await messagingService.unsubscribeFromTopic(countryCode);
                        debugPrint('تم إلغاء الاشتراك من موضوع البلد: $countryCode');
                      }

                      // تخزين حالة الاشتراك
                      await prefs.setBool('subscribed_to_country_$countryCode', value);

                      // التحقق من أن القيمة تم تخزينها بشكل صحيح
                      final verifySubscribed = prefs.getBool('subscribed_to_country_$countryCode') ?? false;
                      debugPrint('تم التحقق من تخزين حالة الاشتراك لـ $countryCode: $verifySubscribed');

                      // تحديث حالة الاشتراك في واجهة المستخدم مرة أخرى بعد التخزين
                      if (mounted && verifySubscribed != value) {
                        setState(() {
                          countriesWithSubscriptionStatus[countryCode]!['isSubscribed'] = verifySubscribed;
                        });
                      }

                      // تخزين رسالة للعرض لاحقاً
                      final message = value
                        ? 'تم الاشتراك في إشعارات وظائف $countryName'
                        : 'تم إلغاء الاشتراك من إشعارات وظائف $countryName';

                      // استخدام WidgetsBinding لتنفيذ الكود بعد اكتمال العمليات غير المتزامنة
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (mounted) {
                          // الحصول على الألوان من السياق الحالي
                          final primaryColor = Theme.of(context).colorScheme.primary;

                          // عرض رسالة
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                message,
                                style: GoogleFonts.tajawal(),
                                textAlign: TextAlign.center,
                              ),
                              duration: const Duration(seconds: 2),
                              backgroundColor: primaryColor,
                            ),
                          );
                        }
                      });
                    },
                    activeColor: Theme.of(context).colorScheme.primary,
                  );
                }).toList(),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'إغلاق',
                style: GoogleFonts.tajawal(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة مساعدة للحصول على رمز الدولة
  String _getCountryCode(String country) {
    switch (country) {
      case "السعودية": return "sa";
      case "مصر": return "eg";
      case "الامارات": return "ae";
      case "الأردن": return "jo";
      case "البحرين": return "bh";
      case "الكويت": return "kw";
      case "قطر": return "qa";
      case "عمان": return "om";
      case "العراق": return "iq";
      case "الجزائر": return "dz";
      case "المغرب": return "ma";
      case "تونس": return "tn";
      case "لبنان": return "lb";
      case "سوريا": return "sy";
      case "السودان": return "sd";
      case "ليبيا": return "ly";
      case "فلسطين": return "ps";
      case "اليمن": return "ye";
      default: return country.toLowerCase();
    }
  }

  // دالة مساعدة للحصول على اسم الدولة من الرمز
  String _getCountryNameFromCode(String code) {
    switch (code.toLowerCase()) {
      case "sa": return "السعودية";
      case "eg": return "مصر";
      case "ae": return "الامارات";
      case "jo": return "الأردن";
      case "bh": return "البحرين";
      case "kw": return "الكويت";
      case "qa": return "قطر";
      case "om": return "عمان";
      case "iq": return "العراق";
      case "dz": return "الجزائر";
      case "ma": return "المغرب";
      case "tn": return "تونس";
      case "lb": return "لبنان";
      case "sy": return "سوريا";
      case "sd": return "السودان";
      case "ly": return "ليبيا";
      case "ps": return "فلسطين";
      case "ye": return "اليمن";
      default: return code.toUpperCase(); // إذا لم يتم العثور على اسم، نعرض الرمز بأحرف كبيرة
    }
  }


}
