import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class Terms_ofUsing extends StatelessWidget {
  const Terms_ofUsing({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xff2daae2),
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
            ),
            onPressed: () => Navigator.pop(context),
            tooltip: 'رجوع',
          ),
        ],
        title: Text(
          'شروط الاستخدام',
          style: GoogleFonts.tajawal(
            fontSize: 20, 
            fontWeight: FontWeight.bold,
            color: Colors.white
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xff2daae2).withOpacity(0.05),
              Colors.white,
            ],
          ),
        ),
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: ListView(
              children: [
                _buildHeaderCard(),
                
                _buildSectionTitle('شروط الاستخدام'),
                _buildSectionContent(
                  'تنطبق المصطلحات التالية على هذه الشروط والأحكام وبيان الخصوصية وإخطار إخلاء المسؤولية وأي الاتفاقيات (المستفيد أو المستفيدين / مستخدم/ أنت / الخاص بك) تعنيك أنت الشخص الذي يقوم بالدخول على هذا الموقع الإلكتروني او استخدام التطبيق والذي يقبل بشروط وأحكام الموقع. (تطبيق ذا فوكس اب للتعليم الألكتروني/ أنفسنا / نحن / لنا) تعني . إن استعمال المصطلحات أعلاه أو أي كلمات أخرى بصفة المفرد أو الجمع (و/ أو، أنه / أنها) تؤخذ على أن تشير إلى نفس المصطلح.\n\nإن دخولك واستخدامك للموقع او التطبيق يخضع للشروط والأحكام الموضحة أدناه، بالإضافة إلى قوانين جمهورية مصر العربية و المملكة العربية السعودية و دولة الأمارات العربية و دولة الكويت و المملكة الأردنية بالاضافة الى القوانين المعمول بها في دولتي البحرين وقطر كل حسب الدولة المقيم بها من الدول المذكورة اعلاه ، دخولك إلى الموقع او استخدامك للتطبيق يعني موافقتك على هذه الشروط والأحكام، سواء كنت مستخدماً مسجلاً أم لا. وبدخولك وتصفحك واستخدامك للموقع او تطبيق المنصة فإنك تكون قد قبلت، من دون قيد أو شرط، أحكام وشروط الاستخدام الواردة في هذه الوثيقة.'
                ),
                
                _buildSectionTitle('التزامات المستخدم'),
                _buildSectionContent(
                  'أنت توافق على دخول واستخدام الموقع والتطبيق لأغراض مشروعة فقط، وأنت مسؤول مسؤولية كاملة عن العلم والعمل بأي من وكل القوانين والأنظمة والقواعد والأحكام المتعلقة باستخدامك للموقع. وبمجرد دخولك إلى الموقع فإنك توافق على الامتناع عن:\n\n- استخدام الموقع من أجل ارتكاب جرم أو تشجيع الآخرين على التورط في أي تصرف قد يعد جريمة معلوماتية.\n- إدخال أو نشر أي محتويات غير قانونية تتضمن تمييز أو تشهيراً أو إساءة.\n- استخدام الموقع او التطبيق من أجل انتحال شخصيات أو أطراف أخرى.\n- استخدام الموقع لتحميل أي مادة فيها برامج تحتوي على فيروسات، أو أي شفرات حاسوبية، أو ملفات، أو برامج قد تعمل على تغيير، أو إتلاف أو إعاقة عمل الموقع أو أي جهاز أو برنامج عائد إلى أي شخص يدخل إلى الموقع.\n- تغيير أو إتلاف أو شطب او نسخ او تسجيل او تصوير أي محتوى على الموقع او التطبيق.\n- الادعاء بالارتباط مع، أو تمثيل أي فرد، شركة أو مؤسسه حكومية أو خاصه من دون أن تكون مخولاً بادعاء تلك العلاقة أو ذلك التمثيل.\n- نشر، أو بث أي إعلان، أو مادة دعائية، أو أي شكل من أشكال الترويج.\n- نشر أي مادة تتنافى أو تتعارض مع حقوق الملكية الفكرية للآخرين.\n- جمع أو تخزين المعلومات الشخصية عن الآخرين'
                ),
                
                _buildSectionTitle('بيان الخصوصية'),
                _buildSectionContent(
                  'للمعرفة، نقوم نحن باستخدام المعلومات التي تم جمعها من المستفيدين. نقوم باستمرار بتحديث أنظمتنا والبيانات لضمان أفضل خدمة ممكنه للمستفيدين. وسيقوم المركز أو من يخوله بمطالبة الجهات المختصة بالتحقيق في أي جرائم بخصوص أنظمة الحاسب الآلي والبيانات والاعتداء على خصوصية بيانات المستفيدين وملاحقة المسؤولين واتخاذ الإجراءات المناسبة ضدهم وإلزامهم بالتعويض عن أي أضرار نتجت عن ذلك.'
                ),
                
                _buildSectionTitle('التسجيل'),
                _buildSectionContent(
                  'بعض خدمات الموقع غير متاحة إلا للمستفيدين المسجلين و/أو يتاح للمستفيد طلب الدعم أو الخدمات إلكتروني<|im_start|>هل أو عبر إدخال المعلومات الشخصية. أنت توافق على أن أي معلومات تقدم لنا عبر تلك الخدمات هي معلومات كاملة ودقيقة، وأنك لن تقوم بالتسجيل، ولن تحاول دخول الموقع مستخدماً اسم شخص آخر، ولن تتبنى اسم مستفيد.'
                ),
                
                _buildSectionTitle('السرية'),
                _buildSectionContent(
                  'دون الإخلال بسياسة الخصوصية، تعتبر كل بيانات المستفيدين ومستنداتهم الإلكترونية سرية. ويرجي من المستفيدين الاحتفاظ بنسخة من أي مادة مطبوعة تصدر لها علاقة بالخدمات التي نقدمها. نحن لا نقوم ببيع أو مشاركة أو تأجير معلوماتك الشخصية لأي طرف آخر أو نقوم باستخدام عنوان بريدك الإلكتروني لإرسال رسائل غير مرغوب فيها، وأي بريد يرسل بواسطة المركز سيكون في إطار توفير الخدمات المقدمة فقط.'
                ),
                
                _buildSectionTitle('إخلاء المسئولية'),
                _buildSectionContent(
                  'الاستثناءات والقيود\nباستخدامك للموقع فإنك توافق على إعفاء تطبيق ذا فوكس اب للتعليم الألكتروني من كل المسؤوليات عن الأضرار الناشئة أو فيما يتعلق باستخدامك لهذا الموقع أو التطبيق، بتوفير المحتوى والبيانات الوصفية "كما هي" دون أي ضمان أو إقرارٍ من أي نوع، سواء أكان صريحًا أو ضمن应用查看.'
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'شروط الاستخدام',
              style: GoogleFonts.tajawal(
                fontSize: 24, 
                fontWeight: FontWeight.bold,
                color: const Color(0xff2daae2)
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'يرجى قراءة شروط الاستخدام بعناية قبل استخدام التطبيق',
              style: GoogleFonts.tajawal(
                fontSize: 16, 
                color: Colors.black
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Text(
        title,
        style: GoogleFonts.tajawal(
          fontSize: 20, 
          fontWeight: FontWeight.bold,
          color: const Color(0xff2daae2)
        ),
      ),
    );
  }
  
  Widget _buildSectionContent(String content) {
    return Text(
      content,
      style: GoogleFonts.tajawal(
        fontSize: 16, 
        color: Colors.black87,
        height: 1.5,
      ),
      textAlign: TextAlign.justify,
    );
  }
}